package com.solum.xplain.xva.proxy.calculation

import com.fasterxml.jackson.databind.ObjectMapper
import com.solum.xplain.xva.proxy.integration.S3DataStore
import com.solum.xplain.xva.proxy.messages.XvaCalculationRequest
import com.solum.xplain.xva.proxy.messages.XvaControlFile
import groovy.json.JsonSlurper
import java.nio.file.Path
import spock.lang.Specification
import spock.lang.TempDir

class XvaCalculationExecutorTest extends Specification {
  int CONTROL_FILE_MIN_LINE_COUNT = 5 // minimum number of lines expected in the control file
  S3DataStore dataStore = Mock()
  JuliaScriptInvoker juliaScriptInvoker = Mock()
  ObjectMapper mapper = new ObjectMapper()
  JsonSlurper jsonSlurper = new JsonSlurper() // not lax.

  @TempDir
  Path tempFolder
  XvaCalculationExecutor executor

  def setup() {
    executor = new XvaCalculationExecutor(dataStore, tempFolder, juliaScriptInvoker, mapper)
  }

  def "should execute XVA calculation"() {
    setup:
    def calculationFolder = tempFolder.resolve("calculationId")
    def xvaPrefix = "/tmp/xva/randomId1233546/"
    def marketFilePath = xvaPrefix + "market"
    def tradesFilePath = xvaPrefix + "trades"
    def controlFile = XvaControlFile.builder()
      .buildSurvivalProbsFromSpreads(true)
      .marketFile(marketFilePath)
      .tradeFile(tradesFilePath)
      .resultsFile("results.json")
      .outputModelFile("model.jls")
      .build()
    def controlFilePath = calculationFolder.resolve("controlFile.json")
    def resultsFilePath = calculationFolder.resolve("results.json")

    1 * dataStore.downloadFile(tradesFilePath, calculationFolder.resolve("trades.csv"))
    1 * dataStore.downloadFile(marketFilePath, calculationFolder.resolve("marketFile.json"))

    1 * juliaScriptInvoker.invokeScript(controlFilePath, resultsFilePath)

    1 * dataStore.uploadFile(xvaPrefix + "controlFile.json", controlFilePath)
    1 * dataStore.uploadFile(xvaPrefix + "results.json", resultsFilePath)
    1 * dataStore.uploadFile(xvaPrefix + "model.jls", _ as Path)

    when:
    def result = executor.processXvaControlFile(new XvaCalculationRequest("calculationId", controlFile))

    then:
    result.errorMessage == null
    result.calculationId == "calculationId"
    result.controlFile == controlFile
    // Ensure the control file is written with pretty print. (detect first and last character + parse using JsonSlurper)
    def controlFileContent = controlFilePath.toFile().text
    def lines = controlFileContent.split("\n")
    lines.first() == "{"
    lines.last() == "}"
    lines.size() > CONTROL_FILE_MIN_LINE_COUNT
    def jsonObject = jsonSlurper.parseText(controlFileContent)
    jsonObject.BuildSurvProbsFromSpreads == true
    jsonObject.MarketFile == "marketFile.json"
    jsonObject.TradeFile == "trades.csv"
    jsonObject.ResultsFile == "results.json"
    jsonObject.OutputModelFile == "model.jls"
  }

  def "should execute XVA calculation with error"() {
    setup:
    def calculationFolder = tempFolder.resolve("calculationId")
    def xvaPrefix = "/tmp/xva/randomId1233546/"
    def marketFilePath = xvaPrefix + "market"
    def tradesFilePath = xvaPrefix + "trades"
    def controlFile = XvaControlFile.builder()
      .buildSurvivalProbsFromSpreads(true)
      .marketFile(marketFilePath)
      .tradeFile(tradesFilePath)
      .resultsFile("results.json")
      .outputModelFile("model.jls")
      .build()
    def controlFilePath = calculationFolder.resolve("controlFile.json")
    def resultsFilePath = calculationFolder.resolve("results.json")

    1 * dataStore.downloadFile(tradesFilePath, calculationFolder.resolve("trades.csv"))
    1 * dataStore.downloadFile(marketFilePath, calculationFolder.resolve("marketFile.json"))

    1 * juliaScriptInvoker.invokeScript(controlFilePath, resultsFilePath) >> { throw new IllegalArgumentException("bad") }


    1 * dataStore.uploadFile(xvaPrefix + "controlFile.json", controlFilePath)
    0 * dataStore.uploadFile(xvaPrefix + "results.json", resultsFilePath)
    0 * dataStore.uploadFile(xvaPrefix + "model.jls", _ as Path)

    when:
    def result = executor.processXvaControlFile(new XvaCalculationRequest("calculationId", controlFile))

    then:
    result.errorMessage == "bad"
    result.calculationId == "calculationId"
    result.controlFile == null
    // Ensure the control file is written with pretty print. (detect first and last character + parse using JsonSlurper)
    def controlFileContent = controlFilePath.toFile().text
    def lines = controlFileContent.split("\n")
    lines.first() == "{"
    lines.last() == "}"
    lines.size() > CONTROL_FILE_MIN_LINE_COUNT
    def jsonObject = jsonSlurper.parseText(controlFileContent)
    jsonObject.BuildSurvProbsFromSpreads == true
    jsonObject.MarketFile == "marketFile.json"
    jsonObject.TradeFile == "trades.csv"
    jsonObject.ResultsFile == "results.json"
    jsonObject.OutputModelFile == "model.jls"
  }
}
