package com.solum.xplain.xva.proxy.messages;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.time.LocalDate;
import java.util.List;
import lombok.Data;

@Data
public class XvaPartyExposureFile {

  @JsonProperty("PFEStatus")
  private String pfeStatus;

  @JsonProperty("NumTrades")
  private Integer numTrades;

  @JsonProperty("NumWhatIfTrades")
  private Integer numWhatIfTrades;

  @JsonProperty("EPE")
  private List<Double> epe;

  @JsonProperty("ENE")
  private List<Double> ene;

  @JsonProperty("PFE")
  private List<Double> pfe;

  @JsonProperty("Time")
  private List<Double> time;

  @JsonProperty("AnchorDate")
  private LocalDate anchorDate;

  @JsonProperty("EPEWhatIf")
  private List<Double> epeWhatIf;

  @JsonProperty("ENEWhatIf")
  private List<Double> eneWhatIf;

  @JsonProperty("PFEWhatIf")
  private List<Double> pfeWhatIf;

  @JsonProperty("EE")
  private List<Double> ee;

  @JsonProperty("EEWhatIf")
  private List<Double> eeWhatIf;
}
