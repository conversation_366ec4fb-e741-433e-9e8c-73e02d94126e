package com.solum.xplain.xva.proxy.messages;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import lombok.Builder;
import lombok.Data;
import org.jspecify.annotations.NullMarked;
import org.jspecify.annotations.Nullable;

@Data
@Builder(toBuilder = true)
@NullMarked
@JsonInclude(JsonInclude.Include.NON_NULL)
public class XvaControlFile {

  @JsonProperty("BuildCurvesFromRates")
  private final boolean buildCurvesFromRates;

  @JsonProperty("BuildSurvProbsFromSpreads")
  private final boolean buildSurvivalProbsFromSpreads;

  @JsonProperty("CounterpartiesToProcess")
  private final @Nullable List<String> counterpartiesToProcess;

  @JsonProperty("DoPV")
  private final boolean doPV;

  @JsonProperty("DoCVA")
  private final boolean doCVA;

  @JsonProperty("DoPFE")
  private final boolean doPFE;

  @JsonProperty("PartitionByNetSet")
  private final boolean partitionByNetSet;

  @JsonProperty("PartitionByTrade")
  private final boolean partitionByTrade;

  @JsonProperty("TimeGap")
  private final double timeGap;

  @JsonProperty("PFEPercentile")
  private final double pfePercentile;

  @JsonProperty("NumSims")
  private final int numSims;

  @JsonProperty("NumSimsCVA")
  private final int numSimsCVA;

  @JsonProperty("SelfPartyName")
  private final @Nullable String selfPartyName;

  @JsonProperty("WhatIfPartyName")
  private final @Nullable String whatIfPartyName;

  @JsonProperty("SavePaths")
  private final boolean savePaths;

  @JsonProperty("OnValuationErrors")
  private final @Nullable String onValuationErrors;

  @JsonProperty("TradeFile")
  private final @Nullable String tradeFile;

  @JsonProperty("MarketFile")
  private final @Nullable String marketFile;

  @JsonProperty("ResultsFile")
  private final @Nullable String resultsFile;

  @JsonProperty("OutputModelFile")
  private final @Nullable String outputModelFile;

  @JsonProperty("AllowScalarsInResults")
  private final boolean allowScalarsInResults;

  public static XvaControlFileBuilder builderWithDefaults() {
    return XvaControlFile.builder()
        .doPV(true)
        .doCVA(true)
        .doPFE(true)
        .savePaths(true)
        .buildSurvivalProbsFromSpreads(true)
        .allowScalarsInResults(true)
        .whatIfPartyName("WHATIF")
        .selfPartyName("SELF")
        .onValuationErrors("Continue");
  }
}
