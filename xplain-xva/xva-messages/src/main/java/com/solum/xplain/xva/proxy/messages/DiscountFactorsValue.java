package com.solum.xplain.xva.proxy.messages;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.time.LocalDate;
import java.util.List;
import lombok.Data;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class DiscountFactorsValue {
  @JsonProperty("Dates")
  private final List<LocalDate> dates;

  @JsonProperty("DFs")
  private final List<Double> dfs;
}
