worker_processes 1;
user nobody nobody;
error_log /tmp/error.log;
pid /tmp/nginx.pid;

events {

  worker_connections 1024;

}

http {

    # Set an array of temp and cache files options that otherwise defaults to
    # restricted locations accessible only to root.

    client_body_temp_path /tmp/client_body;
    fastcgi_temp_path /tmp/fastcgi_temp;
    proxy_temp_path /tmp/proxy_temp;
    scgi_temp_path /tmp/scgi_temp;
    uwsgi_temp_path /tmp/uwsgi_temp;

    # mime types

    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    server {
        listen 4200;

        access_log /dev/stdout;
        error_log /tmp/error.log;

        location /xplain/api/ {
            proxy_set_header X-Forwarded-For $remote_addr;
            proxy_set_header X-Forwarded-Port $server_port;
            proxy_set_header Host $http_host;
            proxy_http_version 1.1;
            proxy_pass http://host.docker.internal:8080/xplain/api/;
            # set client body size to 2M #
            client_max_body_size 150M;
            proxy_connect_timeout       600;
            proxy_send_timeout          600;
            proxy_read_timeout          600;
            send_timeout                600;
        }

        location /xplain/ {
            alias /usr/share/nginx/html/;
            try_files $uri$args $uri$args/ $uri $uri/ /index.html =404;
        }

        location / {
            root /usr/share/nginx/html;
            try_files $uri$args $uri$args/ $uri $uri/ /index.html =404;
        }


    }

}
