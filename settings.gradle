pluginManagement {
  repositories {
    mavenCentral()
    gradlePluginPortal()
    // maven {url "https://repo.spring.io/snapshot" }
    // maven {url "https://repo.spring.io/milestone" }
  }
}

rootProject.name = 'solum-xplain-api'
include 'shared:data-grid'
include 'shared:spring-async'
include 'shared:spring-mongo'
include 'shared:strata-extension'
include 'shared:utils'
include 'shared:versions'

include 'xplain-api'
include 'xplain-api:app'
include 'xplain-api:calculation'
include 'xplain-api:calibration'
include 'xplain-api:core'
include 'xplain-api:secmaster'
include 'xplain-api:support'
include 'xplain-api:trs'
include 'xplain-api:workflow'
include 'xplain-api:xm'
include 'xplain-api:xva'

include 'xplain-valuation'
include 'xplain-valuation:app'
include 'xplain-valuation:messages'

include 'xplain-xva'
include 'xplain-xva:app'
include 'xplain-xva:xva-messages'
include 'xplain-api:generic-product'

include 'xplain-api:valuation-data'
