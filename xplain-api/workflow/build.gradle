dependencies {
  compileOnly "com.hazelcast:hazelcast:${hazelcastVersion}"
  compileOnly "org.springframework.data:spring-data-redis"
  compileOnly "io.netty:netty-common"
  implementation project(":xplain-api:core")
  implementation project(":shared:spring-async")
  implementation project(":shared:data-grid")
  implementation "org.jctools:jctools-core:${jctoolsVersion}"
  implementation 'org.springframework.boot:spring-boot-starter-data-mongodb'
  implementation 'org.springframework.boot:spring-boot-starter-cache'
  implementation "com.github.ben-manes.caffeine:caffeine"
  implementation "io.micrometer:micrometer-registry-prometheus"
  implementation "io.micrometer:micrometer-core"
  runtimeOnly 'javax.cache:cache-api'

  testImplementation "io.micrometer:micrometer-registry-prometheus"
  testImplementation "io.micrometer:micrometer-core"

  integrationTestImplementation project(":shared:data-grid")
  integrationTestImplementation project(":shared:spring-async")
  integrationTestImplementation "com.hazelcast:hazelcast:${hazelcastVersion}"
  integrationTestImplementation 'org.mongodb:bson'
  integrationTestImplementation 'org.springframework:spring-test'
  integrationTestImplementation 'org.springframework.boot:spring-boot-starter-data-mongodb'
  integrationTestImplementation 'org.springframework.boot:spring-boot-test'
  integrationTestImplementation(testFixtures(project(":xplain-api:core")))
  integrationTestImplementation "org.jctools:jctools-core:${jctoolsVersion}"
  integrationTestImplementation "io.micrometer:micrometer-registry-prometheus"
  integrationTestImplementation "io.micrometer:micrometer-core"
}

tasks.named('integrationTest', Test) {
  systemProperty 'jdk.tracePinnedThreads', 'full'
  systemProperty 'jdk.virtualThreadScheduler.parallelism', '8'
  systemProperty 'jdk.virtualThreadScheduler.maxPoolSize', '8'
  jvmArgs '-XX:StartFlightRecording=duration=120s,filename=pinning.jfr'
}
