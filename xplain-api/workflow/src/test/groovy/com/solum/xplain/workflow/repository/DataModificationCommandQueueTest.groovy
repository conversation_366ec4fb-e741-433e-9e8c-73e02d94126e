package com.solum.xplain.workflow.repository

import com.mongodb.bulk.BulkWriteResult
import com.solum.xplain.workflow.entity.CacheableWorkflowEntity
import com.solum.xplain.workflow.value.WorkflowStatus
import io.micrometer.core.instrument.simple.SimpleMeterRegistry
import jakarta.annotation.Nonnull
import org.bson.types.ObjectId
import org.springframework.cache.Cache
import org.springframework.cache.CacheManager
import org.springframework.data.mongodb.core.BulkOperations
import org.springframework.data.mongodb.core.query.Query
import spock.lang.Specification
import spock.lang.Unroll

class DataModificationCommandQueueTest extends Specification {
  BulkOperations bulkOperations = Mock()
  BulkOperations bulkOperations2 = Mock()
  CacheManager cacheManager = Mock()
  Cache cache = Mock()
  DataModificationCommandQueue commandQueue = new DataModificationCommandQueue(
  [(EntityClass): "entityClass", (EntityClass2): "entityClass2"],
  [(EntityClass): bulkOperations, (EntityClass2): bulkOperations2],
  cacheManager,
  new SimpleMeterRegistry()
  )

  class EntityClass implements CacheableWorkflowEntity {
    static boolean cacheable = true
    static WorkflowStatus status = WorkflowStatus.ACTIVE
    @Override
    ObjectId getId() {
      return ObjectId.getSmallestWithDate(new Date())
    }

    @Override
    boolean cacheable() {
      return cacheable
    }

    @Override
    WorkflowStatus getStatus() {
      return status
    }
  }

  class EntityClass2 implements CacheableWorkflowEntity {
    @Override
    ObjectId getId() {
      return ObjectId.getSmallestWithDate(new Date())
    }

    @Override
    boolean cacheable() {
      return false
    }

    @Override
    WorkflowStatus getStatus() {
      return WorkflowStatus.ACTIVE
    }
  }

  class EntityInsertCommand implements CacheSettingDataModificationCommand<EntityClass> {
    @Override
    Class<? super EntityClass> getEntity() {
      return EntityClass
    }

    @Nonnull
    @Override
    EntityClass apply(BulkOperations bulkOps) {
      def inserted = new EntityClass()
      bulkOps.insert(inserted)
      return inserted
    }
  }

  class EntityRemoveAllCommand implements CacheClearingDataModificationCommand<EntityClass> {
    @Override
    Class<? super EntityClass> getEntity() {
      return EntityClass
    }

    @Override
    void apply(BulkOperations bulkOps) {
      bulkOps.remove(new Query())
    }
  }

  def "should create bulk ops for new entity and apply change immediately (cacheable=#cacheable)"() {
    given:
    cacheManager.getCache("entityClass") >> cache
    EntityClass.cacheable = cacheable
    EntityClass.status = status

    when: "first occurence of command for entity"
    commandQueue.append(new EntityInsertCommand())
    def bulkOps = commandQueue.bulkOpsMap.get(EntityClass)

    then: "assign bulk ops and update cache"
    bulkOps != null
    (cacheable && status != WorkflowStatus.DONE ? 1 : 0) * cache.put(_ as ObjectId, _ as EntityClass)
    (cacheable && status == WorkflowStatus.DONE ? 1 : 0) * cache.evict(_ as ObjectId)

    when: "second occurence of command for entity"
    commandQueue.append(new EntityInsertCommand())

    then: "use same bulk ops and update cache again"
    commandQueue.bulkOpsMap.get(EntityClass) == bulkOps
    (cacheable && status != WorkflowStatus.DONE ? 1 : 0) * cache.put(_ as ObjectId, _ as EntityClass)
    (cacheable && status == WorkflowStatus.DONE ? 1 : 0) * cache.evict(_ as ObjectId)

    where:
    cacheable | status
    true      | WorkflowStatus.ACTIVE
    false     | WorkflowStatus.ACTIVE
    true      | WorkflowStatus.FINALIZING
    false     | WorkflowStatus.FINALIZING
    true      | WorkflowStatus.DONE
    false     | WorkflowStatus.DONE
  }

  def "should create bulk ops for bulk change and empty cache"() {
    given:
    cacheManager.getCache("entityClass") >> cache

    when: "first occurence of command for entity"
    commandQueue.append(new EntityRemoveAllCommand())

    then: "create bulk ops and update cache"
    def bulkOps = commandQueue.bulkOpsMap.get(EntityClass)
    1 * cache.clear()

    when: "second occurence of command for entity"
    commandQueue.append(new EntityRemoveAllCommand())

    then: "apply command and update cache only"
    commandQueue.bulkOpsMap.get(EntityClass) == bulkOps
    1 * cache.clear()
  }

  def "should execute all bulk ops when flushing"() {
    when:
    commandQueue.flush()

    then:
    1 * bulkOperations.execute() >> BulkWriteResult.unacknowledged()
    1 * bulkOperations2.execute() >> BulkWriteResult.unacknowledged()
  }

  @Unroll
  def "should call lockedFlush or awaitForExecutionId in tryFlush(executionId)"() {
    given:
    for (var i = 0; i < currentExecutionId; i++) {
      commandQueue.barrier.getAndIncrement()
    }
    when:
    commandQueue.tryFlush(requiredExecutionId)

    then:
    lockedFlushCalls * bulkOperations.execute() >> BulkWriteResult.unacknowledged()
    lockedFlushCalls * bulkOperations2.execute() >> BulkWriteResult.unacknowledged()
    awaitCalls * commandQueue.awaitForExecutionId(requiredExecutionId)

    where:
    requiredExecutionId | currentExecutionId || lockedFlushCalls | awaitCalls
    5                   | 4                 || 1                | 0
    3                   | 4                 || 0                | 0
    4                   | 4                 || 1                | 0
  }

  def "should wait (call awaitForExecutionId) in tryFlush(executionId) when lock is held"() {
    given:
    def queue = Spy(commandQueue)
    // Set barrier to a known state
    for (int i = 0; i < 5; i++) {
      commandQueue.barrier.getAndIncrement()
    }
    // Hold the lock to simulate contention
    commandQueue.runningLock.lock()

    when:
    // Call tryFlush(10) in a separate thread to avoid deadlock
    def thread = Thread.start {
      queue.tryFlush(10)
    }
    // Wait for the thread to finish
    thread.join(500)

    then:
    1 * queue.awaitForExecutionId(10)

    cleanup:
    commandQueue.runningLock.unlock()
  }

  @Unroll
  def "should call lockedFlush or awaitForExecutionId in tryFlush()"() {
    given:
    for (var i = 0; i < currentExecutionId; i++) {
      commandQueue.barrier.getAndIncrement()
    }
    when:
    commandQueue.tryFlush()

    then:
    lockedFlushCalls * bulkOperations.execute() >> BulkWriteResult.unacknowledged()
    lockedFlushCalls * bulkOperations2.execute() >> BulkWriteResult.unacknowledged()
    awaitCalls * commandQueue.awaitFlush()

    where:
    currentExecutionId || lockedFlushCalls | awaitCalls
    4                 || 1                | 0
    4                 || 1                | 0 // try flush always tries to flush
    4                 || 1                | 0
  }

  def "should wait (call awaitForExecutionId) in tryFlush() when lock is held"() {
    given:
    def queue = Spy(commandQueue)
    // Set barrier to a known state
    for (int i = 0; i < 5; i++) {
      commandQueue.barrier.getAndIncrement()
    }
    // Hold the lock to simulate contention
    commandQueue.runningLock.lock()

    when:
    // Call tryFlush in a separate thread to avoid deadlock
    def thread = Thread.start {
      queue.tryFlush()
    }
    // Wait for the thread to finish
    thread.join(500)

    then:
    1 * queue.awaitFlush()

    cleanup:
    commandQueue.runningLock.unlock()
  }
}
