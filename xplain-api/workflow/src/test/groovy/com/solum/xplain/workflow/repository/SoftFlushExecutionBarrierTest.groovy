package com.solum.xplain.workflow.repository


import java.util.concurrent.CountDownLatch
import java.util.concurrent.TimeUnit
import spock.lang.Specification

class SoftFlushExecutionBarrierTest extends Specification {

  def "getAndIncrement and executionId should work as expected"() {
    given:
    def barrier = new SoftFlushExecutionBarrier()

    expect:
    barrier.executionId() == 0
    barrier.getAndIncrement() == 0
    barrier.executionId() == 1
    barrier.getAndIncrement() == 1
    barrier.executionId() == 2
  }

  def "awaitExecution should block until release is called"() {
    given:
    def barrier = new SoftFlushExecutionBarrier()
    barrier.getAndIncrement() // 0 -> 1
    barrier.getAndIncrement() // 1 -> 2

    def latch = new CountDownLatch(1)
    def waitingThread = Thread.start {
      latch.countDown()
      barrier.awaitExecution(2)
    }

    // Wait for thread to start and block
    latch.await(1, TimeUnit.SECONDS)
    Thread.sleep(100)

    expect:
    waitingThread.alive

    when: "advance execution and release"
    barrier.executionId() == 2
    barrier.getAndIncrement() // 2
    barrier.release()
    waitingThread.join(1000)

    then:
    !waitingThread.alive
  }

  def "awaitExecution should block until release is called (wait 2)"() {
    given:
    def barrier = new SoftFlushExecutionBarrier()
    barrier.getAndIncrement() // (internally 1)

    def latch = new CountDownLatch(1)
    def waitingThread = Thread.start {
      latch.countDown()
      barrier.awaitExecution(2)
    }

    // Wait for thread to start and block
    latch.await(1, TimeUnit.SECONDS)
    Thread.sleep(100)

    expect:
    waitingThread.alive

    when: "advance execution and release"
    barrier.executionId() == 1
    barrier.getAndIncrement() // (internally 2)
    barrier.release()
    Thread.sleep(100)

    then:
    waitingThread.alive

    when: "advance execution and release (2)"
    barrier.executionId() == 2
    barrier.getAndIncrement() // (internally 3)
    barrier.release()
    Thread.sleep(100)

    then:
    !waitingThread.alive
  }


  def "awaitExecution returns immediately if executionId already passed"() {
    given:
    def barrier = new SoftFlushExecutionBarrier()
    barrier.getAndIncrement() // 0
    barrier.getAndIncrement() // 1

    when:
    barrier.awaitExecution(1)

    then:
    noExceptionThrown()
  }
}
