plugins {
  id "java-test-fixtures"
}

tasks.withType(GroovyCompile).configureEach {
  configure(groovyOptions.forkOptions) {
    jvmArgs = ['-Xms128m', '-Xmx2048m']
  }
}

afterEvaluate {
  tasks.withType(JavaCompile).tap {
    configureEach {
      options.compilerArgs << "-Xmaxerrs" << "8000"
    }
  }
}

dependencies {
  implementation project(":shared:spring-async")
  implementation project(":shared:spring-mongo")
  implementation project(":shared:data-grid")
  implementation project(":shared:strata-extension")
  implementation project(":shared:utils")
  implementation project(":shared:versions")
  implementation project(":xplain-valuation:messages")

  implementation 'org.springframework.boot:spring-boot-starter-security'
  implementation 'org.springframework.boot:spring-boot-starter-web'
  implementation 'org.springframework.boot:spring-boot-starter-validation'
  implementation 'org.springframework.boot:spring-boot-starter-data-mongodb'
  implementation 'org.springframework.boot:spring-boot-starter-cache'
  implementation "org.reflections:reflections:${reflectionsVersions}"
  implementation "org.springframework.kafka:spring-kafka"
  implementation "com.hazelcast:hazelcast-spring:${hazelcastVersion}"
  implementation "org.springframework.data:spring-data-redis"
  implementation "io.lettuce:lettuce-core"
  runtimeOnly 'javax.cache:cache-api'
  implementation "org.springdoc:springdoc-openapi-starter-webmvc-ui:${springDocVersion}"
  implementation "software.amazon.awssdk:s3:${awsSdkVersion}"
  implementation "one.util:streamex:${streamExVersion}"
  implementation "com.github.ben-manes.caffeine:caffeine"

  implementation "com.opengamma.strata:strata-measure:${strataVersion}"
  implementation "com.opengamma.strata:strata-loader:${strataVersion}"
  implementation "org.ejml:ejml-all:${ejmlVersion}"

  implementation 'org.springframework.boot:spring-boot-starter-log4j2'
  implementation "io.sentry:sentry-log4j2:${sentrySdkVersion}"
  implementation "org.apache.logging.log4j:log4j-layout-template-json"
  implementation "com.lmax:disruptor:${disruptorVersion}"

  implementation 'org.springframework.boot:spring-boot-starter-actuator'
  implementation "io.micrometer:micrometer-registry-prometheus"
  implementation "io.pyroscope:agent:${pyroscopeVersion}"

  implementation "org.apache.tika:tika-core:${tikaCoreVersion}"

  implementation "org.jeasy:easy-rules-core:${easyRulesVersion}"
  implementation "org.jeasy:easy-rules-support:${easyRulesVersion}"

  integrationTestImplementation testFixtures(project(":xplain-api:core"))
  integrationTestImplementation project(":shared:data-grid")
  integrationTestImplementation project(":shared:strata-extension")
  integrationTestImplementation project(":shared:versions")
  integrationTestImplementation testFixtures(project(":shared:utils"))
  integrationTestImplementation "com.opengamma.strata:strata-loader:${strataVersion}"
  integrationTestImplementation "io.atlassian.fugue:fugue:${fugueVersion}"
  integrationTestImplementation 'org.springframework.boot:spring-boot-starter-data-mongodb'
  integrationTestImplementation 'org.springframework.boot:spring-boot-starter-oauth2-resource-server'
  integrationTestImplementation 'org.springframework.boot:spring-boot-starter-security'
  integrationTestImplementation 'org.springframework.boot:spring-boot-starter-test'
  integrationTestImplementation 'org.springframework.boot:spring-boot-starter-web'
  integrationTestImplementation "org.springframework.security:spring-security-test"
  integrationTestCompileOnly "org.apache.commons:commons-lang3"

  testImplementation "org.apache.groovy:groovy-json:${groovyJsonVersion}"
  testImplementation(testFixtures(project(":shared:strata-extension")))
  testImplementation(testFixtures(project(":shared:utils")))
  testFixturesImplementation "org.apache.groovy:groovy:${groovyJsonVersion}"
  testFixturesImplementation 'org.springframework.boot:spring-boot-starter-test'
  testFixturesImplementation "com.hazelcast:hazelcast-spring:${hazelcastVersion}"
  testFixturesImplementation "org.testcontainers:mongodb"
  testFixturesImplementation "com.opengamma.strata:strata-measure:${strataVersion}"
  testFixturesImplementation "org.spockframework:spock-core:${spockVersion}"
  testFixturesImplementation "org.projectlombok:lombok:${lombokVersion}"
  testFixturesImplementation "org.mongodb:bson:5.6.0"
  testFixturesImplementation "io.atlassian.fugue:fugue:${fugueVersion}"
  testFixturesImplementation "org.apache.commons:commons-lang3"
  testFixturesImplementation 'org.springframework.boot:spring-boot-starter-data-mongodb'
  testFixturesImplementation 'org.springframework.boot:spring-boot-starter-oauth2-resource-server'
  testFixturesImplementation "org.springframework.security:spring-security-test"
  testFixturesImplementation project(":shared:strata-extension")
  testFixturesImplementation project(":shared:utils")
  testFixturesImplementation testFixtures(project(":shared:utils"))
  testFixturesImplementation testFixtures(project(":shared:versions"))
}
