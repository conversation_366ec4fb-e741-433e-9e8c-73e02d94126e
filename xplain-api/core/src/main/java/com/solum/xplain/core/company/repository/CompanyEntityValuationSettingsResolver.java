package com.solum.xplain.core.company.repository;

import static com.solum.xplain.core.company.CompanySettingsType.BESPOKE;
import static com.solum.xplain.core.company.CompanySettingsType.DEFAULT;
import static java.lang.String.format;

import com.solum.xplain.core.classifiers.sladeadlines.SlaDeadline;
import com.solum.xplain.core.common.EntityReference;
import com.solum.xplain.core.common.versions.BitemporalDate;
import com.solum.xplain.core.common.versions.State;
import com.solum.xplain.core.company.CompanySettingsType;
import com.solum.xplain.core.company.csv.IpvSettingsImportUpdateData;
import com.solum.xplain.core.company.entity.CompanyIpvSettings;
import com.solum.xplain.core.company.entity.CompanyLegalEntityIpvSettings;
import com.solum.xplain.core.company.entity.CompanyLegalEntityValuationSettings;
import com.solum.xplain.core.company.entity.CompanyValuationSettings;
import com.solum.xplain.core.company.entity.IpvValuationProviders;
import com.solum.xplain.core.company.entity.IpvValuationSettings;
import com.solum.xplain.core.company.entity.ValuationSettings;
import com.solum.xplain.core.company.entity.ValuationSettingsMarketDataGroup;
import com.solum.xplain.core.company.mapper.CompanyIpvSettingsMapper;
import com.solum.xplain.core.company.mapper.LegalEntityIpvSettingsMapper;
import com.solum.xplain.core.company.mapper.LegalEntityValuationSettingsMapper;
import com.solum.xplain.core.company.mapper.ValuationSettingsMapper;
import com.solum.xplain.core.product.ProductTypeResolver;
import com.solum.xplain.core.settings.entity.GlobalValuationSettings;
import com.solum.xplain.core.settings.repository.GlobalValuationSettingsRepository;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import org.springframework.stereotype.Component;

/**
 * Utility class for resolving company and entity valuation and ipv settings for updates via
 * imports. The class encapsulates logic that deals with updates according to settings and their
 * corresponding inherited defaults, for example entity valuation settings updates with defaults
 * coming from the entity's company. Settings returned from the public methods in this class can be
 * compared to an original settings object to determine if there have been any true updates before
 * adding a version record to the database.
 */
@Component
public class CompanyEntityValuationSettingsResolver {

  private final ValuationSettingsMapper valuationSettingsMapper;
  private final LegalEntityValuationSettingsMapper legalEntityValuationSettingsMapper;
  private final CompanyIpvSettingsMapper companyIpvSettingsMapper;
  private final LegalEntityIpvSettingsMapper legalEntityIpvSettingsMapper;
  private final ProductTypeResolver productTypeResolver;
  private final GlobalValuationSettingsRepository globalValuationSettingsRepository;

  public CompanyEntityValuationSettingsResolver(
      ProductTypeResolver productTypeResolver,
      CompanyIpvSettingsMapper companyIpvSettingsMapper,
      LegalEntityIpvSettingsMapper legalEntityIpvSettingsMapper,
      GlobalValuationSettingsRepository globalValuationSettingsRepository) {
    this.valuationSettingsMapper = ValuationSettingsMapper.INSTANCE;
    this.legalEntityValuationSettingsMapper = LegalEntityValuationSettingsMapper.INSTANCE;
    this.companyIpvSettingsMapper = companyIpvSettingsMapper;
    this.legalEntityIpvSettingsMapper = legalEntityIpvSettingsMapper;
    this.productTypeResolver = productTypeResolver;
    this.globalValuationSettingsRepository = globalValuationSettingsRepository;
  }

  /**
   * Amends the input valuation settings according to the import data. Valuation settings with
   * inherited defaults are also passed in. An example of inherited defaults is company settings,
   * when the existing settings are legal entity settings, see {@link
   * CompanyLegalEntityValuationSettings#applyDefaults}.
   *
   * @param existingSettings - valuation settings as they currently exist (entity settings or
   *     company settings)
   * @param existingSettingsWithInheritedDefaults - existingSettings with defaults applied from the
   *     parent settings (i.e., company settings with global defaults)
   * @param importData - data relevant to determining updates to settings to be applied
   * @return an updated version of the existingSettings
   */
  public <T extends ValuationSettings> T updateValuationSettings(
      T existingSettings,
      T existingSettingsWithInheritedDefaults,
      ValuationSettingsImportUpdateData importData) {

    var importCurveConfig = importData.importCurveConfig();
    var existingCurveConfig = existingSettings.getCurveConfiguration();

    var importMdg = importData.importMdg();
    var existingMdg = existingSettings.getMarketDataGroup();

    var importReportingCcy = importData.importReportingCcy();
    var existingReportingCcy = existingSettings.getReportingCurrency();

    // short circuit if import data matches existing settings
    if (!importData.isUpdate(existingCurveConfig, existingMdg, existingReportingCcy)) {
      return existingSettings;
    }

    var updatedSettings =
        withSettingsTypeUpdates(existingSettingsWithInheritedDefaults, importData);

    var defaultValuationSettings =
        globalValuationSettingsRepository.getGlobalValuationSettings(BitemporalDate.newOfNow());

    setMarketDataGroup(updatedSettings, importMdg);

    setCurveConfig(updatedSettings, importCurveConfig, defaultValuationSettings);
    setReportingCcy(updatedSettings, importReportingCcy, defaultValuationSettings);

    updatedSettings.setRecordDate(LocalDateTime.now());
    updatedSettings.setValidFrom(importData.versionForm().getValidFrom());
    updatedSettings.setComment(importData.versionForm().getComment());

    return updatedSettings;
  }

  private <T extends ValuationSettings> void setMarketDataGroup(
      T settings, EntityReference importMdg) {
    settings.setMarketDataGroup(
        importMdg == null
            ? null
            : ValuationSettingsMarketDataGroup.marketDataGroup(
                importMdg.getEntityId(), importMdg.getName()));
  }

  private <T extends ValuationSettings> void setCurveConfig(
      T settings,
      EntityReference importCurveConfig,
      GlobalValuationSettings defaultValuationSettings) {
    if (settings.getSettingsType() == BESPOKE && importCurveConfig != null) {
      settings.setCurveConfiguration(importCurveConfig);
      return;
    }
    if (settings.getSettingsType() == BESPOKE && defaultValuationSettings != null) {
      settings.setCurveConfiguration(defaultValuationSettings.getCurveConfiguration());
    }
  }

  private <T extends ValuationSettings> void setReportingCcy(
      T settings, String importReportingCcy, GlobalValuationSettings defaultValuationSettings) {
    if (settings.getSettingsType() == BESPOKE && importReportingCcy != null) {
      settings.setReportingCurrency(importReportingCcy);
      return;
    }
    if (settings.getSettingsType() == BESPOKE && defaultValuationSettings != null) {
      settings.setReportingCurrency(defaultValuationSettings.getReportingCurrency());
    }
  }

  /**
   * Amends the input ipv settings according to the import data. IPV settings with inherited
   * defaults are also passed in. An example of inherited defaults is company settings, when the
   * existing settings are legal entity settings, see {@link
   * CompanyLegalEntityIpvSettings#applyDefaults}.
   *
   * @param existingIpvSettings - valuation settings as they currently exist (entity settings or
   *     company settings)
   * @param existingIpvSettingsWithInheritedDefaults - existingIpvSettings with defaults applied
   *     from the parent settings (i.e., company settings with global defaults)
   * @param importData - data relevant to determining updates to settings to be applied
   * @return an updated version of the existingIpvSettings
   */
  public <T extends IpvValuationSettings> T updateIpvSettings(
      T existingIpvSettings,
      T existingIpvSettingsWithInheritedDefaults,
      IpvSettingsImportUpdateData importData) {

    var importSla = importData.importSla();
    var existingSla = existingIpvSettings.getSlaDeadline();

    var importIpvDataGroup = importData.importVdg();
    var existingIpvDataGroup =
        Optional.ofNullable(existingIpvSettings.getProducts())
            .map(p -> p.getFirst().getIpvDataGroup())
            .orElse(null);

    // short circuit if import data matches existing settings
    if (!importData.isUpdate(existingIpvDataGroup, existingSla)) {
      return existingIpvSettings;
    }

    var updatedSettings =
        withSettingsTypeUpdates(existingIpvSettingsWithInheritedDefaults, importData);

    setIpvDataGroup(updatedSettings, importIpvDataGroup);
    setSlaDeadline(updatedSettings, importSla);

    updatedSettings.setRecordDate(LocalDateTime.now());
    updatedSettings.setValidFrom(importData.versionForm().getValidFrom());
    updatedSettings.setComment(importData.versionForm().getComment());

    return updatedSettings;
  }

  /**
   * Handles DEFAULT -> BESPOKE and BESPOKE -> DEFAULT settings type changes
   *
   * @param existingIpvSettingsWithInheritedDefaults - existing ipv settings with inherited defaults
   * @param importData - the import data
   * @return updated settings
   * @param <T> - the type of settings
   */
  private <T extends IpvValuationSettings> T withSettingsTypeUpdates(
      T existingIpvSettingsWithInheritedDefaults, IpvSettingsImportUpdateData importData) {

    var settingsType = existingIpvSettingsWithInheritedDefaults.getSettingsType();

    if (importData.allNull()) {
      if (settingsType == BESPOKE) {
        return fromBespokeToDefault(existingIpvSettingsWithInheritedDefaults);
      }
    } else {
      if (settingsType == DEFAULT) {
        return fromDefaultToBespoke(existingIpvSettingsWithInheritedDefaults);
      }
    }

    // no changes to settings type
    return existingIpvSettingsWithInheritedDefaults;
  }

  /**
   * Handles DEFAULT -> BESPOKE and BESPOKE -> DEFAULT settings type changes
   *
   * @param existingValuationSettingsWithInheritedDefaults - existing settings with inherited
   *     defaults
   * @param importData - the import data
   * @return updated settings
   * @param <T> - the type of settings
   */
  private <T extends ValuationSettings> T withSettingsTypeUpdates(
      T existingValuationSettingsWithInheritedDefaults,
      ValuationSettingsImportUpdateData importData) {

    var settingsType = existingValuationSettingsWithInheritedDefaults.getSettingsType();

    // No custom values provided, switch settings to default
    if (settingsType == BESPOKE
        && importData.importCurveConfig() == null
        && importData.importReportingCcy() == null) {
      return fromBespokeToDefault(existingValuationSettingsWithInheritedDefaults);
    }

    // At least 1 custom value provided, change settings to be bespoke
    if (importData.importCurveConfig() != null || importData.importReportingCcy() != null) {
      return fromDefaultToBespoke(existingValuationSettingsWithInheritedDefaults);
    }

    // no changes to settings type
    return existingValuationSettingsWithInheritedDefaults;
  }

  private <T extends ValuationSettings> T fromBespokeToDefault(T settings) {
    if (settings instanceof CompanyValuationSettings companyValuationSettings) {
      return (T) CompanyValuationSettings.newOf(companyValuationSettings.getEntityId());
    } else if (settings
        instanceof CompanyLegalEntityValuationSettings companyLegalEntityValuationSettings) {
      return (T)
          CompanyLegalEntityValuationSettings.newOf(
              companyLegalEntityValuationSettings.getCompanyId(),
              companyLegalEntityValuationSettings.getEntityId());
    }
    throw new IllegalArgumentException(
        format("Unexpected instance of ValuationSettings: %s", settings.getClass().getName()));
  }

  private <T extends ValuationSettings> T fromDefaultToBespoke(T settings) {
    if (settings instanceof CompanyValuationSettings companyValuationSettings) {
      var newSettings =
          valuationSettingsMapper.fromDefaultToBespoke(companyValuationSettings, State.ACTIVE);
      newSettings.setSettingsType(BESPOKE);
      return (T) newSettings;
    } else if (settings
        instanceof CompanyLegalEntityValuationSettings companyLegalEntityValuationSettings) {
      var newSettings =
          legalEntityValuationSettingsMapper.fromDefaultToBespoke(
              companyLegalEntityValuationSettings, State.ACTIVE);
      newSettings.setSettingsType(BESPOKE);
      return (T) newSettings;
    }
    throw new IllegalArgumentException(
        format("Unexpected instance of ValuationSettings: %s", settings.getClass().getName()));
  }

  private <T extends IpvValuationSettings> T fromBespokeToDefault(T settings) {
    if (settings instanceof CompanyIpvSettings companyIpvSettings) {
      return (T) CompanyIpvSettings.newOf(companyIpvSettings.getEntityId());
    } else if (settings instanceof CompanyLegalEntityIpvSettings entityIpvValuationSettings) {
      return (T)
          CompanyLegalEntityIpvSettings.newOf(
              entityIpvValuationSettings.getCompanyId(), entityIpvValuationSettings.getEntityId());
    }
    throw new IllegalArgumentException(
        format("Unexpected instance of IpvValuationSettings: %s", settings.getClass().getName()));
  }

  private <T extends IpvValuationSettings> T fromDefaultToBespoke(T settings) {
    if (settings instanceof CompanyIpvSettings companyIpvSettings) {
      var newSettings =
          companyIpvSettingsMapper.fromDefaultToBespoke(companyIpvSettings, State.ACTIVE);
      newSettings.setSettingsType(BESPOKE);
      return (T) newSettings;
    } else if (settings instanceof CompanyLegalEntityIpvSettings entityIpvValuationSettings) {
      var newSettings =
          legalEntityIpvSettingsMapper.fromDefaultToBespoke(
              entityIpvValuationSettings, State.ACTIVE);
      newSettings.setSettingsType(BESPOKE);
      return (T) newSettings;
    }
    throw new IllegalArgumentException(
        format("Unexpected instance of IpvValuationSettings: %s", settings.getClass().getName()));
  }

  private <T extends IpvValuationSettings> void setSlaDeadline(T ipvSettings, String importSla) {
    if (ipvSettings.getSettingsType() == CompanySettingsType.BESPOKE) {
      if (importSla != null) {
        ipvSettings.setSlaDeadline(SlaDeadline.valueOf(importSla));
      } else {
        ipvSettings.setSlaDeadline(SlaDeadline.OTHER);
      }
    }

    // default fallback
    if (ipvSettings.getSlaDeadline() == null) {
      ipvSettings.setSlaDeadline(SlaDeadline.OTHER);
    }
  }

  private <T extends IpvValuationSettings> void setIpvDataGroup(
      T ipvSettings, EntityReference importVdg) {
    if (ipvSettings.getSettingsType() == CompanySettingsType.BESPOKE) {
      setIpvDataGroup(ipvSettings, ipvSettings.getProducts(), importVdg);
    }
  }

  private <T extends IpvValuationSettings> void setIpvDataGroup(
      T ipvSettings, List<IpvValuationProviders> existingProducts, EntityReference vdg) {
    if (existingProducts == null) {
      ipvSettings.setAllProductsForVdg(productTypeResolver.values(), vdg);
    } else {
      existingProducts.forEach(ep -> ep.setIpvDataGroup(vdg));
    }
  }
}
