package com.solum.xplain.core.curvegroup.curvecredit.entity;

import static com.opengamma.strata.product.credit.type.CdsQuoteConvention.PAR_SPREAD;
import static com.opengamma.strata.product.credit.type.CdsQuoteConvention.POINTS_UPFRONT;
import static com.opengamma.strata.product.credit.type.CdsQuoteConvention.QUOTED_SPREAD;
import static com.solum.xplain.core.classifiers.PermissibleConventions.CDS_CONVENTIONS;
import static com.solum.xplain.core.common.CollectionUtils.nullSafeIsEqualCollection;
import static com.solum.xplain.core.curvemarket.node.NonExpiredCreditNodeFilter.ofNonExpiredZeroMonthCdsNodes;
import static com.solum.xplain.core.error.Error.CALIBRATION_ERROR;
import static com.solum.xplain.core.utils.ComparisonUtils.isEqualBigDecimal;
import static java.lang.String.format;
import static java.util.Comparator.comparing;
import static java.util.Optional.ofNullable;
import static java.util.stream.Collectors.toList;
import static java.util.stream.Collectors.toUnmodifiableList;
import static org.apache.commons.collections4.CollectionUtils.emptyIfNull;
import static org.apache.commons.lang3.StringUtils.equalsAny;
import static org.slf4j.LoggerFactory.getLogger;
import static org.springframework.util.CollectionUtils.isEmpty;

import com.google.common.collect.ImmutableList;
import com.google.common.collect.Iterables;
import com.google.common.collect.Ordering;
import com.opengamma.strata.basics.ReferenceData;
import com.opengamma.strata.basics.StandardId;
import com.opengamma.strata.basics.currency.Currency;
import com.opengamma.strata.basics.date.DayCount;
import com.opengamma.strata.basics.date.DayCounts;
import com.opengamma.strata.collect.Messages;
import com.opengamma.strata.market.curve.CurveName;
import com.opengamma.strata.market.curve.IsdaCreditCurveDefinition;
import com.opengamma.strata.market.curve.IsdaCreditCurveNode;
import com.opengamma.strata.product.credit.type.CdsConvention;
import com.solum.xplain.core.common.value.ChartPoint;
import com.solum.xplain.core.common.versions.State;
import com.solum.xplain.core.common.versions.VersionedNamedEntity;
import com.solum.xplain.core.curvegroup.CurveGroupEntry;
import com.solum.xplain.core.curvegroup.curvecredit.CreditCurveMdkNameUtils;
import com.solum.xplain.core.curvegroup.curvecredit.classifiers.CreditCurveType;
import com.solum.xplain.core.curvegroup.instrument.InstrumentDefinition;
import com.solum.xplain.core.curvemarket.node.NodeInstrumentWrapper;
import com.solum.xplain.core.curvemarket.node.ValidNodesFilter;
import com.solum.xplain.core.error.Error;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.utils.DayCountUtils;
import com.solum.xplain.extensions.enums.CreditDocClause;
import com.solum.xplain.extensions.enums.CreditSector;
import com.solum.xplain.extensions.enums.CreditSeniority;
import com.solum.xplain.extensions.utils.StandardIdUtils;
import io.atlassian.fugue.Either;
import io.atlassian.fugue.Eithers;
import java.math.BigDecimal;
import java.time.Clock;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Consumer;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldNameConstants;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.springframework.data.mongodb.core.mapping.Document;

@Data
@Document(collection = CreditCurve.CREDIT_CURVE_COLLECTION)
@FieldNameConstants
public class CreditCurve extends VersionedNamedEntity implements CurveGroupEntry {

  public static final String CREDIT_CURVE_COLLECTION = "creditCurve";
  private static final Logger LOG = getLogger(CreditCurve.class);
  private static final DayCount DEFAULT_DAY_COUNT = DayCounts.ACT_365F;
  private static final String INVALID_NODES_COUNT =
      "Credit curve {} must have at least 1 node with data for the " + "selected valuation date";
  private static final String UNSUPPORTED_CONVENTION_TEMPLATE =
      "Unsupported credit quote convention specified : %s";
  private static Clock CLOCK = Clock.systemDefaultZone();

  private String curveGroupId;
  private CreditCurveType curveType;

  // CDS Index fields
  private BigDecimal creditIndexFactor;
  private Integer creditIndexSeries;
  private Integer creditIndexVersion;
  private LocalDate creditIndexStartDate;

  // Credit Index Tranche fields
  private String creditIndexTranche;

  // CDS Fields
  private String corpTicker;
  private CreditSeniority seniority;

  private String reference;
  private String entityLongName;
  private double recoveryRate;
  private String currency;
  private String quoteConvention;
  private Double fixedCoupon;
  private CreditSector sector;
  private CreditDocClause docClause;

  private List<CreditCurveIndexNode> indexNodes = new ArrayList<>();
  private List<CreditCurveCdsNode> cdsNodes = new ArrayList<>();
  private List<CreditCurveFundingNode> fundingNodes = new ArrayList<>();
  private List<ChartPoint> chartPoints = new ArrayList<>();

  public static CreditCurve newOf() {
    var curve = new CreditCurve();
    curve.setEntityId(ObjectId.get().toString());
    curve.setState(State.ACTIVE);
    curve.setRecordDate(LocalDateTime.now(CLOCK));
    return curve;
  }

  /** For unit testing. */
  static void configureClock(Clock clock) {
    CLOCK = clock;
  }

  public StandardId legalEntityStandardId() {
    return StandardIdUtils.curveIdStandardId(getName());
  }

  public CreditCurve orderNodes(ReferenceData referenceData) {
    var sortDate = LocalDate.now(CLOCK);
    if (cdsNodes != null) {
      cdsNodes =
          Ordering.<CreditCurveCdsNode>from(
                  comparing(
                      n ->
                          DayCountUtils.yearFraction(
                              sortDate, n.date(this, sortDate, referenceData), DEFAULT_DAY_COUNT)))
              .sortedCopy(cdsNodes);
    }
    if (fundingNodes != null) {
      fundingNodes =
          Ordering.<CreditCurveFundingNode>from(comparing(n -> n.date(sortDate)))
              .sortedCopy(fundingNodes);
    }

    if (indexNodes != null) {
      indexNodes =
          Ordering.<CreditCurveIndexNode>from(comparing(n -> n.date(this, sortDate, referenceData)))
              .sortedCopy(indexNodes);
    }
    return this;
  }

  public Either<ErrorItem, IsdaCreditCurveDefinition> curveDefinition(
      LocalDate valuationDate,
      ValidNodesFilter nodesFilter,
      boolean computeJacobian,
      ReferenceData referenceData,
      Consumer<List<ErrorItem>> warningsConsumer) {
    try {
      return filteredNodes(nodesFilter, valuationDate, referenceData, warningsConsumer).stream()
          .map(cdsCurveNode -> cdsCurveNode.isdaNode(this, referenceData))
          .collect(Collectors.collectingAndThen(toList(), Eithers::sequenceRight))
          .flatMap(this::validateSize)
          .map(
              nodes ->
                  IsdaCreditCurveDefinition.of(
                      CurveName.of(getName()),
                      Currency.of(currency),
                      valuationDate,
                      DEFAULT_DAY_COUNT,
                      ImmutableList.copyOf(nodes),
                      computeJacobian,
                      false))
          .map(IsdaCreditCurveDefinition.class::cast);
    } catch (RuntimeException ex) {
      LOG.debug(ex.getMessage(), ex);
      return Either.left(new ErrorItem(Error.PARSING_ERROR, ex.getMessage()));
    }
  }

  private Either<ErrorItem, Iterable<IsdaCreditCurveNode>> validateSize(
      Iterable<IsdaCreditCurveNode> nodes) {
    return Eithers.cond(
        !Iterables.isEmpty(nodes),
        CALIBRATION_ERROR.entity(Messages.format(INVALID_NODES_COUNT, getName())),
        nodes);
  }

  public Optional<CreditCurveFundingNode> fundingNodeByTenorOrFirst(String tenor) {
    return ofNullable(fundingNodes).stream()
        .flatMap(Collection::stream)
        .filter(n -> tenor.equals(n.getTenor()))
        .findFirst()
        .or(() -> ofNullable(isEmpty(fundingNodes) ? null : fundingNodes.getFirst()));
  }

  public CdsConvention cdsConvention() {
    return CDS_CONVENTIONS.get(Currency.of(currency));
  }

  @Override
  public List<InstrumentDefinition> allInstruments() {
    return ImmutableList.<InstrumentDefinition>builder()
        .addAll(cdsNodeInstruments())
        .addAll(recoveryRateNodeInstruments())
        .addAll(fundingNodeInstruments())
        .addAll(indexNodeInstruments())
        .build();
  }

  public List<InstrumentDefinition> cdsNodeInstruments() {
    return Stream.ofNullable(cdsNodes)
        .flatMap(Collection::stream)
        .map(n -> n.instrument(this))
        .toList();
  }

  public List<InstrumentDefinition> recoveryRateNodeInstruments() {
    return Stream.ofNullable(cdsNodes)
        .flatMap(Collection::stream)
        .map(n -> n.recoveryRateInstrument(this))
        .toList();
  }

  public List<InstrumentDefinition> indexNodeInstruments() {
    return Stream.ofNullable(indexNodes)
        .flatMap(Collection::stream)
        .map(n -> n.instrument(this))
        .toList();
  }

  public List<InstrumentDefinition> fundingNodeInstruments() {
    var mdkNamePrefix = CreditCurveMdkNameUtils.fundingNodeMdkNamePrefix(this);
    return Stream.ofNullable(fundingNodes)
        .flatMap(Collection::stream)
        .map(n -> n.instrument(getName(), reference, sector, currency, mdkNamePrefix))
        .toList();
  }

  private List<? extends CreditCurveIsdaNode> filteredNodes(
      ValidNodesFilter filter,
      LocalDate valuationDate,
      ReferenceData referenceData,
      Consumer<List<ErrorItem>> warningsConsumer) {
    var validNodes = filterNodes(nodes().stream().toList(), filter);
    var nonExpiredFilter =
        ofNonExpiredZeroMonthCdsNodes(valuationDate, referenceData, this, warningsConsumer);
    return filterNodes(validNodes, nonExpiredFilter);
  }

  private List<? extends CreditCurveIsdaNode> filterNodes(
      List<? extends CreditCurveIsdaNode> nodes, ValidNodesFilter validNodesFilter) {
    return nodes.stream()
        .map(n -> NodeInstrumentWrapper.of(n, n.instrument(this)))
        .collect(Collectors.collectingAndThen(toUnmodifiableList(), validNodesFilter::filterNodes));
  }

  public String nodeType() {
    if (StringUtils.equals(POINTS_UPFRONT.name(), quoteConvention)) {
      return "UF";
    } else if (equalsAny(quoteConvention, QUOTED_SPREAD.name(), PAR_SPREAD.name())) {
      return "SPREAD";
    } else {
      throw new IllegalArgumentException(format(UNSUPPORTED_CONVENTION_TEMPLATE, quoteConvention));
    }
  }

  private Collection<? extends CreditCurveIsdaNode> nodes() {
    return switch (curveType) {
      case CDS -> emptyIfNull(cdsNodes);
      case CREDIT_INDEX, CREDIT_INDEX_TRANCHE -> emptyIfNull(indexNodes);
    };
  }

  @Override
  public boolean valueEquals(Object object) {
    CreditCurve entity = (CreditCurve) object;
    return super.valueEquals(entity)
        && Objects.equals(this.curveGroupId, entity.curveGroupId)
        && Objects.equals(this.curveType, entity.curveType)
        && isEqualBigDecimal(this.creditIndexFactor, entity.creditIndexFactor)
        && Objects.equals(this.creditIndexSeries, entity.creditIndexSeries)
        && Objects.equals(this.creditIndexVersion, entity.creditIndexVersion)
        && Objects.equals(this.creditIndexStartDate, entity.creditIndexStartDate)
        && Objects.equals(this.reference, entity.reference)
        && Objects.equals(this.corpTicker, entity.corpTicker)
        && Objects.equals(this.entityLongName, entity.entityLongName)
        && Objects.equals(this.seniority, entity.seniority)
        && Objects.equals(this.recoveryRate, entity.recoveryRate)
        && Objects.equals(this.currency, entity.currency)
        && Objects.equals(this.quoteConvention, entity.quoteConvention)
        && Objects.equals(this.fixedCoupon, entity.fixedCoupon)
        && Objects.equals(this.docClause, entity.docClause)
        && Objects.equals(this.sector, entity.sector)
        && nullSafeIsEqualCollection(this.cdsNodes, entity.cdsNodes)
        && nullSafeIsEqualCollection(this.indexNodes, entity.indexNodes)
        && nullSafeIsEqualCollection(this.fundingNodes, entity.fundingNodes)
        && nullSafeIsEqualCollection(this.chartPoints, entity.chartPoints);
  }

  @EqualsAndHashCode.Include(replaces = "creditIndexFactor")
  private BigDecimal normalisedCreditIndexFactor() {
    return creditIndexFactor == null ? null : creditIndexFactor.stripTrailingZeros();
  }
}
