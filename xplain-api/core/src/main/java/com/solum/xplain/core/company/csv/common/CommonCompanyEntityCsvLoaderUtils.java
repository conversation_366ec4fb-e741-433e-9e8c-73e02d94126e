package com.solum.xplain.core.company.csv.common;

import static com.solum.xplain.core.common.csv.CsvLoaderUtils.getFieldValue;
import static com.solum.xplain.core.common.csv.CsvLoaderUtils.parseIdentifier;
import static com.solum.xplain.core.common.csv.CsvLoaderUtils.rowParsingError;
import static com.solum.xplain.core.common.csv.CsvLoaderUtils.validateValue;
import static com.solum.xplain.core.company.csv.CommonCompanyCsvFields.COMPANY_EXTERNAL_ID;
import static com.solum.xplain.core.company.csv.common.CommonCompanyEntityCsvFields.ALLOW_ALL_TEAMS;
import static com.solum.xplain.core.company.csv.common.CommonCompanyEntityCsvFields.CURVE_CONFIGURATION;
import static com.solum.xplain.core.company.csv.common.CommonCompanyEntityCsvFields.MARKET_DATA_GROUP;
import static com.solum.xplain.core.company.csv.common.CommonCompanyEntityCsvFields.SLA_DEADLINE;
import static com.solum.xplain.core.company.csv.common.CommonCompanyEntityCsvFields.TEAMS;
import static com.solum.xplain.core.company.csv.common.CommonCompanyEntityCsvFields.VALUATION_DATA_GROUP;
import static io.atlassian.fugue.Either.right;

import com.opengamma.strata.collect.io.CsvRow;
import com.solum.xplain.core.common.EntityReference;
import com.solum.xplain.core.common.csv.CsvLoaderUtils;
import com.solum.xplain.core.common.value.AllowedTeamsForm;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.ipv.group.value.IpvDataGroupCondensedCompanyView;
import com.solum.xplain.core.market.value.MarketDataGroupView;
import io.atlassian.fugue.Checked;
import io.atlassian.fugue.Either;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Optional;
import java.util.Set;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;

/** Utility class for common methods for parsing company and entity csv files. */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class CommonCompanyEntityCsvLoaderUtils {

  public static Either<ErrorItem, String> parseExternalCompanyId(CsvRow row) {
    return Checked.now(() -> parseIdentifier(row, COMPANY_EXTERNAL_ID))
        .toEither()
        .leftMap(e -> rowParsingError(row, e));
  }

  public static Either<ErrorItem, Optional<EntityReference>> parseCurveConfiguration(
      CsvRow row, Map<String, String> curveConfigurationsMap) {
    return getFieldValue(
        row, CURVE_CONFIGURATION, false, v -> parseCurveConfiguration(v, curveConfigurationsMap));
  }

  public static Either<ErrorItem, Optional<EntityReference>> parseValuationDataGroup(
      CsvRow row,
      String companyExternalId,
      Map<String, IpvDataGroupCondensedCompanyView> ipvDataGroupsMap) {
    return getFieldValue(
        row,
        VALUATION_DATA_GROUP,
        false,
        v -> parseValuationDataGroup(v, companyExternalId, ipvDataGroupsMap));
  }

  public static Either<ErrorItem, Optional<EntityReference>> parseMarketDataGroup(
      CsvRow row, String companyExternalId, Map<String, MarketDataGroupView> marketDataGroupsMap) {
    return getFieldValue(
        row,
        MARKET_DATA_GROUP,
        false,
        v -> parseMarketDataGroup(v, companyExternalId, marketDataGroupsMap));
  }

  public static Either<ErrorItem, Optional<String>> parseSlaDeadline(CsvRow row) {
    return getFieldValue(
        row, SLA_DEADLINE, false, CommonCompanyEntityCsvLoaderUtils::parseSlaDeadline);
  }

  private static Optional<String> parseSlaDeadline(String value) {
    if (value.isEmpty()) {
      return Optional.empty();
    }
    return Optional.of(CsvLoaderUtils.parseSlaDeadline(value));
  }

  public static Optional<String> parseReportingCcy(String value) {
    if (value.isEmpty()) {
      return Optional.empty();
    }
    return Optional.of(CsvLoaderUtils.validateCurrency(value));
  }

  private static Optional<EntityReference> parseMarketDataGroup(
      String mdgName,
      String companyExternalId,
      Map<String, MarketDataGroupView> marketDataGroupsMap) {
    if (mdgName.isEmpty()) {
      return Optional.empty();
    }

    var permissibleGroupsForCompany =
        marketDataGroupsMap.entrySet().stream()
            .filter(
                e -> {
                  var mdg = e.getValue();
                  return mdg.isAllowAllCompanies()
                      || mdg.getCompanyExternalIds().stream()
                          .anyMatch(n -> n.equalsIgnoreCase(companyExternalId));
                })
            .map(Entry::getKey)
            .toList();

    String name = validateValue(mdgName, permissibleGroupsForCompany);
    var marketDataGroup = marketDataGroupsMap.get(name);

    return Optional.of(EntityReference.newOf(marketDataGroup.getId(), name));
  }

  private static Optional<EntityReference> parseValuationDataGroup(
      String ipvGroupName,
      String companyExternalId,
      Map<String, IpvDataGroupCondensedCompanyView> ipvDataGroupsMap) {
    if (ipvGroupName.isEmpty()) {
      return Optional.empty();
    }

    var permissibleGroupsForCompany =
        ipvDataGroupsMap.entrySet().stream()
            .filter(
                e -> {
                  var ipvGroup = e.getValue();
                  return ipvGroup.isAllowAllCompanies()
                      || ipvGroup.getCompanies().stream()
                          .anyMatch(
                              ref ->
                                  ref.getExternalCompanyId().equalsIgnoreCase(companyExternalId));
                })
            .map(Entry::getKey)
            .toList();

    // validate ipv data group exists (for user)
    String name = validateValue(ipvGroupName, permissibleGroupsForCompany);
    var ipvDataGroup = ipvDataGroupsMap.get(name);

    return Optional.of(EntityReference.newOf(ipvDataGroup.getId(), name));
  }

  private static Optional<EntityReference> parseCurveConfiguration(
      String value, Map<String, String> curveConfigurationsMap) {
    if (value.isEmpty()) {
      return Optional.empty();
    }

    String curveConfigName = validateValue(value, curveConfigurationsMap::keySet);
    return Optional.of(
        EntityReference.newOf(curveConfigurationsMap.get(curveConfigName), curveConfigName));
  }

  public static Either<ErrorItem, AllowedTeamsForm> parseTeams(
      CsvRow row, Map<String, String> teamsMap, Set<String> userTeamIds) {
    boolean allowAllTeams =
        row.findValue(ALLOW_ALL_TEAMS).map(CsvLoaderUtils::parseBoolean).orElse(false);
    if (allowAllTeams) {
      return right(new AllowedTeamsForm(true, null));
    }
    return CsvLoaderUtils.getFieldValue(row, TEAMS, n -> parseTeamNames(n, teamsMap, userTeamIds))
        .map(ids -> new AllowedTeamsForm(false, ids));
  }

  private static List<String> parseTeamNames(
      String namesString, Map<String, String> teamsMap, Set<String> userTeamIds) {
    return Arrays.stream(namesString.split("\\|"))
        .map(name -> validateValue(name, teamsMap::keySet))
        .map(teamsMap::get)
        .filter(userTeamIds::contains)
        .toList();
  }
}
