package com.solum.xplain.core.company.entity.csv;

import static com.solum.xplain.core.common.csv.CsvLoaderUtils.getFieldValue;
import static com.solum.xplain.core.common.csv.CsvLoaderUtils.parseIdentifier;
import static com.solum.xplain.core.common.csv.CsvLoaderUtils.rowParsingError;
import static com.solum.xplain.core.common.csv.CsvLoaderUtils.validateValue;
import static com.solum.xplain.core.company.csv.CommonCompanyCsvFields.COMPANY_EXTERNAL_ID;
import static com.solum.xplain.core.company.csv.common.CommonCompanyEntityCsvFields.ALLOW_ALL_TEAMS;
import static com.solum.xplain.core.company.csv.common.CommonCompanyEntityCsvFields.REPORTING_CCY;
import static com.solum.xplain.core.company.csv.common.CommonCompanyEntityCsvFields.TEAMS;
import static com.solum.xplain.core.company.csv.common.CommonCompanyEntityCsvLoaderUtils.parseCurveConfiguration;
import static com.solum.xplain.core.company.csv.common.CommonCompanyEntityCsvLoaderUtils.parseMarketDataGroup;
import static com.solum.xplain.core.company.csv.common.CommonCompanyEntityCsvLoaderUtils.parseSlaDeadline;
import static com.solum.xplain.core.company.csv.common.CommonCompanyEntityCsvLoaderUtils.parseTeams;
import static com.solum.xplain.core.company.csv.common.CommonCompanyEntityCsvLoaderUtils.parseValuationDataGroup;
import static com.solum.xplain.core.company.entity.csv.CompanyLegalEntityCsvFields.ENTITY_DESCRIPTION;
import static com.solum.xplain.core.company.entity.csv.CompanyLegalEntityCsvFields.ENTITY_EXTERNAL_ID;
import static com.solum.xplain.core.company.entity.csv.CompanyLegalEntityCsvFields.ENTITY_NAME;

import com.opengamma.strata.collect.io.CsvRow;
import com.solum.xplain.core.common.csv.CsvParserResultBuilder;
import com.solum.xplain.core.common.csv.GenericCsvLoader;
import com.solum.xplain.core.common.csv.ParsingMode;
import com.solum.xplain.core.company.csv.common.CommonCompanyEntityCsvLoaderUtils;
import com.solum.xplain.core.company.entity.CompanyReference;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.ipv.group.value.IpvDataGroupCondensedCompanyView;
import com.solum.xplain.core.market.value.MarketDataGroupView;
import io.atlassian.fugue.Checked;
import io.atlassian.fugue.Either;
import io.atlassian.fugue.extensions.step.Steps;
import java.util.List;
import java.util.Map;
import java.util.Set;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode
@AllArgsConstructor(staticName = "newOf")
public class CompanyLegalEntityCsvLoader
    extends GenericCsvLoader<CompanyLegalEntityCsvForm, CompanyLegalEntityUniqueKey> {

  private final Map<String, CompanyReference> existingCompaniesByExternalId;
  private final Map<String, MarketDataGroupView> marketDataGroupsMap;
  private final Map<String, IpvDataGroupCondensedCompanyView> ipvDataGroupsMap;
  private final Map<String, String> curveConfigurationsMap;
  private final Map<String, String> teamsMap;
  private final Set<String> userTeamIds;

  @Override
  protected CsvParserResultBuilder<CompanyLegalEntityCsvForm, CompanyLegalEntityUniqueKey>
      createResult(ParsingMode parsingMode) {
    return new CsvParserResultBuilder<>(
        CompanyLegalEntityUniqueKey::fromForm,
        CompanyLegalEntityUniqueKey::getIdentifier,
        parsingMode.failOnError());
  }

  @Override
  protected List<String> getFileHeaders() {
    return List.of(COMPANY_EXTERNAL_ID, ENTITY_EXTERNAL_ID, TEAMS, ALLOW_ALL_TEAMS);
  }

  @Override
  protected Either<ErrorItem, CompanyLegalEntityCsvForm> parseLine(CsvRow row) {
    return Steps.begin(parseCompanyEntity(row))
        .then(
            cEId ->
                parseValuationDataGroup(
                    row, cEId.companyReference().getExternalCompanyId(), ipvDataGroupsMap))
        .then(
            (cEId, vdg) ->
                parseMarketDataGroup(
                    row, cEId.companyReference().getExternalCompanyId(), marketDataGroupsMap))
        .then(() -> parseSlaDeadline(row))
        .then(() -> parseCurveConfiguration(row, curveConfigurationsMap))
        .then(() -> parseTeams(row, teamsMap, userTeamIds))
        .yield(
            (cEId, vdg, mdg, sla, cconfig, teams) -> {
              var compExtId = cEId.companyReference().getExternalCompanyId();
              var compId = cEId.companyReference().getEntityId();
              var entId = cEId.entityExternalId();

              var entityName = row.findValue(ENTITY_NAME).orElse(entId);
              var description = row.findValue(ENTITY_DESCRIPTION).orElse(null);
              var reportingCcy =
                  row.findValue(REPORTING_CCY)
                      .flatMap(CommonCompanyEntityCsvLoaderUtils::parseReportingCcy)
                      .orElse(null);

              var csvData =
                  new CompanyEntityCsvData(
                      compExtId,
                      compId,
                      entId,
                      entityName,
                      description,
                      sla.orElse(null),
                      vdg.orElse(null),
                      mdg.orElse(null),
                      cconfig.orElse(null),
                      reportingCcy,
                      teams);

              return CompanyLegalEntityCsvForm.of(csvData);
            });
  }

  private Either<ErrorItem, CompanyEntityPair> parseCompanyEntity(CsvRow row) {
    var companyReferenceEither =
        getFieldValue(row, COMPANY_EXTERNAL_ID, this::validateCompanyExternalId)
            .map(existingCompaniesByExternalId::get);

    return companyReferenceEither.flatMap(
        c -> {
          var entityExternalIdEither = parseExternalEntityId(row);
          return entityExternalIdEither.map(e -> new CompanyEntityPair(c, e));
        });
  }

  private Either<ErrorItem, String> parseExternalEntityId(CsvRow row) {
    return Checked.now(() -> parseIdentifier(row, ENTITY_EXTERNAL_ID))
        .toEither()
        .leftMap(e -> rowParsingError(row, e));
  }

  private String validateCompanyExternalId(String companyExternalId) {
    return validateValue(companyExternalId, existingCompaniesByExternalId.keySet());
  }

  private record CompanyEntityPair(CompanyReference companyReference, String entityExternalId) {}
}
