package com.solum.xplain.core.company.repository;

import com.solum.xplain.core.common.EntityReference;
import com.solum.xplain.core.common.value.NewVersionFormV2;
import com.solum.xplain.core.company.entity.ValuationSettingsMarketDataGroup;
import java.util.Objects;

public record ValuationSettingsImportUpdateData(
    EntityReference importCurveConfig,
    EntityReference importMdg,
    String importReportingCcy,
    NewVersionFormV2 versionForm) {

  public boolean isUpdate(
      EntityReference existingCurveConfig,
      ValuationSettingsMarketDataGroup existingMdg,
      String existingReportingCcy) {
    if (!Objects.equals(importCurveConfig, existingCurveConfig)) {
      return true;
    }

    if (!Objects.equals(existingReportingCcy, importReportingCcy)) {
      return true;
    }

    if (existingMdg != null) {
      var existingMdgRef = existingMdg.toEntityReference();
      return !Objects.equals(importMdg, existingMdgRef);
    }

    return importMdg != null;
  }
}
