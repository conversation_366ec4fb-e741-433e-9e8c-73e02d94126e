package com.solum.xplain.core.curvegroup.curvecredit.value;

import com.opengamma.strata.basics.ReferenceData;
import com.solum.xplain.core.common.value.ChartPoint;
import com.solum.xplain.core.curvegroup.curvecredit.classifiers.CreditCurveNodeType;
import com.solum.xplain.core.curvegroup.curvecredit.entity.CreditCurve;
import com.solum.xplain.core.curvegroup.curvecredit.entity.CreditCurveIsdaNode;
import com.solum.xplain.core.curvemarket.marketvalue.CalculationMarketValueFullView;
import com.solum.xplain.core.curvemarket.pricetype.InstrumentPriceType;
import com.solum.xplain.extensions.calendar.ValuationDateReferenceData;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Map;
import java.util.Optional;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.FieldNameConstants;

@Data
@FieldNameConstants
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class CreditCurveCreditNodeCalculatedView extends CreditCurveNodeView {
  private String creditSpreadId;
  private BigDecimal creditSpread;
  private BigDecimal creditSpreadAsk;
  private BigDecimal creditSpreadMid;
  private BigDecimal creditSpreadBid;
  private InstrumentPriceType calibrationPriceType;
  private String dataSource;
  private String ticker;
  private Double survivalProbability;
  private LocalDate endDate;
  private CreditCurveNodeType type;
  private double recoveryRate;

  public static CreditCurveCreditNodeCalculatedView of(
      CreditCurve curve,
      CreditCurveIsdaNode curveNode,
      LocalDate valuationDate,
      Map<String, CalculationMarketValueFullView> quotes,
      Map<LocalDate, ChartPoint> rates,
      InstrumentPriceType priceType,
      ReferenceData referenceData) {
    var nodeKey = curveNode.getKey(curve);
    var recoveryRateKey = curveNode.getRecoveryRateKey(curve);
    var date =
        curveNode.date(
            curve, valuationDate, ValuationDateReferenceData.wrap(referenceData, valuationDate));

    CreditCurveCreditNodeCalculatedView view = new CreditCurveCreditNodeCalculatedView();
    view.setTenor(curveNode.getTenor());
    view.setKey(nodeKey);
    view.setType(curveNode.getType());
    view.setEndDate(date);
    view.setCalibrationPriceType(priceType);
    Optional.ofNullable(quotes.get(nodeKey)).ifPresent(view::withMarketDataSpread);
    Optional.ofNullable(date)
        .map(rates::get)
        .map(ChartPoint::getCalculatedValue)
        .ifPresent(view::setSurvivalProbability);

    var rateFromQuote = quotes.get(recoveryRateKey);
    view.setRecoveryRate(
        rateFromQuote != null && rateFromQuote.getValue() != null
            ? rateFromQuote.getValue().doubleValue()
            : curve.getRecoveryRate());

    return view;
  }

  protected void withMarketDataSpread(CalculationMarketValueFullView spread) {
    creditSpreadId = spread.getId();
    creditSpread = spread.getValue();
    creditSpreadAsk = spread.getAskValue();
    creditSpreadMid = spread.getMidValue();
    creditSpreadBid = spread.getBidValue();
    dataSource = spread.getProvider();
    ticker = spread.getTicker();
  }
}
