package com.solum.xplain.core.company;

import com.solum.xplain.core.common.EntityReference;
import com.solum.xplain.core.common.value.AllowedTeamsForm;

public record CompanyCsvData(
    String externalId,
    String name,
    String description,
    String slaDeadline,
    EntityReference valuationDataGroup,
    EntityReference marketDataGroup,
    EntityReference curveConfiguration,
    String reportingCcy,
    AllowedTeamsForm allowedTeamsForm) {}
