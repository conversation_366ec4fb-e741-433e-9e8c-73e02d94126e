package com.solum.xplain.core.company.csv.common;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PROTECTED)
public class CommonCompanyEntityCsvFields {
  public static final String COMPANY_EXTERNAL_ID = "Company ID";
  public static final String SLA_DEADLINE = "SLA Deadline";
  public static final String VALUATION_DATA_GROUP = "Valuation Data Group";
  public static final String MARKET_DATA_GROUP = "Market Data Group";
  public static final String CURVE_CONFIGURATION = "Curve Configuration";
  public static final String REPORTING_CCY = "Reporting Ccy";
  public static final String TEAMS = "Teams";
  public static final String ALLOW_ALL_TEAMS = "Allow all teams";
}
