package com.solum.xplain.core.company.repository;

import static com.solum.xplain.core.common.filter.VersionedEntityFilter.active;
import static com.solum.xplain.core.common.value.FutureVersionsAction.DELETE;
import static com.solum.xplain.core.common.value.NewVersionFormV2.ROOT_DATE;
import static com.solum.xplain.core.company.CompanySettingsType.BESPOKE;
import static com.solum.xplain.core.company.CompanySettingsType.DEFAULT;
import static com.solum.xplain.core.utils.ReflectionUtils.propertyName;
import static org.springframework.data.mongodb.core.query.Criteria.where;
import static org.springframework.data.mongodb.core.query.Query.query;

import com.solum.xplain.core.common.EntityId;
import com.solum.xplain.core.common.EntityReference;
import com.solum.xplain.core.common.csv.ImportOptions;
import com.solum.xplain.core.common.csv.NewVersionFormV2Utils;
import com.solum.xplain.core.common.value.ArchiveEntityForm;
import com.solum.xplain.core.common.value.DateList;
import com.solum.xplain.core.common.value.NewVersionFormV2;
import com.solum.xplain.core.common.versions.BitemporalDate;
import com.solum.xplain.core.common.versions.GenericUniqueVersionedEntityRepository;
import com.solum.xplain.core.common.versions.VersionedEntity;
import com.solum.xplain.core.company.CompanySettingsType;
import com.solum.xplain.core.company.csv.CompanyCsvForm;
import com.solum.xplain.core.company.entity.CompanyValuationSettings;
import com.solum.xplain.core.company.entity.ValuationSettings;
import com.solum.xplain.core.company.entity.ValuationSettingsMarketDataGroup;
import com.solum.xplain.core.company.entity.ValuationSettingsNames;
import com.solum.xplain.core.company.events.CompanyArchived;
import com.solum.xplain.core.company.events.CompanyCreated;
import com.solum.xplain.core.company.events.CompanyImported;
import com.solum.xplain.core.company.form.ValuationSettingsForm;
import com.solum.xplain.core.company.mapper.ValuationSettingsMapper;
import com.solum.xplain.core.company.value.CompanyValuationSettingsResolver;
import com.solum.xplain.core.company.value.CompanyValuationSettingsView;
import com.solum.xplain.core.curveconfiguration.event.CurveConfigurationNameChanged;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.market.events.MarketDataGroupUpdated;
import com.solum.xplain.core.settings.repository.GlobalValuationSettingsRepository;
import io.atlassian.fugue.Either;
import java.time.LocalDate;
import java.util.Collection;
import java.util.List;
import java.util.Optional;
import org.springframework.context.annotation.Lazy;
import org.springframework.context.event.EventListener;
import org.springframework.data.mongodb.core.MongoOperations;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Repository;

@Repository
public class CompanyValuationSettingsRepository
    extends GenericUniqueVersionedEntityRepository<CompanyValuationSettings> {

  private final MongoOperations mongoOperations;
  private final CompanyEntityValuationSettingsResolver valuationSettingsResolver;
  private final ValuationSettingsMapper mapper;
  private final GlobalValuationSettingsRepository globalValuationSettingsRepository;
  private final CompanyLegalEntityValuationSettingsRepository
      legalEntityValuationSettingsRepository;

  public CompanyValuationSettingsRepository(
      MongoOperations mongoOperations,
      CompanyEntityValuationSettingsResolver valuationSettingsResolver,
      GlobalValuationSettingsRepository globalValuationSettingsRepository,
      @Lazy CompanyLegalEntityValuationSettingsRepository legalEntityValuationSettingsRepository) {
    super(mongoOperations, ValuationSettingsMapper.INSTANCE);
    this.mongoOperations = mongoOperations;
    this.valuationSettingsResolver = valuationSettingsResolver;
    this.mapper = ValuationSettingsMapper.INSTANCE;
    this.globalValuationSettingsRepository = globalValuationSettingsRepository;
    this.legalEntityValuationSettingsRepository = legalEntityValuationSettingsRepository;
  }

  @Override
  protected Criteria uniqueEntityCriteria(CompanyValuationSettings entity) {
    return hasEntityId(entity.getEntityId());
  }

  public CompanyValuationSettingsView getDefaultValuationSettings(
      String companyId, BitemporalDate stateDate) {
    return mapper.toView(companySettingsEntity(companyId, stateDate));
  }

  public Either<ErrorItem, EntityId> updateValuationSettings(
      String companyId,
      LocalDate version,
      ValuationSettingsForm form,
      ValuationSettingsNames names) {
    return entityExact(companyId, version)
        .map(
            entity ->
                update(
                    entity,
                    form.getVersionForm(),
                    copiedEntity -> updateCompanyValuationSettings(form, names, copiedEntity)));
  }

  private CompanyValuationSettings updateCompanyValuationSettings(
      ValuationSettingsForm form,
      ValuationSettingsNames names,
      CompanyValuationSettings versioned) {
    var marketDataGroup =
        Optional.ofNullable(form.getMarketDataGroupId())
            .map(
                v ->
                    ValuationSettingsMarketDataGroup.marketDataGroup(
                        v, names.getMarketDataGroupName()))
            .orElse(null);
    if (CompanySettingsType.valueOf(form.getSettingsType()) == DEFAULT) {
      return mapper.fromFormToDefault(form, marketDataGroup, versioned);
    } else {
      return mapper.fromForm(form, names, marketDataGroup, versioned);
    }
  }

  public List<CompanyValuationSettingsView> getCompanyValuationSettingsVersions(String companyId) {
    return entityVersions(companyId).stream().map(mapper::toView).toList();
  }

  public CompanyValuationSettingsResolver getCompanyEntitySettingsResolver(
      String companyId, BitemporalDate stateDate) {
    var defaultSettings = companySettingsEntity(companyId, stateDate);

    return CompanyValuationSettingsResolver.resolver(
        companyId,
        defaultSettings,
        legalEntityValuationSettingsRepository.getValuationSettings(stateDate, companyId));
  }

  @EventListener
  public void onMarketDataGroupUpdated(MarketDataGroupUpdated event) {
    mongoOperations.updateMulti(
        query(
            where(
                    propertyName(
                        ValuationSettings.Fields.marketDataGroup,
                        ValuationSettingsMarketDataGroup.Fields.marketDataGroupId))
                .is(event.getEntityId())),
        new Update()
            .set(
                propertyName(
                    ValuationSettings.Fields.marketDataGroup,
                    ValuationSettingsMarketDataGroup.Fields.marketDataGroupName),
                event.getForm().getName()),
        CompanyValuationSettings.class);
  }

  @EventListener
  public void onCurveConfigurationNameUpdated(CurveConfigurationNameChanged event) {
    mongoOperations.updateMulti(
        query(
            where(
                    propertyName(
                        ValuationSettings.Fields.curveConfiguration,
                        EntityReference.Fields.entityId))
                .is(event.getEntityId())),
        new Update()
            .set(
                propertyName(
                    ValuationSettings.Fields.curveConfiguration, EntityReference.Fields.name),
                event.getNewName()),
        CompanyValuationSettings.class);

    mongoOperations.updateMulti(
        query(
            where(
                    propertyName(
                        ValuationSettings.Fields.nonFxCurveConfiguration,
                        EntityReference.Fields.entityId))
                .is(event.getEntityId())),
        new Update()
            .set(
                propertyName(
                    ValuationSettings.Fields.nonFxCurveConfiguration, EntityReference.Fields.name),
                event.getNewName()),
        CompanyValuationSettings.class);
  }

  @EventListener
  public void onCompanyCreated(CompanyCreated companyCreated) {
    if (companyCreated instanceof CompanyImported companyImportedEvent) {
      onCompanyImported(companyImportedEvent);
    } else {
      insert(
          CompanyValuationSettings.newOf(companyCreated.getEntityId()),
          NewVersionFormV2.newDefault());
    }
  }

  private void onCompanyImported(CompanyImported companyImported) {
    var mdg = companyImported.getMarketDataGroup();
    var curveConfig = companyImported.getCurveConfiguration();
    var stateDate = companyImported.getStateDate();

    CompanyValuationSettings valuationSettings =
        CompanyValuationSettings.newOf(companyImported.getEntityId());

    var globalSettings = globalValuationSettingsRepository.getGlobalValuationSettings(stateDate);

    if (curveConfig != null || mdg != null) {
      // TODO: not going to be efficient if we're calling this for every row in the csv, maybe cache
      //    response or  pass in the event
      valuationSettings.applyDefaults(globalSettings);
      valuationSettings.setSettingsType(BESPOKE);
    }

    if (curveConfig != null) {
      valuationSettings.setCurveConfiguration(curveConfig);
    }

    if (mdg != null) {
      valuationSettings.setMarketDataGroup(
          ValuationSettingsMarketDataGroup.marketDataGroup(mdg.getEntityId(), mdg.getName()));
    }

    insert(valuationSettings, NewVersionFormV2.newDefault());
  }

  public EntityId updateValuationSettings(
      String companyId, CompanyCsvForm companyCsvForm, ImportOptions importOptions) {

    var version = importOptions.bitemporalDate();
    var currCompanySettings =
        entity(companyId, version, active()).getOr(() -> CompanyValuationSettings.newOf(companyId));

    var versionForm =
        NewVersionFormV2Utils.fromImportOptions(
            importOptions,
            currCompanySettings.getValidFrom(),
            importOptions::getFutureVersionsAction);

    return updateValuationSettings(currCompanySettings, companyCsvForm, version, versionForm);
  }

  private EntityId updateValuationSettings(
      CompanyValuationSettings currCompanySettings,
      CompanyCsvForm companyCsvForm,
      BitemporalDate version,
      NewVersionFormV2 vf) {

    // apply any global default valuation settings to the current valuation settings entity
    // we copy currCompanySettings so updates are not applied, so that we can later compare
    // currCompanySettings before and after global settings are applied
    var settingsWithGlobalDefaults =
        companySettingsEntityWithGlobalDefaults(mapper.copy(currCompanySettings), version);

    var valuationSettingsImportData =
        new ValuationSettingsImportUpdateData(
            companyCsvForm.getCurveConfiguration(),
            companyCsvForm.getMarketDataGroup(),
            companyCsvForm.getReportingCcy(),
            vf);

    return update(
        currCompanySettings,
        vf,
        v ->
            valuationSettingsResolver.updateValuationSettings(
                v, settingsWithGlobalDefaults, valuationSettingsImportData));
  }

  @EventListener
  public void onCompanyArchived(CompanyArchived event) {
    entityExact(event.getEntityId(), ROOT_DATE)
        .forEach(
            e ->
                archive(
                    e,
                    new ArchiveEntityForm(
                        NewVersionFormV2.builder()
                            .comment("Entity archived")
                            .validFrom(ROOT_DATE)
                            .futureVersionsAction(DELETE)
                            .build())));
  }

  public List<CompanyValuationSettings> companySettingsEntities(
      Collection<String> companyIds, BitemporalDate stateDate) {
    var rawCompanyValuationSettings =
        entities(stateDate, active(), where(VersionedEntity.Fields.entityId).in(companyIds));
    return companySettingsWithGlobalDefaults(rawCompanyValuationSettings, stateDate);
  }

  public CompanyValuationSettings companySettingsEntity(
      String companyId, BitemporalDate stateDate) {
    var entity =
        entity(companyId, stateDate, active())
            .getOr(() -> CompanyValuationSettings.newOf(companyId));
    return companySettingsEntityWithGlobalDefaults(entity, stateDate);
  }

  private CompanyValuationSettings companySettingsEntityWithGlobalDefaults(
      CompanyValuationSettings entity, BitemporalDate stateDate) {
    entity.applyDefaults(globalValuationSettingsRepository.getGlobalValuationSettings(stateDate));
    return entity;
  }

  private List<CompanyValuationSettings> companySettingsWithGlobalDefaults(
      List<CompanyValuationSettings> companyValuationSettings, BitemporalDate stateDate) {
    var globalValuationSettings =
        globalValuationSettingsRepository.getGlobalValuationSettings(stateDate);
    for (var entity : companyValuationSettings) {
      entity.applyDefaults(globalValuationSettings);
    }
    return companyValuationSettings;
  }

  public DateList getFutureVersions(String companyId, LocalDate stateDate) {
    var searchCriteria = hasEntityId(companyId);
    return futureVersionsByCriteria(searchCriteria, stateDate);
  }

  public Either<ErrorItem, EntityId> deleteValuationSettings(String entityId, LocalDate version) {
    return entityExact(entityId, version).flatMap(this::delete);
  }
}
