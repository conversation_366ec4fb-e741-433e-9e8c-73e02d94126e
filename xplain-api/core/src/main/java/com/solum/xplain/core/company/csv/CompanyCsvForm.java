package com.solum.xplain.core.company.csv;

import com.solum.xplain.core.common.EntityReference;
import com.solum.xplain.core.company.CompanyCsvData;
import com.solum.xplain.core.company.form.CompanyCreateForm;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.jspecify.annotations.Nullable;

@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class CompanyCsvForm extends CompanyCreateForm {

  /** The SLA deadline to set on the related CompanyIpvSettings. */
  @NotEmpty private String slaDeadline;

  /** The VDG to set on the related CompanyIpvSettings. */
  @NotEmpty private EntityReference valuationDataGroup;

  /** The MDG to set on the related CompanyValuationSettings. */
  @NotEmpty private EntityReference marketDataGroup;

  /** The CC to set on the related CompanyValuationSettings. */
  @NotEmpty private EntityReference curveConfiguration;

  /** The reporting currency to set on the related CompanyIpvSettings. */
  @Nullable private String reportingCcy;

  public static CompanyCsvForm of(CompanyCsvData csvData) {
    var form = new CompanyCsvForm();
    form.setExternalCompanyId(csvData.externalId());
    form.setName(csvData.name());
    form.setDescription(csvData.description());
    form.setSlaDeadline(csvData.slaDeadline());
    form.setValuationDataGroup(csvData.valuationDataGroup());
    form.setMarketDataGroup(csvData.marketDataGroup());
    form.setCurveConfiguration(csvData.curveConfiguration());
    form.setReportingCcy(csvData.reportingCcy());
    form.setAllowedTeamsForm(csvData.allowedTeamsForm());
    return form;
  }
}
