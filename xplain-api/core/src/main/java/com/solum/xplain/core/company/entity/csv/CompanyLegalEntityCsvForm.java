package com.solum.xplain.core.company.entity.csv;

import com.solum.xplain.core.common.EntityReference;
import com.solum.xplain.core.company.form.CompanyLegalEntityCreateForm;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.jspecify.annotations.Nullable;

@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class CompanyLegalEntityCsvForm extends CompanyLegalEntityCreateForm {

  @NotEmpty private String companyId;
  @NotEmpty private String companyExternalId;

  @NotEmpty private String slaDeadline;

  @NotEmpty private EntityReference valuationDataGroup;

  @NotEmpty private EntityReference marketDataGroup;

  @NotEmpty private EntityReference curveConfiguration;

  @Nullable private String reportingCcy;

  public static CompanyLegalEntityCsvForm of(CompanyEntityCsvData csvData) {
    var form = new CompanyLegalEntityCsvForm();
    form.setCompanyId(csvData.companyId());
    form.setCompanyExternalId(csvData.companyExternalId());
    form.setExternalId(csvData.entityExternalId());
    form.setName(csvData.entityName());
    form.setDescription(csvData.description());
    form.setCompanyExternalId(csvData.companyExternalId());
    form.setSlaDeadline(csvData.slaDeadline());
    form.setValuationDataGroup(csvData.valuationDataGroup());
    form.setMarketDataGroup(csvData.marketDataGroup());
    form.setCurveConfiguration(csvData.curveConfiguration());
    form.setReportingCcy(csvData.reportingCcy());
    form.setAllowedTeamsForm(csvData.allowedTeamsForm());
    return form;
  }
}
