package com.solum.xplain.core.curvegroup.curvecredit.entity;

import static io.atlassian.fugue.Either.left;
import static io.atlassian.fugue.Either.right;
import static java.lang.String.format;

import com.opengamma.strata.basics.ReferenceData;
import com.opengamma.strata.basics.date.Tenor;
import com.opengamma.strata.market.curve.IsdaCreditCurveNode;
import com.opengamma.strata.market.observable.QuoteId;
import com.opengamma.strata.product.credit.type.CdsQuoteConvention;
import com.opengamma.strata.product.credit.type.CdsTemplate;
import com.solum.xplain.core.curvegroup.curvecredit.CreditCurveMdkNameUtils;
import com.solum.xplain.core.curvegroup.instrument.InstrumentDefinition;
import com.solum.xplain.core.error.Error;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.instrument.InstrumentType;
import com.solum.xplain.core.market.mapping.MarketDataUtils;
import com.solum.xplain.extensions.curve.node.XplainCdsIsdaCreditCurveNode;
import io.atlassian.fugue.Either;
import java.time.LocalDate;
import org.springframework.lang.Nullable;

public abstract class CreditCurveIsdaNode extends CreditCurveNode {
  private static final String CDS_NODE_KEY_PATTERN = "%s_%s_%s";
  private static final String CDS_NODE_NAME_PATTERN = "%S %S %S";
  private static final String RECOVERY_RATE_KEY_PATTERN = "%s_RR";

  protected abstract InstrumentType instrumentType();

  @Nullable
  public abstract LocalDate date(
      CreditCurve curve, @Nullable LocalDate valuationDate, ReferenceData referenceData);

  protected abstract Either<ErrorItem, CdsTemplate> cdsTemplate(
      CreditCurve curve, ReferenceData referenceData);

  public InstrumentDefinition instrument(CreditCurve curve) {
    return InstrumentDefinition.ofCreditCurve(
        curve.getName(),
        curve.getSector(),
        instrumentType(),
        getTenor(),
        getKey(curve),
        getName(curve));
  }

  public InstrumentDefinition recoveryRateInstrument(CreditCurve curve) {
    return InstrumentDefinition.ofCreditCurve(
        curve.getName(),
        curve.getSector(),
        instrumentType(),
        null,
        getRecoveryRateKey(curve),
        getName(curve));
  }

  public Either<ErrorItem, IsdaCreditCurveNode> isdaNode(
      CreditCurve curve, ReferenceData referenceData) {
    try {
      return node(curve, referenceData);
    } catch (RuntimeException ex) {
      return left(new ErrorItem(Error.PARSING_ERROR, ex.getMessage()));
    }
  }

  private Either<ErrorItem, IsdaCreditCurveNode> node(
      CreditCurve curve, ReferenceData referenceData) {
    return cdsTemplate(curve, referenceData).flatMap(template -> node(template, curve));
  }

  private Either<ErrorItem, IsdaCreditCurveNode> node(CdsTemplate template, CreditCurve curve) {
    var quoteId = quoteId(curve);
    var legalEntityId = curve.legalEntityStandardId();
    var quoteConvention = curve.getQuoteConvention();

    return switch (CdsQuoteConvention.of(quoteConvention)) {
      case QUOTED_SPREAD ->
          right(
              XplainCdsIsdaCreditCurveNode.ofQuotedSpread(
                  template, quoteId, legalEntityId, curve.getFixedCoupon()));
      case PAR_SPREAD ->
          right(XplainCdsIsdaCreditCurveNode.ofParSpread(template, quoteId, legalEntityId));
      case POINTS_UPFRONT ->
          right(
              XplainCdsIsdaCreditCurveNode.ofPointsUpfront(
                  template, quoteId, legalEntityId, curve.getFixedCoupon()));
    };
  }

  public String getKey(CreditCurve curve) {
    return format(CDS_NODE_KEY_PATTERN, getTenor(), curve.getName(), curve.nodeType());
  }

  public String getRecoveryRateKey(CreditCurve curve) {
    return format(RECOVERY_RATE_KEY_PATTERN, curve.getName());
  }

  public String getName(CreditCurve curve) {
    var prefix = CreditCurveMdkNameUtils.cdsNodeMdkNamePrefix(curve);
    return format(CDS_NODE_NAME_PATTERN, prefix, curve.nodeType(), getTenor());
  }

  public abstract Either<ErrorItem, Tenor> parsedTenor();

  private QuoteId quoteId(CreditCurve curve) {
    return MarketDataUtils.quoteId(getKey(curve));
  }
}
