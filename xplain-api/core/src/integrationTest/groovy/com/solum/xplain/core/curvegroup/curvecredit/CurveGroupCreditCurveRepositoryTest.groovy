package com.solum.xplain.core.curvegroup.curvecredit

import static com.solum.xplain.core.common.filter.VersionedEntityFilter.active
import static com.solum.xplain.core.common.filter.VersionedEntityFilter.archived
import static com.solum.xplain.core.common.value.NewVersionFormV2.ROOT_DATE
import static com.solum.xplain.core.curvegroup.curvecredit.classifiers.CreditCurveType.CDS
import static com.solum.xplain.core.curvegroup.curvecredit.classifiers.CreditCurveType.CREDIT_INDEX
import static com.solum.xplain.core.curvegroup.curvecredit.classifiers.CreditCurveType.CREDIT_INDEX_TRANCHE
import static com.solum.xplain.core.curvegroup.curvecredit.entity.CreditCurveBuilder.creditCurve
import static com.solum.xplain.core.curvegroup.curvecredit.entity.CreditCurveBuilder.creditCurveWith
import static com.solum.xplain.core.curvegroup.curvecredit.entity.CreditCurveFundingNodeBuilder.creditCurveFundingNode
import static com.solum.xplain.core.curvegroup.curvegroup.entity.CurveGroupBuilder.curveGroup
import static com.solum.xplain.core.curvegroup.curvegroup.value.CurveGroupViewBuilder.curveGroupView
import static com.solum.xplain.core.curvemarket.CurveMarketSample.MARKET_STATE_FORM
import static com.solum.xplain.core.curvemarket.pricetype.InstrumentPriceRequirements.bidRequirements
import static com.solum.xplain.core.market.MarketDataSample.STATE_DATE
import static com.solum.xplain.core.market.MarketDataSample.VAL_DT
import static com.solum.xplain.extensions.enums.CreditSector.BASIC_MATERIALS
import static com.solum.xplain.shared.utils.filter.TableFilter.emptyTableFilter
import static java.math.BigDecimal.ONE
import static java.math.BigDecimal.TEN
import static java.time.LocalDate.now
import static java.time.LocalDate.parse
import static org.springframework.data.domain.Sort.Direction.DESC
import static org.springframework.data.mongodb.core.query.Criteria.where
import static org.springframework.data.mongodb.core.query.Query.query

import com.solum.xplain.core.common.EntityId
import com.solum.xplain.core.common.value.ArchiveEntityForm
import com.solum.xplain.core.common.value.ChartPoint
import com.solum.xplain.core.common.value.ChartPointBuilder
import com.solum.xplain.core.common.value.FutureVersionsAction
import com.solum.xplain.core.common.value.NewVersionFormV2
import com.solum.xplain.core.common.versions.BitemporalDate
import com.solum.xplain.core.common.versions.State
import com.solum.xplain.core.common.versions.VersionedNamedEntity
import com.solum.xplain.core.curvegroup.curve.entity.Curve
import com.solum.xplain.core.curvegroup.curvecredit.classifiers.CreditCurveNodeType
import com.solum.xplain.core.curvegroup.curvecredit.entity.CreditCurve
import com.solum.xplain.core.curvegroup.curvecredit.entity.CreditCurveBuilder
import com.solum.xplain.core.curvegroup.curvecredit.entity.CreditCurveCdsNodeBuilder
import com.solum.xplain.core.curvegroup.curvecredit.entity.CreditCurveIndexNode
import com.solum.xplain.core.curvegroup.curvecredit.value.CdsCurveForm
import com.solum.xplain.core.curvegroup.curvecredit.value.CdsCurveUpdateForm
import com.solum.xplain.core.curvegroup.curvecredit.value.CreditCurveNodeForm
import com.solum.xplain.core.curvegroup.curvecredit.value.CreditCurveSearch
import com.solum.xplain.core.curvegroup.curvecredit.value.CreditIndexCurveForm
import com.solum.xplain.core.curvegroup.curvecredit.value.CreditIndexCurveUpdateForm
import com.solum.xplain.core.curvegroup.curvecredit.value.CreditIndexTrancheCurveForm
import com.solum.xplain.core.curvegroup.curvegroup.entity.CurveGroup
import com.solum.xplain.core.curvegroup.curvegroup.entity.CurveGroupBuilder
import com.solum.xplain.core.curvegroup.curvegroup.value.CalibrationMarketData
import com.solum.xplain.core.curvemarket.CurveConfigMarketStateQuotes
import com.solum.xplain.core.curvemarket.marketvalue.CalculationMarketValueFullView
import com.solum.xplain.core.curvemarket.pricetype.InstrumentPriceType
import com.solum.xplain.core.error.Error
import com.solum.xplain.core.error.ErrorItem
import com.solum.xplain.core.helper.IntegrationSpecification
import com.solum.xplain.core.users.UserBuilder
import com.solum.xplain.extensions.enums.CreditDocClause
import com.solum.xplain.extensions.enums.CreditSector
import com.solum.xplain.extensions.enums.CreditSeniority
import jakarta.annotation.Resource
import java.time.LocalDateTime
import org.bson.types.ObjectId
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.data.domain.Sort
import org.springframework.data.mongodb.core.MongoOperations
import org.springframework.data.mongodb.core.query.Query
import org.springframework.security.authentication.TestingAuthenticationToken
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.test.context.ActiveProfiles
import spock.lang.Unroll

@SpringBootTest
@ActiveProfiles("test")
class CurveGroupCreditCurveRepositoryTest extends IntegrationSpecification {
  def static VALID_FROM = parse("2020-01-01")
  def static FUTURE_VALID_FROM = parse("2100-01-01")

  @Resource
  CurveGroupCreditCurveRepository repository
  @Resource
  MongoOperations operations

  def user = UserBuilder.user("userId")

  def setup() {
    def auth = new TestingAuthenticationToken(user, null)
    auth.authenticated = true
    SecurityContextHolder.getContext().authentication = auth
  }

  def cleanup() {
    SecurityContextHolder.getContext().authentication = null
    operations.remove(new Query(), CreditCurve)
    operations.remove(new Query(), CurveGroup)
  }

  def "should create credit curve"() {
    setup:
    def group = curveGroup()
    operations.insert(group)

    def form = new CdsCurveForm(
      reference: "BARC",
      corpTicker: "CORP",
      currency: "EUR",
      seniority: "SNRLAC",
      docClause: "CR14",
      entityLongName: "Legal entity",
      recoveryRate: 0.1,
      sector: "FINANCIALS",
      versionForm: NewVersionFormV2.newDefault()
      )

    when:
    def result = repository.createCurve(group.id, form)

    then:
    result.isRight()
    def entityId = result.getOrNull()

    def loaded = allSortedValidFromDesc(entityId.id)
    loaded.size() == 1

    // New major ROOT curve version
    assertAutofilledFields(loaded[0])
    assertCdsUpdateFormFields(loaded[0], form)
    loaded[0].state == State.ACTIVE
    loaded[0].curveType == CDS
    loaded[0].validFrom == form.versionForm.validFrom
    loaded[0].comment == form.versionForm.comment
    loaded[0].curveGroupId == group.id
    loaded[0].name == "BARC_EUR_SNRLAC_CR14"
    loaded[0].corpTicker == "CORP"
    loaded[0].reference == "BARC"
    loaded[0].currency == "EUR"
    loaded[0].seniority == CreditSeniority.SNRLAC
    loaded[0].docClause == CreditDocClause.CR14
  }

  def "should create Credit Index curve"() {
    setup:
    def group = curveGroup()
    operations.insert(group)

    def form = new CreditIndexCurveForm(
      creditIndexFactor: ONE,
      entityLongName: "INDEX",
      creditIndexVersion: 1,
      creditIndexSeries: 2,
      reference: "REFERENCE_CODE",
      currency: "USD",
      recoveryRate: 0.1,
      sector: CreditSector.UNDEFINED.name(),
      versionForm: NewVersionFormV2.newDefault()
      )

    when:
    def result = repository.createCurve(group.id, form)

    then:
    result.isRight()
    def entityId = result.getOrNull()

    def loaded = allSortedValidFromDesc(entityId.id)
    loaded.size() == 1

    // New major ROOT curve version
    assertAutofilledFields(loaded[0])
    assertIndexUpdateFormFields(loaded[0], form)
    loaded[0].state == State.ACTIVE
    loaded[0].curveType == CREDIT_INDEX
    loaded[0].validFrom == form.versionForm.validFrom
    loaded[0].comment == form.versionForm.comment
    loaded[0].curveGroupId == group.id
    loaded[0].name == "REFERENCE_CODE_USD"
    loaded[0].currency == "USD"
  }

  def "should create Credit Index Tranche curve"() {
    setup:
    def group = curveGroup()
    operations.insert(group)

    def form = new CreditIndexTrancheCurveForm(
      creditIndexFactor: ONE,
      entityLongName: "INDEX",
      creditIndexVersion: 1,
      creditIndexSeries: 2,
      creditIndexTranche: "0-3",
      reference: "REFERENCE_CODE",
      currency: "USD",
      recoveryRate: 0.1,
      sector: CreditSector.UNDEFINED.name(),
      versionForm: NewVersionFormV2.newDefault()
      )

    when:
    def result = repository.createCurve(group.id, form)

    then:
    result.isRight()
    def entityId = result.getOrNull()

    def loaded = allSortedValidFromDesc(entityId.id)
    loaded.size() == 1

    // New major ROOT curve version
    assertAutofilledFields(loaded[0])
    assertIndexUpdateFormFields(loaded[0], form)
    loaded[0].state == State.ACTIVE
    loaded[0].curveType == CREDIT_INDEX_TRANCHE
    loaded[0].validFrom == form.versionForm.validFrom
    loaded[0].comment == form.versionForm.comment
    loaded[0].curveGroupId == group.id
    loaded[0].name == "REFERENCE_CODE_0-3_USD"
    loaded[0].currency == "USD"
    loaded[0].creditIndexTranche == "0-3"
  }

  def "should update Credit Index curve"() {
    setup:
    def group = curveGroup()
    operations.insert(group)
    def curve = new CreditCurve(
      curveGroupId: group.id,
      entityId: ObjectId.get().toHexString(),
      curveType: CREDIT_INDEX,
      creditIndexFactor: ONE,
      entityLongName: "INDEX",
      creditIndexVersion: 1,
      creditIndexSeries: 2,
      reference: "REFERENCE_CODE",
      name: "REFERENCE_CODE_USD",
      docClause: CreditDocClause.CR14.name(),
      currency: "USD",
      recoveryRate: 0.1,
      state: State.ACTIVE,
      recordDate: LocalDateTime.now(),
      validFrom: ROOT_DATE
      )
    operations.insert(curve)
    def form = new CreditIndexCurveUpdateForm(
      creditIndexFactor: TEN,
      entityLongName: "UPDATED_INDEX",
      creditIndexVersion: 12,
      creditIndexSeries: 22,
      creditIndexStartDate: now(),
      docClause: CreditDocClause.XR14.name(),
      recoveryRate: 0.3,
      versionForm: NewVersionFormV2.newDefault()
      )

    when:
    def result = repository.updateCurve(group.id, curve.entityId, ROOT_DATE, form)

    then:
    result.isRight()
    def entityId = result.getOrNull()

    def loaded = allSortedValidFromDesc(entityId.id)
    loaded.size() == 2

    assertAutofilledFields(loaded[0])
    assertIndexUpdateFormFields(loaded[0], form)

    loaded[0].state == State.ACTIVE
    loaded[0].curveType == CREDIT_INDEX
    loaded[0].creditIndexFactor == TEN
    loaded[0].validFrom == form.versionForm.validFrom
    loaded[0].comment == form.versionForm.comment
    loaded[0].curveGroupId == group.id
    loaded[0].name == "REFERENCE_CODE_USD"
    loaded[0].entityLongName == "UPDATED_INDEX"
    loaded[0].recoveryRate == 0.3d
  }

  def "should insert curve with archived from ROOT date"() {
    setup:
    def group = curveGroup()
    operations.insert(group)

    def form = new CdsCurveForm(
      corpTicker: "CORP",
      reference: "BARC",
      currency: "EUR",
      seniority: "SNRLAC",
      docClause: "CR14",
      entityLongName: "Legal entity",
      recoveryRate: 0.1,
      sector: "FINANCIALS",
      versionForm: NewVersionFormV2.builder().validFrom(VALID_FROM).build()
      )

    when:
    def result = repository.createCurve(group.id, form)

    then:
    result.isRight()
    def entityId = result.getOrNull()

    def loaded = allSortedValidFromDesc(entityId.id)
    loaded.size() == 2

    // New major curve version
    assertAutofilledFields(loaded[0])
    assertCdsUpdateFormFields(loaded[0], form)
    loaded[0].state == State.ACTIVE
    loaded[0].validFrom == form.versionForm.validFrom
    loaded[0].comment == form.versionForm.comment
    loaded[0].curveGroupId == group.id
    loaded[0].name == "BARC_EUR_SNRLAC_CR14"
    loaded[0].corpTicker == "CORP"
    loaded[0].reference == "BARC"
    loaded[0].currency == "EUR"
    loaded[0].seniority == CreditSeniority.SNRLAC
    loaded[0].docClause == CreditDocClause.CR14

    // New major ROOT ARCHIVED curve version
    assertAutofilledFields(loaded[1])
    assertCdsUpdateFormFields(loaded[1], form)
    loaded[1].state == State.ARCHIVED
    loaded[1].validFrom == ROOT_DATE
    loaded[1].comment == form.versionForm.comment
    loaded[1].curveGroupId == group.id
    loaded[1].name == "BARC_EUR_SNRLAC_CR14"
    loaded[1].corpTicker == "CORP"
    loaded[1].reference == "BARC"
    loaded[1].currency == "EUR"
    loaded[1].seniority == CreditSeniority.SNRLAC
    loaded[1].docClause == CreditDocClause.CR14
  }

  @Unroll
  def "should update on insert if curve with the same name exists for state #initialState"() {
    setup:
    def group = curveGroup()
    operations.insert(group)

    def curve = new CreditCurveBuilder()
      .state(initialState)
      .curveGroupId(group.id)
      .build()
    operations.insert(curve)

    def form = new CdsCurveForm(
      corpTicker: "CORP",
      reference: curve.reference,
      currency: curve.currency,
      seniority: curve.seniority,
      docClause: curve.docClause,
      entityLongName: "Updated Legal entity",
      recoveryRate: 0.1,
      sector: "FINANCIALS",
      versionForm: NewVersionFormV2.builder().validFrom(VALID_FROM).stateDate(VALID_FROM).build()
      )
    when:
    def result = repository.createCurve(curve.curveGroupId, form)

    then:
    result.isRight()
    def entityId = result.getOrNull()

    def loaded = allSortedValidFromDesc(entityId.id)
    loaded.size() == 2

    // New major version
    assertAutofilledFields(loaded[0])
    assertCdsUpdateFormFields(loaded[0], form)
    loaded[0].state == State.ACTIVE
    loaded[0].validFrom == form.versionForm.validFrom
    loaded[0].comment == form.versionForm.comment
    loaded[0].curveGroupId == curve.curveGroupId
    loaded[0].name == curve.name
    loaded[0].corpTicker == curve.corpTicker
    loaded[0].currency == curve.currency
    loaded[0].seniority == curve.seniority
    loaded[0].docClause == curve.docClause

    // Initial major ROOT version
    curve.valueEquals(loaded[1])

    where:
    initialState << [State.ACTIVE, State.ARCHIVED, State.DELETED]
  }

  def "should update curve and order nodes"() {
    setup:
    def group = curveGroup()
    operations.insert(group)

    def curve = new CreditCurveBuilder()
      .curveGroupId(group.id)
      .corpTicker("DB")
      .currency("USD")
      .seniority(CreditSeniority.SNRLAC)
      .docClause(CreditDocClause.CR14)
      .name("DB_USD_SNRLAC_CR14")
      .build()
    operations.insert(curve)

    def form = new CdsCurveUpdateForm(
      recoveryRate: 1,
      corpTicker: "DB",
      sector: "INDUSTRIALS",
      quoteConvention: "PAR_SPREAD",
      cdsNodes: [
        new CreditCurveNodeForm(type: CreditCurveNodeType.CDS, tenor: "2Y"),
        new CreditCurveNodeForm(type: CreditCurveNodeType.CDS, tenor: "1Y")
      ],
      versionForm: NewVersionFormV2.newDefault()
      )

    when:
    def result = repository.updateCurve(curve.curveGroupId, curve.entityId, curve.validFrom, form)

    then:
    result.isRight()
    def entityId = result.getOrNull()

    def loaded = allSortedValidFromDesc(entityId.id)
    loaded.size() == 2

    // New minor version
    assertAutofilledFields(loaded[0])
    assertCdsUpdateFormFields(loaded[0], form)
    loaded[0].cdsNodes[0].tenor == "1Y"
    loaded[0].cdsNodes[1].tenor == "2Y"
    loaded[0].state == State.ACTIVE
    loaded[0].validFrom == form.versionForm.validFrom
    loaded[0].comment == form.versionForm.comment
    loaded[0].curveGroupId == curve.curveGroupId
    loaded[0].name == curve.name
    loaded[0].corpTicker == curve.corpTicker
    loaded[0].currency == curve.currency
    loaded[0].seniority == curve.seniority
    loaded[0].docClause == curve.docClause

    // Initial major ROOT version
    curve.valueEquals(loaded[1])
  }

  def "should update and set fixedRate to null when cdsQuoteConvention is QUOTED_SPREAD"() {
    setup:
    def curve = new CreditCurveBuilder()
      .quoteConvention("POINTS_UPFRONT")
      .fixedCoupon(0.005)
      .build()
    operations.insert(curve)

    def form = new CdsCurveUpdateForm(
      corpTicker: "CORP",
      quoteConvention: "QUOTED_SPREAD",
      sector: "TECHNOLOGY",
      versionForm: NewVersionFormV2.builder()
      .comment("Version comment")
      .validFrom(curve.validFrom)
      .futureVersionsAction(FutureVersionsAction.KEEP)
      .build()
      )

    when:
    def result = repository.updateCurve(curve.curveGroupId, curve.entityId, curve.validFrom, form)

    then:
    result.isRight()
    def entityId = result.getOrNull()

    def loaded = allSortedValidFromDesc(entityId.id)
    loaded.size() == 2

    // New minor version
    assertAutofilledFields(loaded[0])
    loaded[0].quoteConvention == "QUOTED_SPREAD"
    loaded[0].fixedCoupon == null
    loaded[0].state == State.ACTIVE
    loaded[0].validFrom == form.versionForm.validFrom
    loaded[0].comment == form.versionForm.comment
    loaded[0].curveGroupId == curve.curveGroupId
    loaded[0].name == curve.name
    loaded[0].reference == curve.reference
    loaded[0].corpTicker == form.corpTicker
    loaded[0].currency == curve.currency
    loaded[0].seniority == curve.seniority
    loaded[0].docClause == curve.docClause

    // Initial major ROOT version
    curve.valueEquals(loaded[1])
  }

  def "should update and remove future versions"() {
    setup:
    def group = curveGroup()
    operations.insert(group)

    def curve = new CreditCurveBuilder()
      .curveGroupId(group.id)
      .build()
    operations.insert(curve)

    def curveInFuture = new CreditCurveBuilder()
      .curveGroupId(group.id)
      .entityId(curve.entityId)
      .validFrom(FUTURE_VALID_FROM)
      .build()
    operations.insert(curveInFuture)

    def form = new CdsCurveUpdateForm(
      sector: "TECHNOLOGY",
      recoveryRate: (curve.recoveryRate + 1),
      versionForm: NewVersionFormV2.builder()
      .validFrom(VALID_FROM)
      .futureVersionsAction(FutureVersionsAction.DELETE)
      .build()
      )

    when:
    def result = repository.updateCurve(curve.curveGroupId, curve.entityId, curve.validFrom, form)

    then:
    result.isRight()
    def entityId = result.right().get() as EntityId

    def loaded = allSortedValidFromDesc(entityId.id)
    loaded.size() == 4

    // Future Major (DELETED)
    loaded[0].validFrom == FUTURE_VALID_FROM
    loaded[0].state == State.DELETED

    // Future major
    loaded[1].validFrom == FUTURE_VALID_FROM
    loaded[1].state == State.ACTIVE

    // New major
    loaded[2].validFrom == VALID_FROM
    loaded[2].state == State.ACTIVE

    // Initial major (ROOT)
    loaded[3].validFrom == ROOT_DATE
    loaded[3].state == State.ACTIVE
  }

  def "should not update curve when no changes"() {
    setup:
    def group = curveGroup()
    operations.insert(group)

    def curve = new CreditCurveBuilder()
      .curveGroupId(group.id)
      .build()
    operations.insert(curve)

    def form = new CdsCurveUpdateForm(
      corpTicker: "CORP",
      entityLongName: curve.entityLongName,
      recoveryRate: curve.recoveryRate,
      quoteConvention: curve.quoteConvention,
      fixedCoupon: curve.fixedCoupon,
      sector: curve.sector,
      cdsNodes: [], // as in curve,
      fundingNodes: [], // as in curve
      versionForm: NewVersionFormV2.builder().validFrom(curve.validFrom).build()
      )

    when:
    def result = repository.updateCurve(curve.curveGroupId, curve.entityId, curve.validFrom, form)

    then:
    result.isRight()
    def entityId = result.right().get() as EntityId

    def loaded = allSortedValidFromDesc(entityId.id)
    loaded.size() == 1

    loaded[0].valueEquals(curve)
  }

  def "should not update index curve when no changes"() {
    setup:
    def group = curveGroup()
    operations.insert(group)

    def curve = CreditCurveBuilder.indexCurve().tap {
      curveGroupId = group.id
    }
    operations.insert(curve)

    def form = new CreditIndexCurveUpdateForm(
      entityLongName: curve.entityLongName,
      creditIndexSeries: curve.creditIndexSeries,
      creditIndexVersion: curve.creditIndexVersion,
      creditIndexStartDate: curve.creditIndexStartDate,
      creditIndexFactor: curve.creditIndexFactor,
      recoveryRate: curve.recoveryRate,
      quoteConvention: curve.quoteConvention,
      fixedCoupon: curve.fixedCoupon,
      sector: curve.sector,
      docClause: curve.docClause,
      indexNodes: [], // as in curve
      versionForm: NewVersionFormV2.builder().validFrom(curve.validFrom).build()
      )

    when:
    def result = repository.updateCurve(curve.curveGroupId, curve.entityId, curve.validFrom, form)

    then:
    result.isRight()
    def entityId = result.right().get() as EntityId

    def loaded = allSortedValidFromDesc(entityId.id)
    loaded.size() == 1

    loaded[0].valueEquals(curve)
  }

  def "should archive curve"() {
    setup:
    def group = curveGroup()
    operations.insert(group)

    def curve = new CreditCurveBuilder()
      .curveGroupId(group.id)
      .build()
    operations.insert(curve)

    when:
    def form = new ArchiveEntityForm(NewVersionFormV2.newDefault())
    def result = repository.archiveCurve(
      curve.curveGroupId,
      curve.entityId,
      curve.validFrom,
      form
      )

    then:
    result.isRight()
    def loaded = allSortedValidFromDesc(curve.entityId)

    loaded.size() == 2
    // New ARCHIVED major version
    assertAutofilledFields(loaded[0])
    loaded[0].state == State.ARCHIVED
    loaded[0].validFrom == form.versionForm.validFrom
    loaded[0].comment == form.versionForm.comment

    // Initial version
    loaded[1].valueEquals(curve)
  }

  def "should make curve version DELETED"() {
    setup:
    def group = curveGroup()
    operations.insert(group)

    def curve = new CreditCurveBuilder()
      .curveGroupId(group.id)
      .build()
    operations.insert(curve)

    when:
    def result = repository.deleteCurve(curve.curveGroupId, curve.entityId, curve.validFrom)

    then:
    result.isRight()
    def entityId = result.right().get() as EntityId

    def loaded = allSortedValidFromDesc(entityId.id)
    loaded.size() == 2

    loaded[0].validFrom == curve.validFrom
    loaded[0].state == State.DELETED

    loaded[1].validFrom == curve.validFrom
    loaded[1].state == State.ACTIVE
  }

  def "should fail delete curve with status DELETED"() {
    setup:
    def group = curveGroup()
    operations.insert(group)

    def curve = new CreditCurveBuilder()
      .curveGroupId(group.id)
      .state(State.DELETED)
      .build()
    operations.insert(curve)

    when:
    def result = repository.deleteCurve(curve.curveGroupId, curve.entityId, curve.validFrom)

    then:
    result.isLeft()
    def error = result.left().get() as ErrorItem
    error.reason == Error.OPERATION_NOT_ALLOWED

    def loaded = allSortedValidFromDesc(curve.entityId)
    loaded.size() == 1

    loaded[0].validFrom == curve.validFrom
    loaded[0].state == State.DELETED
  }

  def "should get curve views and return empty if no curves"() {
    def stateDate = BitemporalDate.newOfNow()
    def group = curveGroup()
    operations.insert(group)

    when:
    def loaded = repository.getCurveViews(group.id, stateDate, active(), emptyTableFilter(), Sort.unsorted())

    then:
    loaded.size() == 0
  }

  def "should load #filter curve views"() {
    setup:
    def stateDate = BitemporalDate.newOfNow()
    def group = curveGroup()
    operations.insert(group)

    def archivedCurve = new CreditCurveBuilder().curveGroupId(group.id).state(State.ARCHIVED).build()
    operations.insert(archivedCurve)
    def activeCurve = new CreditCurveBuilder().curveGroupId(group.id).state(State.ACTIVE).build()
    operations.insert(activeCurve)
    def deletedCurve = new CreditCurveBuilder().curveGroupId(group.id).state(State.DELETED).build()
    operations.insert(deletedCurve)

    when:
    def loaded = repository.getCurveViews(group.id, stateDate, filter, emptyTableFilter(), Sort.unsorted())

    then:
    loaded.size() == 1

    where:
    filter << [archived(), active()]
  }

  def "should load sorted curve list"() {
    setup:
    def curve = creditCurve()
    def curve2 = creditCurveWith({ c ->
      c.reference("ABD")
      c.name("BARC_EUR_SNRFOR_CR15")
    })
    def curve3 = creditCurveWith({ c ->
      c.reference("ABD")
      c.seniority(CreditSeniority.SUBLT2)
      c.name("BARC_EUR_SNRFOR_CR16")
    })
    def curve4 = creditCurveWith({ c ->
      c.reference("ABE")
      c.curveType(CREDIT_INDEX)
      c.name("BARC_EUR_SNRFOR_CR13")
    })
    def curve5 = creditCurveWith({ c ->
      c.reference("ABD")
      c.seniority(CreditSeniority.SECDOM)
      c.currency("USD")
      c.curveType(CREDIT_INDEX)
      c.name("BARC_EUR_SNRFOR_CR15")
    })
    def curve6 = creditCurveWith({ c ->
      c.reference("ABD")
      c.seniority(CreditSeniority.SECDOM)
      c.currency("AUD")
      c.curveType(CREDIT_INDEX)
      c.name("BARC_EUR_SNRFOR_CR14")
    })
    operations.insertAll([curve, curve2, curve3, curve4, curve5, curve6])
    def stateDate = BitemporalDate.newOfNow()
    def sort = Sort.by(Curve.Fields.curveType, VersionedNamedEntity.Fields.name)

    when:
    def loaded = repository.getCurveViews(curve.curveGroupId, stateDate, active(), emptyTableFilter(), sort)

    then:
    loaded.size() == 6
    loaded.curveType == [CDS, CDS, CDS, CREDIT_INDEX, CREDIT_INDEX, CREDIT_INDEX]
    loaded.name == [
      "BARC_EUR_SNRFOR_CR14",
      "BARC_EUR_SNRFOR_CR15",
      "BARC_EUR_SNRFOR_CR16",
      "BARC_EUR_SNRFOR_CR13",
      "BARC_EUR_SNRFOR_CR14",
      "BARC_EUR_SNRFOR_CR15"
    ]
  }

  def "should get (latest) active curve view"() {
    setup:
    def group = curveGroup()
    operations.insert(group)

    def curveVersion1 = new CreditCurveBuilder()
      .curveGroupId(group.id)
      .recordDate(STATE_DATE.recordDate.minusSeconds(2))
      .build()
    operations.insert(curveVersion1)

    def curveVersion2 = new CreditCurveBuilder()
      .entityId(curveVersion1.entityId)
      .sector(BASIC_MATERIALS)
      .entityLongName("legalEntity")
      .quoteConvention("POINTS_UPFRONT")
      .fixedCoupon(10.0)
      .curveGroupId(group.id)
      .recordDate(STATE_DATE.recordDate.minusSeconds(1))
      .build()
    operations.insert(curveVersion2)

    def archivedCurve = new CreditCurveBuilder()
      .state(State.ARCHIVED)
      .curveGroupId(group.id)
      .build()
    operations.insert(archivedCurve)

    when:
    def loaded = repository.getActiveCurveView(
      curveVersion2.curveGroupId,
      curveVersion1.entityId,
      STATE_DATE.actualDate
      )

    then:
    loaded.isRight()
    with(loaded.getOrNull()) {
      state == curveVersion2.state
      validFrom == curveVersion2.validFrom
      comment == curveVersion2.comment
      curveGroupId == curveVersion2.curveGroupId
      name == curveVersion2.name
      corpTicker == curveVersion2.corpTicker
      currency == curveVersion2.currency
      seniority == curveVersion2.seniority.name()
      docClause == curveVersion2.docClause.name()
      entityLongName == curveVersion2.entityLongName
      recoveryRate == curveVersion2.recoveryRate
      calendar == "EUTA+GBLO"
      quoteConvention == curveVersion2.quoteConvention
      fixedCoupon == curveVersion2.fixedCoupon
      sector == curveVersion2.sector.name()
      numberOfCdsNodes == curveVersion2.cdsNodes.size()
      numberOfFundingNodes == curveVersion2.fundingNodes.size()
    }
  }

  def "should load index curve view"() {
    setup:
    def group = curveGroup()
    operations.insert(group)

    def curve = CreditCurveBuilder.indexCurve()
    curve.indexNodes = [new CreditCurveIndexNode(tenor: "1Y")]
    operations.insert(curve)

    when:
    def loaded = repository.getActiveCurveView(curve.curveGroupId, curve.entityId, now())

    then:
    loaded.isRight()
    with(loaded.getOrNull()) {
      creditIndexStartDate == curve.creditIndexStartDate
      entityLongName == curve.entityLongName
      creditIndexFactor == curve.creditIndexFactor
      numberOfCdsNodes == 1
      numberOfFundingNodes == 0
    }
  }

  def "should return error if no active curve view"() {
    when:
    def loaded = repository.getActiveCurveView("curveGroupId", "id", now())

    then:
    loaded.isLeft()
    def error = loaded.left().get() as ErrorItem
    error.reason == Error.OBJECT_NOT_FOUND
  }


  def "should load curve version views"() {
    setup:
    def curve = new CreditCurveBuilder().build()
    def futureCurve = new CreditCurveBuilder()
      .entityId(curve.entityId)
      .state(State.DELETED)
      .validFrom(FUTURE_VALID_FROM)
      .build()
    operations.insertAll([curve, futureCurve])

    when:
    def result = repository.getCurveVersionViews(curve.curveGroupId, curve.entityId)

    then:
    result.size() == 2

    // Future DELETED major version
    result[0].entityId == futureCurve.entityId
    result[0].validFrom == futureCurve.validFrom
    result[0].state == State.DELETED

    // Initial ROOT version
    result[1].entityId == curve.entityId
    result[1].validFrom == curve.validFrom
    result[1].state == State.ACTIVE
  }

  def "should load active curve"() {
    setup:
    def curve = operations.insert(creditCurve())

    when:
    def result = repository.getActiveCurve(curve.curveGroupId, curve.entityId, now())

    then:
    result.isRight()

    def retrievedCurve = result.right().get() as CreditCurve
    retrievedCurve.entityId == curve.entityId
    retrievedCurve.validFrom == curve.validFrom
    retrievedCurve.valueEquals(curve)
  }

  def "should load active curves"() {
    setup:
    def activeCurve = new CreditCurveBuilder().build()
    def archivedCurve = new CreditCurveBuilder().state(State.ARCHIVED).build()
    operations.insertAll([activeCurve, archivedCurve])

    when:
    def result = repository.getActiveCurves(archivedCurve.curveGroupId, BitemporalDate.newOf(archivedCurve.validFrom))

    then:
    result.size() == 1

    result.get(0).entityId == activeCurve.entityId
    result.get(0).validFrom == activeCurve.validFrom
    result.get(0).valueEquals(activeCurve)
  }


  def "should load curve future versions dates list"() {
    setup:
    def curve = new CreditCurveBuilder().build()
    def futureCurve = new CreditCurveBuilder()
      .name(curve.name)
      .entityId(curve.entityId)
      .state(State.ARCHIVED)
      .validFrom(FUTURE_VALID_FROM)
      .build()
    operations.insertAll([curve, futureCurve])

    when:
    def search = new CreditCurveSearch(curve.name, curve.validFrom)
    def result = repository.getFutureVersions(curve.curveGroupId, search)

    then:
    result.dates == [futureCurve.validFrom]
  }

  def "should load curve cds node list with no nodes"() {
    setup:
    def group = new CurveGroupBuilder()
      .calibrationDate(parse("2017-01-01"))
      .build()
    def curve = new CreditCurveBuilder().build()
    def version = curve.validFrom
    def marketStateQuotes = new CurveConfigMarketStateQuotes(MARKET_STATE_FORM, [:])
    operations.insert(curve)

    when:
    def loaded = repository.curveCdsNodes(
      curveGroupView(group),
      curve.entityId,
      version,
      marketStateQuotes,
      now(),
      group.calibrationCurrency
      )

    then:
    loaded.size() == 0
  }

  def "should load curve cds node list"() {
    setup:
    def version = parse("2017-01-01")
    def valuationDate = parse("2020-01-01")
    def node = new CreditCurveCdsNodeBuilder().build()
    def curve1 = new CreditCurveBuilder()
      .cdsNodes([node])
      .chartPoints([ChartPointBuilder.point(parse("2020-12-20"))])
      .validFrom(version)
      .build()

    def group = new CurveGroupBuilder()
      .id(curve1.curveGroupId)
      .calibrationDate(MARKET_STATE_FORM.getStateDate())
      .calibrationMarketDataGroup(new CalibrationMarketData(
      marketDataGroupId: MARKET_STATE_FORM.getMarketDataGroupId(),
      sourceType: MARKET_STATE_FORM.getMarketDataSource(),
      curveConfigurationId: MARKET_STATE_FORM.getConfigurationId()))
      .calibrationPriceRequirements(bidRequirements())
      .build()
    operations.insert(curve1)
    def spreads = ["1Y_BARC_EUR_SNRFOR_CR14_SPREAD": mdValueView()]
    def marketStateQuotes = new CurveConfigMarketStateQuotes(MARKET_STATE_FORM, spreads)
    when:
    def loaded = repository.curveCdsNodes(curveGroupView(group),
      curve1.entityId,
      version,
      marketStateQuotes,
      valuationDate,
      group.calibrationCurrency
      )

    then:
    loaded.size() == 1
    def view = loaded[0]
    view.tenor == curve1.cdsNodes[0].tenor
    view.endDate == parse("2020-12-20")
    view.creditSpreadId == "spreadId"
    view.creditSpread == 10.0
    view.creditSpreadAsk == 11.0
    view.creditSpreadMid == 11.6
    view.creditSpreadBid == 12.0
    view.calibrationPriceType == InstrumentPriceType.BID_PRICE
    view.ticker == "EONIATICK"
    view.survivalProbability == 1
    view.key == "1Y_BARC_EUR_SNRFOR_CR14_SPREAD"
  }

  def "should create recovery-only view when no nodes exist"() {
    setup:
    def version = parse("2017-01-01")
    def valuationDate = parse("2020-01-01")

    def curve1 = new CreditCurveBuilder()
      .cdsNodes([]) // Empty nodes list
      .chartPoints([ChartPointBuilder.point(parse("2020-12-20"))])
      .validFrom(version)
      .build()

    def group = new CurveGroupBuilder()
      .id(curve1.curveGroupId)
      .calibrationDate(MARKET_STATE_FORM.getStateDate())
      .calibrationMarketDataGroup(new CalibrationMarketData(
      marketDataGroupId: MARKET_STATE_FORM.getMarketDataGroupId(),
      sourceType: MARKET_STATE_FORM.getMarketDataSource(),
      curveConfigurationId: MARKET_STATE_FORM.getConfigurationId()))
      .calibrationPriceRequirements(bidRequirements())
      .build()

    operations.insert(curve1)

    // Include recovery rate in spreads
    def spreads = [(curve1.getName() + "_RR"): mdValueView()]
    def marketStateQuotes = new CurveConfigMarketStateQuotes(MARKET_STATE_FORM, spreads)

    when:
    def loaded = repository.curveCdsNodes(curveGroupView(group),
      curve1.entityId,
      version,
      marketStateQuotes,
      valuationDate,
      group.calibrationCurrency
      )

    then:
    loaded.size() == 1
    def view = loaded[0]
    view.key == curve1.getName() + "_RR"
    view.recoveryRate == 10
    view.calibrationPriceType == InstrumentPriceType.BID_PRICE
    view.tenor == null
    view.endDate == null
    view.survivalProbability == null
  }

  def "should use default recovery rate when no recovery rate spread is available and no nodes exist"() {
    setup:
    def version = parse("2017-01-01")
    def valuationDate = parse("2020-01-01")
    def defaultRecoveryRate = 0.35

    def curve1 = new CreditCurveBuilder()
      .cdsNodes([]) // Empty nodes list
      .recoveryRate(defaultRecoveryRate)
      .chartPoints([ChartPointBuilder.point(parse("2020-12-20"))])
      .validFrom(version)
      .build()

    def group = new CurveGroupBuilder()
      .id(curve1.curveGroupId)
      .calibrationDate(MARKET_STATE_FORM.getStateDate())
      .calibrationMarketDataGroup(new CalibrationMarketData(
      marketDataGroupId: MARKET_STATE_FORM.getMarketDataGroupId(),
      sourceType: MARKET_STATE_FORM.getMarketDataSource(),
      curveConfigurationId: MARKET_STATE_FORM.getConfigurationId()))
      .calibrationPriceRequirements(bidRequirements())
      .build()

    operations.insert(curve1)

    def spreads = [:]
    def marketStateQuotes = new CurveConfigMarketStateQuotes(MARKET_STATE_FORM, spreads)

    when:
    def loaded = repository.curveCdsNodes(curveGroupView(group),
      curve1.entityId,
      version,
      marketStateQuotes,
      valuationDate,
      group.calibrationCurrency
      )

    then:
    loaded.size() == 1
    def view = loaded[0]
    view.key == curve1.getName() + "_RR"
    view.recoveryRate == defaultRecoveryRate // Should fall back to curve's default recovery rate
    view.calibrationPriceType == InstrumentPriceType.BID_PRICE
  }

  def "should load curve funding node list"() {
    setup:
    def group = new CurveGroupBuilder()
      .calibrationDate(parse("2017-01-01"))
      .build()

    def node = creditCurveFundingNode()
    def curve = new CreditCurveBuilder()
      .curveGroupId(group.id)
      .fundingNodes([node])
      .build()
    operations.insertAll([curve])
    def spreads = ["1Y_BARC_EUR_FUNDING": mdValueView()]
    def priceRequirements = bidRequirements()
    when:
    def loaded = repository.getFundingNodes(curveGroupView(group), curve.entityId, curve.validFrom, spreads, priceRequirements)

    then:
    loaded.size() == 1
    def view = loaded[0]
    view.tenor == curve.fundingNodes[0].tenor
    view.key == "1Y_BARC_EUR_FUNDING"
    view.creditSpreadId == "spreadId"
    view.creditSpread == 10.0
    view.creditSpreadAsk == 11.0
    view.creditSpreadMid == 11.6
    view.creditSpreadBid == 12.0
    view.calibrationPriceType == InstrumentPriceType.BID_PRICE
    view.ticker == "EONIATICK"
  }

  def "should load curve index node list"() {
    setup:
    def version = parse("2017-01-01")
    def node = new CreditCurveIndexNode(tenor: "1Y")
    def curve = CreditCurveBuilder.indexCurve()
    curve.setChartPoints([ChartPointBuilder.point(parse("2018-12-20"))])
    curve.setIndexNodes([node])

    def group = new CurveGroupBuilder()
      .id(curve.curveGroupId)
      .calibrationDate(MARKET_STATE_FORM.getStateDate())
      .calibrationMarketDataGroup(new CalibrationMarketData(
      marketDataGroupId: MARKET_STATE_FORM.getMarketDataGroupId(),
      sourceType: MARKET_STATE_FORM.getMarketDataSource(),
      curveConfigurationId: MARKET_STATE_FORM.getConfigurationId()))
      .calibrationPriceRequirements(bidRequirements())
      .build()
    operations.insert(curve)
    def spreads = ["1Y_CDX_NA_HY_S2_V1_SPREAD": mdValueView()]
    when:
    def loaded = repository.getIndexNodes(curveGroupView(group),
      curve.entityId,
      version,
      spreads,
      now(),
      MARKET_STATE_FORM)

    then:
    loaded.size() == 1
    def view = loaded[0]
    view.tenor == "1Y"
    view.endDate == parse("2018-12-20")
    view.creditSpreadId == "spreadId"
    view.creditSpread == 10.0
    view.creditSpreadAsk == 11.0
    view.creditSpreadMid == 11.6
    view.creditSpreadBid == 12.0
    view.calibrationPriceType == InstrumentPriceType.BID_PRICE
    view.ticker == "EONIATICK"
    view.survivalProbability == 1
    view.key == "1Y_CDX_NA_HY_S2_V1_SPREAD"
  }

  def "should load all curve node list"() {
    setup:
    def group = new CurveGroupBuilder()
      .calibrationDate(parse("2017-01-01"))
      .build()

    def fundingNode = creditCurveFundingNode()
    def indexNode = new CreditCurveIndexNode(tenor: "1Y", type: CreditCurveNodeType.CREDIT_INDEX)
    def cdsNode = new CreditCurveCdsNodeBuilder().build()
    def curve = new CreditCurveBuilder()
      .curveGroupId(group.id)
      .fundingNodes([fundingNode])
      .cdsNodes([cdsNode])
      .indexNodes([indexNode])
      .build()
    operations.insertAll([curve])
    when:
    def loaded = repository.allCurveNodes(curveGroupView(group).id, curve.entityId, curve.validFrom)

    then:
    loaded.size() == 3
    def cds = loaded[0]
    cds.tenor == curve.cdsNodes[0].tenor
    cds.curveName == curve.name
    cds.nodeType == CreditCurveNodeType.CDS
    def funding = loaded[1]
    funding.tenor == curve.fundingNodes[0].tenor
    funding.curveName == curve.name
    funding.nodeType == CreditCurveNodeType.FUNDING
    def index = loaded[2]
    index.tenor == curve.indexNodes[0].tenor
    index.curveName == curve.name
    index.nodeType == CreditCurveNodeType.CREDIT_INDEX
  }

  def "should load curve empty cds chart points if no points"() {
    setup:

    def curve = creditCurve()
    def group = new CurveGroupBuilder()
      .id(curve.curveGroupId)
      .calibrationDate(VAL_DT)
      .build()
    operations.insert(curve)

    when:
    def loaded = repository.getCurveChartPoints(
      curveGroupView(group),
      curve.entityId,
      MARKET_STATE_FORM,
      null
      )

    then:
    loaded == [:]
  }

  def "should load curve cds chart points"() {
    setup:
    def curve = new CreditCurveBuilder()
      .chartPoints([new ChartPoint(now(), 1, 1.0)])
      .build()
    def group = new CurveGroupBuilder()
      .id(curve.curveGroupId)
      .calibrationDate(MARKET_STATE_FORM.getStateDate())
      .calibrationMarketDataGroup(new CalibrationMarketData(
      marketDataGroupId: MARKET_STATE_FORM.getMarketDataGroupId(),
      sourceType: MARKET_STATE_FORM.getMarketDataSource(),
      curveConfigurationId: MARKET_STATE_FORM.getConfigurationId()))
      .calibrationPriceRequirements(bidRequirements())
      .build()
    operations.insert(curve)

    when:
    def loaded = repository.getCurveChartPoints(
      curveGroupView(group),
      curve.entityId,
      MARKET_STATE_FORM,
      null
      )

    then:
    List.copyOf(loaded.values()) == curve.chartPoints
  }

  def "should not load curve cds chart points if calibration date is different"() {
    setup:
    def calibrationDate = parse("2017-01-01")
    def curve = new CreditCurveBuilder()
      .chartPoints([new ChartPoint(now(), 1, 1.0)])
      .build()
    def group = new CurveGroupBuilder()
      .calibrationDate(calibrationDate.minusDays(1))
      .calibrationMarketDataGroup(new CalibrationMarketData(
      marketDataGroupId: MARKET_STATE_FORM.getMarketDataGroupId(),
      sourceType: MARKET_STATE_FORM.getMarketDataSource(),
      curveConfigurationId: MARKET_STATE_FORM.getConfigurationId()))
      .build()
    operations.insert(curve)

    when:
    def loaded = repository.getCurveChartPoints(
      curveGroupView(group),
      curve.entityId,
      MARKET_STATE_FORM,
      null
      )

    then:
    loaded == [:]
  }

  def "should update calibration results"() {
    setup:
    def valuationDate = parse("2017-01-01")
    def curve = creditCurve()

    operations.insert(curve)

    def expectedPoints = [new ChartPoint(valuationDate, 1, 1.0)]

    when:
    repository.updateCalibrationResults(curve.curveGroupId, valuationDate, { it == curve.getName() ? expectedPoints : null })

    then:
    def result = allSortedValidFromDesc(curve.entityId)
    result.size() == 1
    result[0].chartPoints == expectedPoints
  }

  def "should clear calibration results"() {
    setup:
    def curve = new CreditCurveBuilder()
      .chartPoints([new ChartPoint(VAL_DT, 1, 1)])
      .build()
    operations.insert(curve)

    when:
    repository.clearCalibrationResults(curve.curveGroupId)

    then:
    def result = operations.findById(curve.id, CreditCurve)
    result.chartPoints == []
  }

  static void assertCdsUpdateFormFields(CreditCurve curve, CdsCurveUpdateForm form) {
    assert curve.entityLongName == form.entityLongName
    assert curve.recoveryRate == form.recoveryRate.doubleValue()
    assert curve.quoteConvention == form.quoteConvention
    assert curve.fixedCoupon == form.fixedCoupon
    assert curve.corpTicker == form.corpTicker
    if (form.sector != null) {
      assert curve.sector.name() == form.sector
    }
    assert curve.cdsNodes.size() == (form.cdsNodes == null ? 0 : form.cdsNodes.size())
    assert curve.fundingNodes.size() == (form.fundingNodes == null ? 0 : form.fundingNodes.size())
  }

  static void assertIndexUpdateFormFields(CreditCurve curve, CreditIndexCurveUpdateForm form) {
    assert curve.entityLongName == form.entityLongName
    assert curve.recoveryRate == form.recoveryRate.doubleValue()
    assert curve.quoteConvention == form.quoteConvention
    assert curve.fixedCoupon == form.fixedCoupon
    if (form.sector != null) {
      assert curve.sector.name() == form.sector
    }
    assert curve.indexNodes.size() == (form.indexNodes == null ? 0 : form.indexNodes.size())
    assert curve.creditIndexSeries == form.creditIndexSeries
    assert curve.creditIndexFactor == form.creditIndexFactor
    assert curve.creditIndexStartDate == form.creditIndexStartDate
    assert curve.creditIndexVersion == form.creditIndexVersion
    if (form.docClause != null) {
      assert curve.docClause.name() == form.docClause
    }
  }

  void assertAutofilledFields(CreditCurve curve) {
    assert curve.recordDate != null
    assert curve.modifiedAt != null
    assert curve.modifiedBy != null
    assert curve.modifiedBy.name == user.name
  }

  private List<CreditCurve> allSortedValidFromDesc(String entityId) {
    operations
      .query(CreditCurve).matching(query(where("entityId").is(entityId))
      .with(Sort.by(DESC, "validFrom", "recordDate")))
      .all()
  }

  private CalculationMarketValueFullView mdValueView() {
    new CalculationMarketValueFullView(
      key: "key",
      id: "spreadId",
      ticker: "EONIATICK",
      value: TEN,
      askValue: 11.0,
      midValue: 11.6,
      bidValue: 12.0,
      provider: "BBG")
  }
}
