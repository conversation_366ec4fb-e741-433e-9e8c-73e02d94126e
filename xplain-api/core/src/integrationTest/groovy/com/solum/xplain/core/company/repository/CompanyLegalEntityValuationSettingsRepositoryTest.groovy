package com.solum.xplain.core.company.repository

import static com.solum.xplain.core.common.csv.ParsingMode.STRICT
import static com.solum.xplain.core.curvemarket.pricetype.InstrumentPriceRequirements.bidRequirements
import static com.solum.xplain.core.curvemarket.pricetype.InstrumentPriceType.ASK_PRICE
import static com.solum.xplain.core.users.UserBuilder.user
import static java.time.LocalDate.parse
import static org.springframework.data.domain.Sort.Direction.DESC
import static org.springframework.data.mongodb.core.query.Criteria.where
import static org.springframework.data.mongodb.core.query.Query.query

import com.solum.xplain.core.authentication.value.XplainPrincipal
import com.solum.xplain.core.common.EntityId
import com.solum.xplain.core.common.EntityReference
import com.solum.xplain.core.common.csv.DuplicateAction
import com.solum.xplain.core.common.csv.ImportOptions
import com.solum.xplain.core.common.value.AllowedCompaniesForm
import com.solum.xplain.core.common.value.AllowedTeamsForm
import com.solum.xplain.core.common.value.CalculationDiscountingForm
import com.solum.xplain.core.common.value.NewVersionFormV2
import com.solum.xplain.core.common.versions.BitemporalDate
import com.solum.xplain.core.common.versions.State
import com.solum.xplain.core.company.CompanySettingsType
import com.solum.xplain.core.company.entity.CompanyLegalEntityValuationSettings
import com.solum.xplain.core.company.entity.CompanyValuationSettings
import com.solum.xplain.core.company.entity.ValuationSettingsMarketDataGroup
import com.solum.xplain.core.company.entity.ValuationSettingsNames
import com.solum.xplain.core.company.entity.csv.CompanyLegalEntityCsvForm
import com.solum.xplain.core.company.events.CompanyArchived
import com.solum.xplain.core.company.events.CompanyLegalEntityArchived
import com.solum.xplain.core.company.events.CompanyLegalEntityCreated
import com.solum.xplain.core.company.form.ValuationSettingsForm
import com.solum.xplain.core.curveconfiguration.event.CurveConfigurationNameChanged
import com.solum.xplain.core.curvemarket.pricetype.InstrumentPriceRequirements
import com.solum.xplain.core.helper.IntegrationSpecification
import com.solum.xplain.core.market.events.MarketDataGroupUpdated
import com.solum.xplain.core.market.value.MarketDataGroupForm
import com.solum.xplain.core.settings.entity.GlobalValuationSettings
import jakarta.annotation.Resource
import java.time.LocalDate
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.data.domain.Sort
import org.springframework.data.mongodb.core.MongoOperations
import org.springframework.data.mongodb.core.query.Query
import org.springframework.security.authentication.TestingAuthenticationToken
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.test.context.ActiveProfiles

@SpringBootTest
@ActiveProfiles("test")
class CompanyLegalEntityValuationSettingsRepositoryTest extends IntegrationSpecification {

  def static VALID_FROM = parse("2020-01-01")
  def static FUTURE_VALID_FROM = parse("2100-01-01")

  def static STATE_DATE = BitemporalDate.newOf(VALID_FROM)

  @Resource
  MongoOperations operations

  @Resource
  CompanyLegalEntityValuationSettingsRepository repository

  @Resource
  CompanyEntityValuationSettingsResolver valuationSettingsResolver

  XplainPrincipal creator

  def setup() {
    creator = user("creatorId")
    def auth = new TestingAuthenticationToken(creator, null)
    auth.authenticated = true
    SecurityContextHolder.getContext().authentication = auth
  }

  def cleanup() {
    operations.remove(new Query(), CompanyValuationSettings)
    operations.remove(new Query(), CompanyLegalEntityValuationSettings)
    operations.remove(new Query(), GlobalValuationSettings)
  }

  def "should archive legal entity ipv settings"() {
    setup:
    operations.insert(bespokeValuationSettings({ c -> c.validFrom = NewVersionFormV2.ROOT_DATE }))

    when:
    repository.onCompanyArchived(CompanyArchived.newOf(EntityId.entityId("companyId")))
    then:
    def loaded = allSortedValidFromDesc("entityId")
    loaded.size() == 2
    loaded[0].state == State.ARCHIVED
  }

  def "should mark as deleted legal entity settings"() {
    setup:
    def settings = bespokeValuationSettings()
    operations.insert(settings)

    when:
    def result = repository.deleteValuationSettings(settings.entityId, settings.validFrom)
    then:
    result.right
    def loaded = allSortedValidFromDesc("entityId")
    loaded.size() == 2
    loaded[0].state == State.DELETED
  }

  def "should delete legal entity settings"() {
    setup:
    operations.insert(bespokeValuationSettings({ c -> c.validFrom = NewVersionFormV2.ROOT_DATE }))
    when:
    repository.onCompanyEntityArchived(CompanyLegalEntityArchived.newOf("companyId", "entityId"))
    then:
    def loaded = allSortedValidFromDesc("entityId")
    loaded.size() == 2
    loaded[0].state == State.ARCHIVED
  }

  def "should update portfolio valuation settings market data group name"() {
    setup:
    def settings = operations.insert(bespokeValuationSettings({ c -> c }))
    when:
    repository.onMarketDataGroupUpdated(new MarketDataGroupUpdated("marketDataGroupId2", new MarketDataGroupForm("New name", null, new AllowedCompaniesForm(true, []), new AllowedTeamsForm(true, []))))
    then:
    def loaded = operations.findById(settings.id, CompanyLegalEntityValuationSettings.class)
    loaded.marketDataGroup.marketDataGroupName == "New name"
  }

  def "should update legal entity valuation settings"() {
    setup:
    def settings = bespokeValuationSettings()
    operations.insert(settings)
    def form = new ValuationSettingsForm(
      settingsType: "BESPOKE",
      marketDataGroupId: "UPDATED",
      configurationType: "UPDATED",
      curveConfigurationId: "UPDATED",
      nonFxCurveConfigurationId: "UPDATED",
      curveDiscountingForm: new CalculationDiscountingForm("UPDATED", "UPDATED", "UPDATED_TRIANGULATION", true),
      reportingCurrency: "UPDATED",
      versionForm: NewVersionFormV2.builder().validFrom(VALID_FROM).build()
      )
    when:
    def result = repository.updateCompanyEntityValuationSettings(
      settings.entityId,
      settings.validFrom,
      form,
      new ValuationSettingsNames(
      marketDataGroupName: "UPDATED",
      curveConfigurationName: "UPDATED",
      nonFxCurveConfigurationName: "UPDATED"
      )
      )
    then:
    result.isRight()

    def loaded = allSortedValidFromDesc(result.getOrNull().id)
    loaded.size() == 2

    assertAutofilledFields(loaded[0])
    with(loaded[0]) {
      state == State.ACTIVE
      validFrom == form.versionForm.validFrom
      comment == form.versionForm.comment
      entityId == settings.entityId
      marketDataGroup.marketDataGroupId == "UPDATED"
      marketDataGroup.marketDataGroupName == "UPDATED"
      configurationType == "UPDATED"
      curveConfiguration.entityId == "UPDATED"
      nonFxCurveConfiguration.entityId == "UPDATED"
      strippingType == "UPDATED"
      discountingType == "UPDATED"
      triangulationCcy == "UPDATED_TRIANGULATION"
      useCsaDiscounting
      reportingCurrency == "UPDATED"
      priceRequirements == bidRequirements()
    }
  }

  def "should get company legal entity valuation settings"() {
    setup:
    def companySettings = companySettings()
    operations.insert(companySettings)

    def settings = bespokeValuationSettings({ c -> c })
    operations.insert(settings)

    expect:
    def view = repository.getCompanyEntitySettingsView("companyId", "entityId", STATE_DATE)
    view != null
    view.companyId == "companyId"
    view.entityId == "entityId"
    view.settingsType == CompanySettingsType.BESPOKE
    view.marketDataGroupId == "marketDataGroupId2"
    view.marketDataGroupName == "marketDataName2"
    view.configurationType == "TYPE2"
    view.curveConfigurationId == "id"
    view.curveConfigurationName == "name"
    view.nonFxCurveConfigurationId == "id1"
    view.nonFxCurveConfigurationName == "name1"
    view.strippingType == "STRIP2"
    view.discountingType == "DISCOUNT2"
    view.triangulationCcy == "TRIANGULATION2"
    view.reportingCurrency == "REPORT2"
    view.priceRequirements == new InstrumentPriceRequirements(ASK_PRICE, ASK_PRICE, ASK_PRICE, ASK_PRICE, ASK_PRICE)
    view.validFrom == VALID_FROM.minusYears(5)
  }

  def "should get company valuation settings versions"() {
    setup:
    def settings = bespokeValuationSettings()
    operations.insert(settings)
    operations.insert(bespokeValuationSettings())
    when:
    def result = repository.getCompanyValuationSettingsVersions(settings.entityId)
    then:
    result.size() == 2
    def view = result.get(0)
    view.marketDataGroupId == "marketDataGroupId2"
    view.marketDataGroupName == "marketDataName2"
    view.configurationType == "TYPE2"
    view.curveConfigurationId == "id"
    view.curveConfigurationName == "name"
    view.nonFxCurveConfigurationId == "id1"
    view.nonFxCurveConfigurationName == "name1"
    view.strippingType == "STRIP2"
    view.discountingType == "DISCOUNT2"
    view.triangulationCcy == "TRIANGULATION2"
    view.reportingCurrency == "REPORT2"
    view.priceRequirements == new InstrumentPriceRequirements(ASK_PRICE, ASK_PRICE, ASK_PRICE, ASK_PRICE, ASK_PRICE)
    view.validFrom == VALID_FROM.minusYears(5)
    view.entityId == settings.entityId
    view.modifiedBy == creator.name
    view.modifiedAt != null
    view.state == State.ACTIVE
  }

  def "should load future versions dates list"() {
    setup:
    def settings = bespokeValuationSettings()
    def futureSettings = bespokeValuationSettings()
    futureSettings.state = State.ARCHIVED
    futureSettings.validFrom = FUTURE_VALID_FROM
    operations.insertAll([settings, futureSettings])

    when:
    def result = repository.getFutureVersions(settings.entityId, settings.validFrom)

    then:
    result.dates == [futureSettings.validFrom]
  }

  def "should load valuation settings list"() {
    setup:
    operations.insert(bespokeValuationSettings({ c ->
      c.entityId = 'entity1'
      c.recordDate = STATE_DATE.recordDate.minusSeconds(2)
    }))
    operations.insert(bespokeValuationSettings({ c ->
      c.entityId = 'entity2'
      c.recordDate = STATE_DATE.recordDate.minusSeconds(1)
    }))

    when:
    def result = repository.getValuationSettings(STATE_DATE, "companyId")

    then:
    result.size() == 2
    result[0].entityId == "entity1"
    result[1].entityId == "entity2"
  }

  def "should update curve configuration names in portfolio"() {
    setup:
    def settings = bespokeValuationSettings({ c ->
      c.nonFxCurveConfiguration = new EntityReference(entityId: "id", name: "name1")
    })
    operations.insert(settings)
    repository.onCurveConfigurationNameUpdated(new CurveConfigurationNameChanged("id", "UPDATED"))
    expect:
    def result = operations.findById(settings.id, CompanyLegalEntityValuationSettings)
    result.curveConfiguration.name == "UPDATED"
    result.nonFxCurveConfiguration.name == "UPDATED"
  }

  def "should not update curve configuration names in portfolio"() {
    setup:
    def settings = bespokeValuationSettings({ c ->
      c.nonFxCurveConfiguration = new EntityReference(entityId: "id", name: "name")
    })
    operations.insert(settings)
    repository.onCurveConfigurationNameUpdated(new CurveConfigurationNameChanged("id1", "UPDATED"))
    expect:
    def result = operations.findById(settings.id, CompanyLegalEntityValuationSettings)
    result.curveConfiguration.name == "name"
    result.nonFxCurveConfiguration.name == "name"
  }

  def "should update curve configuration names in company"() {
    setup:
    def settings = bespokeValuationSettings()
    settings.nonFxCurveConfiguration = new EntityReference(entityId: "id", name: "name")
    operations.insert(settings)
    repository.onCurveConfigurationNameUpdated(new CurveConfigurationNameChanged("id", "UPDATED"))
    expect:
    def result = operations.findById(settings.id, CompanyLegalEntityValuationSettings)
    result.curveConfiguration.name == "UPDATED"
    result.nonFxCurveConfiguration.name == "UPDATED"
  }

  def "should update valuation settings type from default to bespoke"() {
    setup:
    def importOptions = importOptions()

    // default settings
    def compSettings = companySettings()
    def compId = compSettings.entityId
    def entId = "entityId"
    def entSettings = CompanyLegalEntityValuationSettings.newOf(
      compId,
      entId
      )

    operations.insert(compSettings)
    operations.insert(entSettings)

    def newCurveConfig = new EntityReference(entityId: "diffCcId", name: "diffCcName")
    def csvForm = entityCsvForm(compId, newCurveConfig)

    when:
    repository.updateValuationSettings(compId, entId, csvForm, importOptions)
    def outputSettings = operations.find(new Query(), CompanyLegalEntityValuationSettings.class)

    then:
    outputSettings.size() == 2
    def v1 = outputSettings[0]
    def v2 = outputSettings[1]
    v1.companyId == compId
    v2.companyId == compId
    v1.entityId == entId
    v2.entityId == entId

    // 2 versions...
    v1.valueEquals(entSettings)
    !v2.valueEquals(entSettings)
    v1.validFrom.isEqual(v2.validFrom) && v1.validFrom.isEqual(NewVersionFormV2.ROOT_DATE)
    v2.recordDate > v1.recordDate

    // ...due to updated curve config...
    v2.curveConfiguration.entityId == "diffCcId"
    v2.curveConfiguration.name == "diffCcName"
    v1.curveConfiguration == null

    // ...which means settings are now bespoke...
    v2.settingsType == CompanySettingsType.BESPOKE
    v1.settingsType == CompanySettingsType.DEFAULT

    // ...and all other fields in new version equal to global settings...
    v2.configurationType == compSettings.configurationType
    v2.nonFxCurveConfiguration == compSettings.nonFxCurveConfiguration
    v2.strippingType == compSettings.strippingType
    v2.discountingType == compSettings.discountingType
    v2.triangulationCcy == compSettings.triangulationCcy
    v2.reportingCurrency == compSettings.reportingCurrency
    v2.priceRequirements == compSettings.priceRequirements
    v2.marketDataGroup.marketDataGroupId == compSettings.marketDataGroup.marketDataGroupId
    v2.marketDataGroup.marketDataGroupName == compSettings.marketDataGroup.marketDataGroupName

    v2.useCsaDiscounting == null
  }

  private List<CompanyLegalEntityValuationSettings> allSortedValidFromDesc(String entityId) {
    operations.query(CompanyLegalEntityValuationSettings)
      .matching(query(where("entityId").is(entityId)).with(Sort.by(DESC, "validFrom", "recordDate")))
      .all()
  }

  def assertAutofilledFields(CompanyLegalEntityValuationSettings setting) {
    setting.id != null
    setting.recordDate != null
    setting.modifiedAt != null
    setting.modifiedBy != null
    setting.modifiedBy.name == creator.name
  }

  def "should create default company IPV settings"() {
    setup:
    repository.onCompanyEntityCreated(new CompanyLegalEntityCreated("companyId", "entityId"))
    expect:
    def loaded = allSortedValidFromDesc("entityId")
    loaded.size() == 1

    // New major ROOT version
    assertAutofilledFields(loaded[0])
    loaded[0].settingsType == CompanySettingsType.DEFAULT
    loaded[0].state == State.ACTIVE
    loaded[0].validFrom == LocalDate.ofEpochDay(0)
    loaded[0].entityId == "entityId"
    loaded[0].companyId == "companyId"
  }

  def "should get company legal entity settings by entityIds and stateDate"() {
    setup:
    def companySettings = companySettings()
    operations.insert(companySettings)

    def settings1 = bespokeValuationSettings({ c ->
      c.entityId = "entityId1"
      c.companyId = "companyId"
    })

    def settings2 = bespokeValuationSettings({ c ->
      c.entityId = "entityId2"
      c.companyId = "companyId"
    })

    operations.insertAll([settings1, settings2])

    def stateDate = BitemporalDate.newOf(VALID_FROM)

    when:
    def result = repository.getCompanyLegalEntitySettings(["entityId1", "entityId2"], stateDate)

    then:
    result.size() == 2

    def resultEntity1 = result["entityId1"]
    def resultEntity2 = result["entityId2"]

    resultEntity1 != null
    resultEntity1.companyId == "companyId"
    resultEntity1.entityId == "entityId1"
    resultEntity1.settingsType == CompanySettingsType.BESPOKE

    resultEntity2 != null
    resultEntity2.companyId == "companyId"
    resultEntity2.entityId == "entityId2"
    resultEntity2.settingsType == CompanySettingsType.BESPOKE
  }

  def "should return mapped views for each company legal entity settings"() {
    setup:
    def companySettings = companySettings()
    operations.insert(companySettings)

    def settings1 = bespokeValuationSettings({ c ->
      c.entityId = "entityId1"
      c.companyId = companySettings.entityId
    })

    def settings2 = bespokeValuationSettings({ c ->
      c.entityId = "entityId2"
      c.companyId = companySettings.entityId
    })

    operations.insertAll([settings1, settings2])

    def stateDate = BitemporalDate.newOf(VALID_FROM)

    when:
    def result = repository.getCompanyLegalEntitySettingsOfCompanies([companySettings.entityId], stateDate).toList()

    then:
    result.size() == 2

    def resultEntity1 = result.find { it -> it.entityId == "entityId1" }
    def resultEntity2 = result.find { it -> it.entityId == "entityId2" }

    resultEntity1 != null
    resultEntity1.companyId == "companyId"
    resultEntity1.entityId == "entityId1"
    resultEntity1.settingsType == CompanySettingsType.BESPOKE

    resultEntity2 != null
    resultEntity2.companyId == "companyId"
    resultEntity2.entityId == "entityId2"
    resultEntity2.settingsType == CompanySettingsType.BESPOKE
  }

  def companySettings() {
    new CompanyValuationSettings(
      entityId: "companyId",
      settingsType: "BESPOKE",
      marketDataGroup: new ValuationSettingsMarketDataGroup(
      marketDataGroupId: "marketDataGroupId",
      marketDataGroupName: "marketDataName"),
      configurationType: "TYPE",
      curveConfiguration: new EntityReference(entityId: "id", name: "name"),
      nonFxCurveConfiguration: new EntityReference(entityId: "id1", name: "name1"),
      strippingType: "STRIP",
      discountingType: "DISCOUNT",
      triangulationCcy: "TRIANGULATION",
      reportingCurrency: "REPORT",
      priceRequirements: bidRequirements(),
      validFrom: VALID_FROM.minusYears(10),
      state: State.ACTIVE,
      recordDate: STATE_DATE.recordDate.minusSeconds(1),
      )
  }

  def bespokeValuationSettings(Closure c = { cm -> cm }) {
    new CompanyLegalEntityValuationSettings(
      companyId: "companyId",
      entityId: "entityId",
      settingsType: "BESPOKE",
      marketDataGroup: new ValuationSettingsMarketDataGroup(
      marketDataGroupId: "marketDataGroupId2",
      marketDataGroupName: "marketDataName2"),
      configurationType: "TYPE2",
      curveConfiguration: new EntityReference(entityId: "id", name: "name"),
      nonFxCurveConfiguration: new EntityReference(entityId: "id1", name: "name1"),
      strippingType: "STRIP2",
      discountingType: "DISCOUNT2",
      triangulationCcy: "TRIANGULATION2",
      reportingCurrency: "REPORT2",
      priceRequirements: new InstrumentPriceRequirements(ASK_PRICE, ASK_PRICE, ASK_PRICE, ASK_PRICE, ASK_PRICE),
      validFrom: VALID_FROM.minusYears(5),
      state: State.ACTIVE,
      recordDate: STATE_DATE.recordDate.minusSeconds(1),
      ).with(true, c)
  }

  static def importOptions() {
    new ImportOptions(
      parse("2022-11-29"),
      DuplicateAction.REPLACE,
      STRICT,
      null,
      null,
      null,
      null,
      null
      )
  }

  static def entityCsvForm(String compId, EntityReference curveConfig = new EntityReference(entityId: "id", name: "name")) {
    new CompanyLegalEntityCsvForm(
      companyId: compId,
      companyExternalId: "extId",
      slaDeadline: "OTHER",
      valuationDataGroup: null,
      marketDataGroup: EntityReference.newOf(
      "marketDataGroupId",
      "marketDataName"),
      curveConfiguration: curveConfig,
      reportingCcy: "REPORT"
      )
  }
}
