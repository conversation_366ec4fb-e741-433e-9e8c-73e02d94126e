plugins {
  id "java-test-fixtures"
}

dependencies {
  implementation project(":xplain-api:core")
  implementation project(":shared:spring-mongo")
  implementation project(":shared:versions")
  implementation "jakarta.inject:jakarta.inject-api:2.0.1"
  implementation 'org.springframework.boot:spring-boot-starter-security'
  implementation 'org.springframework.boot:spring-boot-starter-web'
  implementation 'org.springframework.boot:spring-boot-starter-validation'
  implementation 'org.springframework.boot:spring-boot-starter-data-mongodb'
  runtimeOnly 'javax.cache:cache-api'
  implementation "com.opengamma.strata:strata-loader:${strataVersion}"
  implementation "com.opengamma.strata:strata-pricer:${strataVersion}"
  implementation "commons-beanutils:commons-beanutils:${commonsBeanUtilsVersion}"
  implementation "org.jeasy:easy-rules-core:${easyRulesVersion}"

  testImplementation(testFixtures(project(":shared:strata-extension")))
  testImplementation(testFixtures(project(":xplain-api:core")))

  testFixturesImplementation "io.atlassian.fugue:fugue:${fugueVersion}"
  testFixturesImplementation "com.opengamma.strata:strata-loader:${strataVersion}"
  testFixturesImplementation "com.opengamma.strata:strata-pricer:${strataVersion}"
  testFixturesImplementation project(":xplain-api:core")
  testFixturesImplementation(testFixtures(project(":xplain-api:core")))
  testFixturesImplementation project(":shared:utils")
  testFixturesImplementation project(":shared:versions")
  testFixturesImplementation "org.apache.groovy:groovy:${groovyJsonVersion}"
  testFixturesImplementation "org.mongodb:bson:5.6.0"
  testFixturesImplementation "org.spockframework:spock-core:${spockVersion}"
  testFixturesImplementation('org.springframework.boot:spring-boot-starter-test')

  integrationTestImplementation(testFixtures(project(":shared:utils")))
  integrationTestImplementation(testFixtures(project(":xplain-api:core")))
  integrationTestImplementation project(":shared:versions")
  integrationTestImplementation("com.google.guava:guava:${guavaVersion}")
  integrationTestImplementation "io.atlassian.fugue:fugue:${fugueVersion}"
  integrationTestImplementation "com.opengamma.strata:strata-loader:${strataVersion}"
  integrationTestImplementation "org.apache.commons:commons-lang3"
  integrationTestImplementation "org.apache.groovy:groovy:${groovyJsonVersion}"
  integrationTestImplementation "org.projectlombok:lombok:${lombokVersion}"
  integrationTestImplementation 'org.springframework.boot:spring-boot-starter-test'
  integrationTestImplementation 'org.springframework.boot:spring-boot-starter-oauth2-resource-server'
  integrationTestImplementation 'org.springframework.security:spring-security-test'
  integrationTestImplementation 'org.springframework.boot:spring-boot-starter-data-mongodb'
  integrationTestImplementation "org.spockframework:spock-core:${spockVersion}"
  integrationTestRuntimeOnly "org.junit.platform:junit-platform-launcher"
}
