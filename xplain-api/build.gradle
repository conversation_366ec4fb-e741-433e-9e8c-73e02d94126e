plugins {
  id 'org.owasp.dependencycheck' version "${dependencyCheckVersion}"
  id 'io.spring.dependency-management' version "${dependencyManagementVersion}"
  id "org.sonarqube" version "${sonarVersion}"
}


sonarqube {
  properties {
    property "sonar.projectKey", "solumxplain_solum-xplain-api"
    property "sonar.organization", "solumxplain"
    property "sonar.host.url", "https://sonarcloud.io"
  }
}

subprojects {
  group = 'com.solum.xplain'
  version = rootProject.version

  apply plugin: 'java'
  apply plugin: 'io.spring.dependency-management'
  apply plugin: 'groovy'
  apply plugin: 'jacoco'
  apply plugin: 'idea'
  apply plugin: 'org.owasp.dependencycheck'

  java {
    toolchain {
      languageVersion = JavaLanguageVersion.of(21)
    }
  }
  compileJava {
    sourceCompatibility = 21
    targetCompatibility = 21
    options.compilerArgs = [
      '-Amapstruct.unmappedTargetPolicy=ERROR',
      '-Amapstruct.defaultComponentModel=spring',
      '-parameters'
    ]
  }

  //Force higher SnakeYaml
  configurations {
    all*.exclude module: 'spring-boot-starter-logging'
    runtimeClasspath.exclude group: 'com.google.code.findbugs'
  }

  dependencyManagement {
    imports {
      mavenBom("org.springframework.boot:spring-boot-dependencies:${springBootVersion}")
      mavenBom "software.amazon.awssdk:bom:${awsSdkVersion}"
      mavenBom "io.mongock:mongock-bom:${mongockVersion}"
    }
  }

  dependencies {
    compileOnly "org.projectlombok:lombok:${lombokVersion}"
    compileOnly "com.google.code.findbugs:jsr305:${findBugsVersion}"

    implementation project(":shared:utils")
    integrationTestImplementation project(":shared:utils")

    implementation "org.apache.commons:commons-lang3"
    implementation "org.apache.commons:commons-collections4:${commonsCollections4Version}"

    implementation("com.google.guava:guava:${guavaVersion}")
    implementation "io.atlassian.fugue:fugue:${fugueVersion}"
    implementation "io.atlassian.fugue:fugue-extensions:${fugueVersion}"
    implementation "org.mapstruct:mapstruct:${mapstructVersion}"
    implementation "org.springdoc:springdoc-openapi-starter-webmvc-ui:${springDocVersion}"
    implementation 'org.springframework.boot:spring-boot-starter-oauth2-resource-server'


    implementation 'org.springframework.boot:spring-boot-starter-log4j2'
    implementation "io.sentry:sentry-log4j2:${sentrySdkVersion}"
    implementation "org.apache.logging.log4j:log4j-layout-template-json"
    implementation "com.lmax:disruptor:${disruptorVersion}"

    testCompileOnly "org.projectlombok:lombok:${lombokVersion}"
    testImplementation('org.springframework.boot:spring-boot-starter-test')
    testImplementation 'org.springframework.security:spring-security-test'
    testImplementation "org.apache.groovy:groovy-json:${groovyJsonVersion}"
    testImplementation "org.spockframework:spock-core:${spockVersion}"
    testImplementation "org.spockframework:spock-spring:${spockVersion}"
    testImplementation "org.testcontainers:mongodb"

    implementation "jakarta.inject:jakarta.inject-api:2.0.1"

    annotationProcessor "org.projectlombok:lombok:${lombokVersion}"
    annotationProcessor "org.projectlombok:lombok-mapstruct-binding:${lombokMapstructBindingVersion}"
    annotationProcessor "org.mapstruct:mapstruct-processor:${mapstructVersion}"
  }

  repositories {
    mavenCentral()
    maven {
      url = uri("https://maven.pkg.github.com/SolumXplain/solum-xplain-opengamma-strata")
      credentials {
        username = "x-access-token"
        password = project.findProperty("GITHUB_TOKEN") as String ?: System.getenv("GITHUB_TOKEN")
      }
    }
  }
}
