plugins {
  id 'java'
  id 'org.springframework.boot' version "${springBootVersion}"
  id 'com.google.cloud.tools.jib' version "${jibVersion}"
}

// Define a task to get the current Git branch
def getGitBranch = {
  try {
    def branch = 'git rev-parse --abbrev-ref HEAD'.execute().text.trim()
    return branch ?: 'unknown'
  } catch (Exception e) {
    return 'unknown' // Fallback if Git command fails
  }
}

// Define a task to get the current Git hash
def getGitHash = {
  try {
    def hash = 'git rev-parse --short HEAD'.execute().text.trim()
    return hash ?: 'unknown'
  } catch (Exception e) {
    return 'unknown' // Fallback if Git command fails
  }
}

processResources {
  def properties = new HashMap(project.properties)
  properties['git_branch'] = getGitBranch() // Explicitly add git.branch
  properties['git_hash'] = getGitHash() // Explicitly add git.hash
  filesMatching('application.yml') {
    expand(properties)
  }
}

jib {
  from {
    image = 'eclipse-temurin:21-jre'
    platforms {
      platform {
        architecture = 'amd64'
        os = 'linux'
      }
      platform {
        architecture = 'arm64'
        os = 'linux'
      }
    }
  }
  to {
    image = '575153581909.dkr.ecr.eu-west-2.amazonaws.com/solum-xplain-api'
  }
  container {
    ports = ['8080']
  }
}

sourceSets {
  integrationTest {
    resources {
      srcDir 'src/test/resources'
    }
  }
}

dependencies {
  implementation project(":xplain-api:core")
  implementation project(":xplain-api:calibration")
  implementation project(":xplain-api:calculation")
  implementation project(":xplain-api:xva")
  implementation project(":xplain-api:trs")
  implementation project(":xplain-api:secmaster")
  implementation project(":shared:strata-extension")
  implementation project(":xplain-api:support")
  implementation project(":xplain-api:workflow")
  implementation project(":xplain-api:xm")
  implementation project(":xplain-api:valuation-data")
  implementation project(":xplain-api:generic-product")
  implementation "org.springframework.boot:spring-boot-starter-web"
  implementation "com.javax0.license3j:license3j:${license3j}"

  testImplementation(testFixtures(project(":xplain-api:core")))
  testImplementation(testFixtures(project(":xplain-api:calculation")))
  testImplementation "org.springframework.data:spring-data-mongodb"

  integrationTestImplementation(testFixtures(project(":xplain-api:core")))
  integrationTestImplementation(testFixtures(project(":xplain-api:calculation")))
  integrationTestImplementation project(":xplain-api:secmaster")
  integrationTestImplementation "org.springframework.boot:spring-boot-starter-test"
  integrationTestImplementation "org.springframework.boot:spring-boot-starter-web"
  integrationTestImplementation "org.springframework.security:spring-security-test"
  integrationTestImplementation "org.springframework.data:spring-data-mongodb"
}
