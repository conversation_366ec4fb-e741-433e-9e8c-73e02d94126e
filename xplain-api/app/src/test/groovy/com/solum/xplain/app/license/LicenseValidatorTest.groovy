package com.solum.xplain.app.license

import javax0.license3j.License
import spock.lang.Specification

class LicenseValidatorTest extends Specification {

  def "should validate correct license"() {
    setup:
    def license = Mock(License)
    1 * license.isOK(LicensePublicKey.BYTES) >> true
    1 * license.isExpired() >> false
    def properties = Mock(LicenseProperties)
    1 * properties.getLicense() >> license

    when:
    def validator = new LicenseValidator(properties)

    then:
    validator.validate()
  }

  def "should throw error when license invalid"() {
    setup:
    def license = Mock(License)
    1 * license.isOK(LicensePublicKey.BYTES) >> false
    def properties = Mock(LicenseProperties)
    1 * properties.getLicense() >> license

    when:
    def validator = new LicenseValidator(properties)
    validator.validate()

    then:
    IllegalArgumentException exception = thrown()
    exception.message == "Invalid license key"
  }

  def "should throw error when license expired"() {
    setup:
    def license = Mock(License)
    1 * license.isOK(LicensePublicKey.BYTES) >> true
    1 * license.isExpired() >> true
    def properties = Mock(LicenseProperties)
    1 * properties.getLicense() >> license

    when:
    def validator = new LicenseValidator(properties)
    validator.validate()

    then:
    IllegalArgumentException exception = thrown()
    exception.message == "License is expired"
  }
}
