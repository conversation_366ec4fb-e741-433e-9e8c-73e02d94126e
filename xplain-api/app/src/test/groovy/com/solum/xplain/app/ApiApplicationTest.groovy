package com.solum.xplain.app

import com.solum.xplain.core.authentication.MeController
import com.solum.xplain.core.helper.IntegrationSpecification
import com.solum.xplain.shared.utils.event.CacheInvalidationListener
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.context.ApplicationContext
import org.springframework.test.context.ActiveProfiles

@SpringBootTest
@ActiveProfiles("test")
class ApiApplicationTest extends IntegrationSpecification {


  @Autowired(required = false)
  private MeController meController

  @Autowired
  private ApplicationContext beanFactory

  def "should load context"() {
    expect: "the MeController is created"
    meController
  }

  def "all caching beans should implement CacheInvalidationListener"() {
    expect:
    verifyEach(beanFactory.getBeanDefinitionNames().findAll { it.startsWithIgnoreCase("caching") }) {beanName ->
      def bean = beanFactory.getBean(beanName)
      assert bean instanceof CacheInvalidationListener : "Bean $beanName does not implement CacheInvalidationListener"
    }
  }
}
