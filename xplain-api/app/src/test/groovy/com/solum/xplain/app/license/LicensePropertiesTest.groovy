package com.solum.xplain.app.license

import spock.lang.Specification

class LicensePropertiesTest extends Specification {

  def XVA_LICENSE = "Ic5OXgAAABoAAAALAAAACmV4cGlyeURhdGUAAARcz71sAAAAAJwAAAABAAAAEAAAAIBsaWNlbnNlU2lnbmF0dXJlrGVuksqCt+3PyhLhTg+ncN4oFvByl+HL6i7Q/iq8XuuvBdoQ+Z4X7uSRzKOTxirjfxK0mUqufR6UL2nLFOHY9c5ikvNhQuDOT4GA77s6C1SKOe3lyBTd+jfGh7PNveQ+pHwUienmsbiqEHtmDnzQuphY+Iz4zcVlh01KrsELn+MAAAAiAAAAAgAAAA8AAAAHc2lnbmF0dXJlRGlnZXN0U0hBLTUxMgAAAAwAAAADAAAAA3h2YQE="
  def NON_XVA_LICENSE = "Ic5OXgAAABoAAAALAAAACmV4cGlyeURhdGUAAARcz71sAAAAAJwAAAABAAAAEAAAAIBsaWNlbnNlU2lnbmF0dXJlt+NZENRKp4gsTWOlaWgrOaUk29b0Q+0Hl1gbdYc5LRVdagrGWOpdnNRpC1DUvCeIneSbQ6qSCZs9/ynGYfWXl0VJxFnDusFAwyLGyNwZc9QmShzdLDpalyC26J0Rv7KG8eSlY1OiX3SQiuwS5zl0f/hMev6OzAY0MmPyw8N5vOUAAAAiAAAAAgAAAA8AAAAHc2lnbmF0dXJlRGlnZXN0U0hBLTUxMgAAAAwAAAADAAAAA3h2YQA="
  def TRS_LICENSE = "Ic5OXgAAABoAAAALAAAACmV4cGlyeURhdGUAAAaZ5GtMAAAAAJwAAAABAAAAEAAAAIBsaWNlbnNlU2lnbmF0dXJlVdaGkasquGQWTV152muHjC6b9ZuT91IRnhI3pE5rPNsmQHvw2u9AaeygJtj7Bp869r3ZPf0XGkw/ExYFQthoJjRsnbtV8Wogf6ov16A0A4D+2tRmwFiDM98hXBKIH7nCrCkaB86YYe45YfoNE+1ii5fF37bH7BlfaEyGPUCmU30AAAAiAAAAAgAAAA8AAAAHc2lnbmF0dXJlRGlnZXN0U0hBLTUxMgAAAAwAAAADAAAAA3RycwE="
  def NON_TRS_LICENSE = "Ic5OXgAAABoAAAALAAAACmV4cGlyeURhdGUAAAaZ5GtMAAAAAJwAAAABAAAAEAAAAIBsaWNlbnNlU2lnbmF0dXJlVdaGkasquGQWTV152muHjC6b9ZuT91IRnhI3pE5rPNsmQHvw2u9AaeygJtj7Bp869r3ZPf0XGkw/ExYFQthoJjRsnbtV8Wogf6ov16A0A4D+2tRmwFiDM98hXBKIH7nCrCkaB86YYe45YfoNE+1ii5fF37bH7BlfaEyGPUCmU30AAAAiAAAAAgAAAA8AAAAHc2lnbmF0dXJlRGlnZXN0U0hBLTUxMgAAAAwAAAADAAAAA3RycwA="

  def "should load license with XVA"() {
    setup:
    def properties = new LicenseProperties(XVA_LICENSE)
    expect:
    properties.isXvaEnabled()
  }

  def "should load license without XVA"() {
    setup:
    def properties = new LicenseProperties(NON_XVA_LICENSE)
    expect:
    !properties.isXvaEnabled()
  }

  def "should return error when invalid license"() {
    when:
    new LicenseProperties("aW50ZXJlc3RpbmdMaWNlbnNl")
    then:
    thrown IllegalArgumentException
  }

  def "should load license without TRS"() {
    setup:
    def properties = new LicenseProperties(NON_TRS_LICENSE)

    expect:
    !properties.isTrsEnabled()
  }

  def "should load license with TRS"() {
    setup:
    def properties = new LicenseProperties(TRS_LICENSE)

    expect:
    properties.isTrsEnabled()
  }
}
