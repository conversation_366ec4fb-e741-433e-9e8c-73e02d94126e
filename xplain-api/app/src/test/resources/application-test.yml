springdoc:
  api-docs:
    enabled: true
  swagger-ui:
    enabled: true
spring:
  hazelcast:
    config:
  cache:
    jcache:
      provider: com.hazelcast.cache.HazelcastMemberCachingProvider
  data:
    mongodb:
      uri: mongodb://${embedded.mongodb.host}:${embedded.mongodb.port}/${embedded.mongodb.database}
  security:
    oauth2:
      resourceserver:
        jwt:
          issuer-uri: https://somerandomauthserver.test.com/
app:
  version: 1.0.0
  license-key: Ic5OXgAAABoAAAALAAAACmV4cGlyeURhdGUAAARh+z/IAAAAARwAAAABAAAAEAAAAQBsaWNlbnNlU2lnbmF0dXJlIEsAavUzHCfjLRWIc0fGx1p5Bjlr7bF7aa5H64NPAdCP/eOO3CS+rC9Oc/Tz8B9FHYIZFXSqJgGrUmGHPeUBacEuUPEoKehTv8bcmFjBn25SzGx9jQWZIcYkR1vIWo4Z5GzT/VxIzRKkSxzBSqnCA8yTde2EBM6evPSjbe8J2DTH6ZuFl43FACFaqASkmMXJLgHfgsbTvlWecnke+/2oV/1JwdP71LY1357Qkr/yAG/64PfLtvx+L0fmd/l/weDD3h6hWhhxqPN95HGHJyQllarNdRinbAfvFTzPASHbqGwAG0/2B8r5l4ZNlqpEenLLDBMv+ff3XuoK+vP6K4+01wAAACIAAAACAAAADwAAAAdzaWduYXR1cmVEaWdlc3RTSEEtNTEyAAAADAAAAAMAAAADdHJzAQAAAAwAAAADAAAAA3h2YQA=
  oauth2:
    inactivity-timeout-minutes: 30
    token-cache-duration-minutes: 480
    claims:
      username-claim: xplain/email
      roles-claim: xplain/roles
      teams-claim: xplain/teams
  pnl-explain:
    enabled: true