# Log4j2 Component Configuration
# Enable async logging for all loggers to eliminate virtual thread pinning

# Use AsyncLoggerContextSelector to make all loggers async by default
log4j2.contextSelector=org.apache.logging.log4j.core.async.AsyncLoggerContextSelector

# Optional: Configure ring buffer size (default is 256k)
# log4j2.asyncLoggerRingBufferSize=262144

# Optional: Configure wait strategy (default is Block)
# log4j2.asyncLoggerWaitStrategy=Block