<?xml version="1.0" encoding="UTF-8" ?>
<Configuration status="WARN">
  <Properties>
    <Property name="LOG_PATTERN">%d{${LOG_DATEFORMAT_PATTERN:-yyyy-MM-dd HH:mm:ss.SSS}} %highlight{${LOG_LEVEL_PATTERN:-%5p}}{FATAL=red blink, ERROR=red, WARN=yellow bold, INFO=green, DEBUG=green bold, TRACE=blue} %style{${sys:PID}}{magenta} [%15.15t] %style{%-40.40c{1.}}{cyan} : %m%n${LOG_EXCEPTION_CONVERSION_WORD:-%wEx}
    </Property>
  </Properties>
  <Appenders>
    <Console follow="true" name="Console" target="SYSTEM_OUT">
      <PatternLayout pattern="${LOG_PATTERN}"/>
    </Console>
  </Appenders>
  <Loggers>
    <Logger level="TRACE" name="com.solum.xplain"/>
    <Logger level="DEBUG" name="com.solum.xplain.workflow.repository.DataModificationCommandQueue"/>
    <Logger level="DEBUG" name="com.solum.xplain.workflow.repository.SoftFlushExecutionBarrier"/>
    <Logger level="INFO" name="org.springframework.context.event"/>
    <Logger level="WARN" name="org.springframework.data.mongodb.core"/>
    <Logger level="WARN" name="org.mongodb"/>
    <Logger level="ERROR" name="org.apache.catalina.startup.DigesterFactory"/>
    <Logger level="ERROR" name="org.apache.catalina.util.LifecycleBase"/>
    <Logger level="WARN" name="org.apache.coyote.http11.Http11NioProtocol"/>
    <Logger level="WARN" name="org.apache.sshd.common.util.SecurityUtils"/>
    <Logger level="WARN" name="org.apache.tomcat.util.net.NioSelectorPool"/>
    <Logger level="ERROR" name="org.eclipse.jetty.util.component.AbstractLifeCycle"/>
    <Logger level="WARN" name="org.hibernate.validator.internal.util.Version"/>
    <Logger level="WARN" name="org.springframework.boot.actuate.endpoint.jmx"/>

    <Root level="INFO">
      <AppenderRef ref="Console"/>
    </Root>
  </Loggers>
</Configuration>
