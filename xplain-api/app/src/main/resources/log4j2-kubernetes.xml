<?xml version="1.0" encoding="UTF-8"?>
<Configuration packages="org.apache.logging.log4j.core,io.sentry.log4j2" status="WARN">

  <Appenders>
    <Console follow="true" name="Console" target="SYSTEM_OUT">
      <JsonTemplateLayout/>
    </Console>
    <Sentry minimumEventLevel="ERROR" name="SentryAppender"/>
  </Appenders>

  <Loggers>
    <Logger level="TRACE" name="com.solum.xplain"/>
    <Logger level="DEBUG" name="com.solum.xplain.workflow.repository.DataModificationCommandQueue"/>
    <Logger level="DEBUG" name="com.solum.xplain.workflow.repository.SoftFlushExecutionBarrier"/>
    <Logger level="INFO" name="org.springframework.context.event"/>
    <Logger level="WARN" name="org.springframework.data.mongodb.core"/>
    <Logger level="WARN" name="org.mongodb"/>
    <Logger level="ERROR" name="org.apache.catalina.startup.DigesterFactory"/>
    <Logger level="ERROR" name="org.apache.catalina.util.LifecycleBase"/>
    <Logger level="WARN" name="org.apache.coyote.http11.Http11NioProtocol"/>
    <Logger level="WARN" name="org.apache.sshd.common.util.SecurityUtils"/>
    <Logger level="WARN" name="org.apache.tomcat.util.net.NioSelectorPool"/>
    <Logger level="ERROR" name="org.eclipse.jetty.util.component.AbstractLifeCycle"/>
    <Logger level="WARN" name="org.hibernate.validator.internal.util.Version"/>
    <Logger level="WARN" name="org.springframework.boot.actuate.endpoint.jmx"/>

    <Root level="INFO">
      <AppenderRef ref="Console"/>
      <AppenderRef level="ERROR" ref="SentryAppender"/>
    </Root>
  </Loggers>
</Configuration>

