package com.solum.xplain.app.version;

import io.swagger.v3.oas.annotations.Operation;
import org.jspecify.annotations.NullMarked;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@NullMarked
@RestController
@RequestMapping("/version")
public class VersionController {

  private final String version;

  private final String branchName;

  private final String commitHash;

  public VersionController(
      @Value("${app.version}") String version,
      @Value("${app.git.branch}") String branchName,
      @Value("${app.git.commit_hash}") String commitHash) {
    this.version = version;
    this.branchName = branchName;
    this.commitHash = commitHash;
  }

  @Operation(summary = "Get app version")
  @GetMapping
  public VersionInfo version() {
    return new VersionInfo(version, branchName, commitHash);
  }
}
