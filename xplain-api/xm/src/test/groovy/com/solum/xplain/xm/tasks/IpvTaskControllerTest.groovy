package com.solum.xplain.xm.tasks

import static com.solum.xplain.core.lock.XplainLock.TASK_LOCK_ID
import static groovy.json.JsonOutput.toJson
import static org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.csrf
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.put

import com.solum.xplain.core.common.EntityId
import com.solum.xplain.core.lock.LockingSupport
import com.solum.xplain.core.lock.XplainLock
import com.solum.xplain.core.portfolio.CoreProductType
import com.solum.xplain.xm.excmngmt.enums.IpvExceptionManagementPhase
import com.solum.xplain.xm.helpers.MockMvcConfiguration
import com.solum.xplain.xm.tasks.enums.IpvTaskExecutionRerunType
import com.solum.xplain.xm.tasks.form.IpvTaskExecutionRerunRequest
import com.solum.xplain.xm.tasks.form.IpvTasksDefinitionForm
import com.solum.xplain.xm.tasks.service.IpvTaskExecutionService
import com.solum.xplain.xm.tasks.service.IpvTaskReRunExecutionService
import io.atlassian.fugue.Either
import java.util.function.Supplier
import org.bson.types.ObjectId
import org.spockframework.spring.SpringBean
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.http.MediaType
import org.springframework.test.web.servlet.MockMvc
import spock.lang.Specification
import spock.lang.Unroll

@Unroll
@MockMvcConfiguration
@WebMvcTest(controllers = [IpvTasksController])
class IpvTaskControllerTest extends Specification {

  @Autowired
  private MockMvc mockMvc

  @SpringBean
  private IpvTaskDefinitionsControllerService service = Mock()

  @SpringBean
  private IpvTaskExecutionService executionService = Mock()

  @SpringBean
  private IpvTaskReRunExecutionService reRunExecutionService = Mock()

  @SpringBean
  LockingSupport lockingSupport = Mock()


  def "should create task definition with response #responseBody and code #code "() {
    setup:
    service.update(IpvExceptionManagementPhase.OVERLAY_1, _ as IpvTasksDefinitionForm) >> EntityId.entityId("1")

    def results = mockMvc.perform(put("/ipv-tasks/definitions/OVERLAY_1")
      .with(csrf())
      .content(toJson(form))
      .contentType(MediaType.APPLICATION_JSON))
      .andReturn()

    expect:
    with(results.getResponse()) {
      getStatus() == code
      getContentAsString().indexOf(responseBody) >= 0
    }

    where:
    form                                                                                      | code | responseBody
    taskForm({ c -> c })                                                                      | 200  | """{"id":"1"}"""
    taskForm { c -> c.overrides = [overrideForm()] }                                          | 200  | """{"id":"1"}"""
    taskForm { c -> c.remove("granularityByTradeType") }                                      | 412  | "NotNull.ipvTasksDefinitionForm.granularityByTradeType"
    taskForm { c -> c.remove("granularityByRate") }                                           | 412  | "NotNull.ipvTasksDefinitionForm.granularityByRate"
    taskForm { c -> c.remove("granularityByFxCcyPairType") }                                  | 412  | "NotNull.ipvTasksDefinitionForm.granularityByFxCcyPairType"
    taskForm { c -> c.remove("granularityByContractualTerm") }                                | 412  | "NotNull.ipvTasksDefinitionForm.granularityByContractualTerm"
    taskForm { c -> c.remove("granularityBySector") }                                         | 412  | "NotNull.ipvTasksDefinitionForm.granularityBySector"
    taskForm { c -> c.remove("teams") }                                                       | 412  | "NotEmpty.ipvTasksDefinitionForm.teams"
    taskForm { c -> c.put("teams", [productTypeTeamsForm(CoreProductType.CDS, { a -> a })]) } | 412  | "Size.ipvTasksDefinitionForm.teams"
    taskForm { c -> c.overrides = [overrideForm(), overrideForm()] }                          | 412  | "Overrides have overlapping filters!"
  }

  def "should rerun IPV task executions with response #responseBody and code #code "() {
    setup:
    lockingSupport.doEitherWithLocks(_ as List, _ as Supplier) >> { List<?> args ->
      (args[1] as Supplier<Either<?, ?>>).get() as Either<?, ?>
    }
    reRunExecutionService.rerunTasks(_, _ as IpvTaskExecutionRerunRequest) >> Either.right([EntityId.entityId("1")])

    def results = mockMvc.perform(post("/ipv-tasks/executions/rerun")
      .with(csrf())
      .content(toJson(form))
      .contentType(MediaType.APPLICATION_JSON))
      .andReturn()

    expect:
    with(results.getResponse()) {
      getStatus() == code
      getContentAsString().indexOf(responseBody) >= 0
    }

    where:
    form                                                          | code | responseBody
    rerunForm({ c -> c.type = IpvTaskExecutionRerunType.DELETE }) | 200  | """{"id":"1"}"""
    rerunForm({ c -> c.type = IpvTaskExecutionRerunType.UPDATE }) | 200  | """{"id":"1"}"""
    rerunForm({ c -> c.type = null })                             | 412  | "NotNull.ipvTaskExecutionRerunRequest.type"
    rerunForm({ c -> c.taskIds = Collections.emptyList() })       | 412  | "NotEmpty.ipvTaskExecutionRerunRequest.taskIds"
    rerunForm({ c -> c.taskIds = null })                          | 412  | "NotEmpty.ipvTaskExecutionRerunRequest.taskIds"
    rerunForm({ c -> c.revaluatePortfolios = null })              | 412  | "NotNull.ipvTaskExecutionRerunRequest.revaluatePortfolios"
  }

  def taskForm(Closure c) {
    [
      teams                       : allExceptionManagementProductTypes(),
      overrides                   : [],
      granularityByTradeType      : "NONE",
      granularityByRate           : "NONE",
      granularityBySector         : "NONE",
      granularityByFxCcyPairType  : "NONE",
      granularityByContractualTerm: "NONE",
    ].with(true, c)
  }

  def taskExecutionsForm(Closure c) {
    [
      taskIds: ["task1"],

    ].with(true, c)
  }

  def rerunForm(Closure c) {
    [
      taskIds            : ["task1"],
      type               : IpvTaskExecutionRerunType.DELETE,
      revaluatePortfolios: false

    ].with(true, c)
  }

  def allExceptionManagementProductTypes() {
    Arrays.stream(CoreProductType.values())
      .map({ v -> productTypeTeamsForm(v, { c -> c }) })
      .toList()
  }

  def productTypeTeamsForm(CoreProductType type, Closure c) {
    [
      productType    : type,
      resolutionTeams: [ObjectId.get().toHexString()],
      approvalTeams  : [ObjectId.get().toHexString()],
    ].with(true, c)
  }

  def overrideForm() {
    [
      overrideTeams: Arrays.stream(CoreProductType.values())
      .map({ v -> overrideTeamForm(v, { a -> a }) })
      .toList()
    ]
  }

  def overrideTeamForm(CoreProductType type, Closure c) {
    [
      filter         : [
        productTypes: [type],
        rateCcys    : ["EUR"]
      ],
      resolutionTeams: [ObjectId.get().toHexString()],
      approvalTeams  : [ObjectId.get().toHexString()],
    ].with(true, c)
  }
}
