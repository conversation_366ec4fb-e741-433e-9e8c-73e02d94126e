package com.solum.xplain.xm.workflow.steps.vd


import com.solum.xplain.core.common.versions.BitemporalDate
import com.solum.xplain.core.company.value.ProvidersVo
import com.solum.xplain.workflow.service.StepStateOps
import com.solum.xplain.xm.excmngmt.processipv.CachingProviderDataService
import com.solum.xplain.xm.excmngmt.processipv.CachingXplainCalculationDataService
import com.solum.xplain.xm.excmngmt.processipv.data.ProviderDataValue
import com.solum.xplain.xm.excmngmt.processipv.data.ProviderDataWithGreeks
import com.solum.xplain.xm.excmngmt.processipv.data.Trade
import com.solum.xplain.xm.excmngmt.rulesipv.value.IpvProvidersType
import com.solum.xplain.xm.workflow.state.VdEntryContext
import com.solum.xplain.xm.workflow.state.VdEntryState
import org.springframework.beans.MutablePropertyValues
import spock.lang.Specification

class SetInitialBaseValueStepTest extends Specification {
  StepStateOps stepStateOps = Mock(StepStateOps)
  CachingProviderDataService cachingProviderDataService = Mock(CachingProviderDataService)
  CachingXplainCalculationDataService cachingXplainCalculationDataService = Mock(CachingXplainCalculationDataService)

  static ProviderDataWithGreeks nullPv = ProviderDataWithGreeks.emptyWithProvider("NULL")
  static ProviderDataWithGreeks validPv = new ProviderDataWithGreeks(
  provider: "VALID",
  pv: new ProviderDataValue(1.0, 1.0)
  )



  def "should set outcome (#currentPrimary.provider, #expectedProviderName)"() {
    given:
    def currentState = new VdEntryState()
    def currentContext = new VdEntryContext(BitemporalDate.newOfNow(), null, "mdgId", null, null, new Trade(), null, new ProvidersVo(expectedProviderName, null, null, null), null, null)
    stepStateOps.getInitialState() >> currentState
    stepStateOps.getContext() >> currentContext
    cachingProviderDataService.providerData(currentContext.vdg(), currentContext.trade(), currentContext.stateDate()) >> [currentPrimary]
    cachingXplainCalculationDataService.calculationData(currentContext.marketDataGroupId(), currentContext.trade(), currentContext.stateDate()) >> new ProviderDataWithGreeks(
      provider: "XPLAIN",
      pv: new ProviderDataValue(1.0, null)
      )

    when:
    new SetInitialBaseValueStep(cachingProviderDataService, cachingXplainCalculationDataService).runStep(stepStateOps)

    then:
    1 * stepStateOps.setOutcome(_) >> { MutablePropertyValues outcome ->
      assert outcome.get("baseValue") == expectedBaseValue
      assert outcome.get("providerType") == expectedProviderType
      assert outcome.get("providerName") == expectedProviderName
    }

    where:
    currentPrimary   || expectedBaseValue | expectedProviderType | expectedProviderName
    nullPv           || null              | IpvProvidersType.P1  | "NULL"
    validPv          || 1.0               | IpvProvidersType.P1  | "VALID"
    nullPv           || 1.0               | IpvProvidersType.P1  | "XPLAIN"
    validPv          || null              | IpvProvidersType.P1  | "MISSING"
  }

  def "should handle null primary provider without NPE"() {
    given:
    def currentState = new VdEntryState()
    def currentContext = new VdEntryContext(BitemporalDate.newOfNow(), null, "mdgId", null, null, new Trade(), null, new ProvidersVo(null, null, null, null), null, null)
    stepStateOps.getInitialState() >> currentState
    stepStateOps.getContext() >> currentContext
    cachingProviderDataService.providerData(currentContext.vdg(), currentContext.trade(), currentContext.stateDate()) >> []

    when:
    new SetInitialBaseValueStep(cachingProviderDataService, cachingXplainCalculationDataService).runStep(stepStateOps)

    then:
    1 * stepStateOps.setOutcome(_) >> { MutablePropertyValues outcome ->
      assert outcome.get("baseValue") == null
      assert outcome.get("providerType") == IpvProvidersType.P1
      assert outcome.get("providerName") == null
    }
    noExceptionThrown()
  }
}
