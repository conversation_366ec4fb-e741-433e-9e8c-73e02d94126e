package com.solum.xplain.xm.workflow.state.minifier

import static com.solum.xplain.xm.workflow.state.minifier.VdDashboardStateMinifier.deflateToCompactTrades
import static com.solum.xplain.xm.workflow.state.minifier.VdDashboardStateMinifier.inflateFromCompactTrades

import com.solum.xplain.core.classifiers.pricingslots.PricingSlot
import com.solum.xplain.core.company.value.IpvDataGroupVo
import com.solum.xplain.core.company.value.ProvidersVo
import com.solum.xplain.core.portfolio.CoreProductType
import com.solum.xplain.xm.workflow.state.PricingSlotTradeWithProviders
import groovy.json.JsonOutput
import org.bson.types.ObjectId
import spock.lang.Specification

class VdDashboardStateMinifierTest extends Specification {
  def "deflateToCompactTrades and inflateFromCompactTrades round-trip"() {
    given:
    def trade = newTrade()
    def original = [trade]

    when:
    def compressed = deflateToCompactTrades(original)
    def restored = inflateFromCompactTrades(compressed)

    then:
    restored == original
  }

  def "deflateToCompactTrades and inflateFromCompactTrades round-trip (big object should be small binary)"() {
    given:
    def allTrades = new ArrayList<PricingSlotTradeWithProviders>()
    // 200 bytes * 200k trades = 40,000,000 bytes = ~40MB in JSON
    def guesstimateByteSize = 0
    for (int i = 0; i < 200_000; i++) {
      // new trades are constantly generated to get *different* ObjectId values.
      // this is important as it may affect compression ratio.
      var trade = newTrade()
      guesstimateByteSize += JsonOutput.toJson(trade).getBytes().size()
      allTrades.add(trade)
    }

    when:
    def compressed = deflateToCompactTrades(allTrades)
    def restored = inflateFromCompactTrades(compressed)

    then:
    guesstimateByteSize > 20_000_000 // our guesstimate should be at least 20MB
    compressed.size() < 2_000_000 // less than 2MB for 200k trades
    restored.size() == allTrades.size()
  }

  private static def PricingSlotTradeWithProviders newTrade() {
    new PricingSlotTradeWithProviders(
      new IpvDataGroupVo(ObjectId.get().toHexString(), "testDesc"),
      PricingSlot.LDN_1200,
      ObjectId.get().toHexString(),
      ObjectId.get().toHexString(),
      CoreProductType.CAP_FLOOR,
      new ProvidersVo("prim", "sec", "third", "quart")
      )
  }
}
