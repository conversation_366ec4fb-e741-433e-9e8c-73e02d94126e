package com.solum.xplain.xm.tasks

import static com.solum.xplain.core.common.EntityId.entityId
import static com.solum.xplain.core.lock.XplainLock.TASK_LOCK_ID
import static io.atlassian.fugue.Either.right
import static org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.csrf
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post

import com.fasterxml.jackson.databind.ObjectMapper
import com.solum.xplain.core.common.RequestPathVariablesSupport
import com.solum.xplain.core.lock.DefaultLockingInterceptor
import com.solum.xplain.core.lock.LockingSupport
import com.solum.xplain.core.lock.XplainLock
import com.solum.xplain.core.lock.XplainLockResolver
import com.solum.xplain.shared.datagrid.ClusterLock
import com.solum.xplain.xm.helpers.MockMvcConfiguration
import com.solum.xplain.xm.tasks.enums.IpvTaskExecutionRerunType
import com.solum.xplain.xm.tasks.form.IpvTaskExecutionRerunRequest
import com.solum.xplain.xm.tasks.service.IpvTaskExecutionService
import com.solum.xplain.xm.tasks.service.IpvTaskReRunExecutionService
import io.atlassian.fugue.Either
import java.util.function.Supplier
import org.spockframework.spring.SpringBean
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.http.MediaType
import org.springframework.test.web.servlet.MockMvc
import spock.lang.Specification
import spock.lang.Unroll

@Unroll
@MockMvcConfiguration
@WebMvcTest(controllers = [IpvTasksController])
class IpvTasksControllerLockingTest extends Specification {
  @Autowired
  private MockMvc mockMvc

  @SpringBean
  private IpvTaskDefinitionsControllerService service = Mock()

  @SpringBean
  private IpvTaskExecutionService executionService = Mock()

  @SpringBean
  private IpvTaskReRunExecutionService reRunExecutionService = Mock()

  @SpringBean
  RequestPathVariablesSupport requestPathVariablesSupport = new RequestPathVariablesSupport()

  @SpringBean
  XplainLockResolver lockResolver = new XplainLockResolver(requestPathVariablesSupport)

  @SpringBean
  LockingSupport lockingSupport = Mock()

  @SpringBean
  DefaultLockingInterceptor lockingInterceptor = new DefaultLockingInterceptor(lockResolver, lockingSupport, new ObjectMapper())

  def "should lock when cancelling own task"() {
    given:
    def mockLock = Mock(ClusterLock)

    when:
    def results = mockMvc.perform(post("/ipv-tasks/executions/{id}/cancel", "taskId")
      .with(csrf())
      .contentType(MediaType.APPLICATION_JSON))
      .andReturn()

    then: "first of all we lock"
    1 * lockingSupport.tryLock(XplainLock.newOf(TASK_LOCK_ID, "taskId")) >> right(mockLock)

    and: "then we call the services"
    1 * executionService.cancelOwnTask(_, "taskId") >> right(entityId("taskId"))

    and: "then we unlock"
    1 * lockingSupport.releaseLock(mockLock)

    and: "we get an OK response"
    results != null
    results.getResponse().getStatus() == 200
  }

  def "should lock when cancelling any task"() {
    given:
    def mockLock = Mock(ClusterLock)

    when:
    def results = mockMvc.perform(post("/ipv-tasks/executions/{id}/cancel/admin", "taskId")
      .with(csrf())
      .contentType(MediaType.APPLICATION_JSON))
      .andReturn()

    then: "first of all we lock"
    1 * lockingSupport.tryLock(XplainLock.newOf(TASK_LOCK_ID, "taskId")) >> right(mockLock)

    and: "then we call the services"
    1 * executionService.cancelTask(_, "taskId") >> right(entityId("taskId"))

    and: "then we unlock"
    1 * lockingSupport.releaseLock(mockLock)

    and: "we get an OK response"
    results != null
    results.getResponse().getStatus() == 200
  }

  def "should lock when starting resolution of a list of tasks"() {
    when:
    def results = mockMvc.perform(post("/ipv-tasks/executions/start")
      .with(csrf())
      .contentType(MediaType.APPLICATION_JSON)
      .content('{"taskIds": ["taskId1", "taskId2"]}'))
      .andReturn()

    then: "we call the services inside a lock block"
    1 * lockingSupport.doEitherWithLocks([XplainLock.newOf(TASK_LOCK_ID, "taskId1"), XplainLock.newOf(TASK_LOCK_ID, "taskId2")], _ as Supplier) >> { List<?> args ->
      (args[1] as Supplier<Either<?, ?>>).get() as Either<?, ?>
    }
    1 * executionService.startTasksProgress(_, ["taskId1", "taskId2"]) >> right([entityId("taskId1"), entityId("taskId2")])

    and: "we get an OK response"
    results != null
    results.getResponse().getStatus() == 200
  }

  def "should lock when submitting resolution of a list of tasks"() {
    when:
    def results = mockMvc.perform(post("/ipv-tasks/executions/submit")
      .with(csrf())
      .contentType(MediaType.APPLICATION_JSON)
      .content('{"taskIds": ["taskId1", "taskId2"]}'))
      .andReturn()

    then: "we call the services inside a lock block"
    1 * lockingSupport.doEitherWithLocks([XplainLock.newOf(TASK_LOCK_ID, "taskId1"), XplainLock.newOf(TASK_LOCK_ID, "taskId2")], _ as Supplier) >> { List<?> args ->
      (args[1] as Supplier<Either<?, ?>>).get() as Either<?, ?>
    }
    1 * executionService.submitTasksResolution(_, ["taskId1", "taskId2"]) >> right([entityId("taskId1"), entityId("taskId2")])

    and: "we get an OK response"
    results != null
    results.getResponse().getStatus() == 200
  }

  def "should lock when starting approval of a list of tasks"() {
    when:
    def results = mockMvc.perform(post("/ipv-tasks/executions/start-verify")
      .with(csrf())
      .contentType(MediaType.APPLICATION_JSON)
      .content('{"taskIds": ["taskId1", "taskId2"]}'))
      .andReturn()

    then: "we call the services inside a lock block"
    1 * lockingSupport.doEitherWithLocks([XplainLock.newOf(TASK_LOCK_ID, "taskId1"), XplainLock.newOf(TASK_LOCK_ID, "taskId2")], _ as Supplier) >> { List<?> args ->
      (args[1] as Supplier<Either<?, ?>>).get() as Either<?, ?>
    }
    1 * executionService.startTasksApproval(_, ["taskId1", "taskId2"]) >> right([entityId("taskId1"), entityId("taskId2")])

    and: "we get an OK response"
    results != null
    results.getResponse().getStatus() == 200
  }

  def "should lock when submitting approval of a list of tasks"() {
    when:
    def results = mockMvc.perform(post("/ipv-tasks/executions/verify")
      .with(csrf())
      .contentType(MediaType.APPLICATION_JSON)
      .content('{"taskIds": ["taskId1", "taskId2"]}'))
      .andReturn()

    then: "we call the services inside a lock block"
    1 * lockingSupport.doEitherWithLocks([XplainLock.newOf(TASK_LOCK_ID, "taskId1"), XplainLock.newOf(TASK_LOCK_ID, "taskId2")], _ as Supplier) >> { List<?> args ->
      (args[1] as Supplier<Either<?, ?>>).get() as Either<?, ?>
    }
    1 * executionService.submitTasksApproval(_, ["taskId1", "taskId2"]) >> right([entityId("taskId1"), entityId("taskId2")])

    and: "we get an OK response"
    results != null
    results.getResponse().getStatus() == 200
  }

  def "should lock when re-running a list of tasks"() {
    when:
    def results = mockMvc.perform(post("/ipv-tasks/executions/rerun")
      .with(csrf())
      .contentType(MediaType.APPLICATION_JSON)
      .content('{"taskIds": ["taskId1", "taskId2"], "type": "DELETE", "revaluatePortfolios": true}'))
      .andReturn()

    then: "we call the services inside a lock block"
    1 * lockingSupport.doEitherWithLocks([XplainLock.newOf(TASK_LOCK_ID, "taskId1"), XplainLock.newOf(TASK_LOCK_ID, "taskId2")], _ as Supplier) >> { List<?> args ->
      (args[1] as Supplier<Either<?, ?>>).get() as Either<?, ?>
    }
    1 * reRunExecutionService.rerunTasks(_, new IpvTaskExecutionRerunRequest(["taskId1", "taskId2"], IpvTaskExecutionRerunType.DELETE, true)) >> right([entityId("taskId1"), entityId("taskId2")])

    and: "we get an OK response"
    results != null
    results.getResponse().getStatus() == 200
  }
}
