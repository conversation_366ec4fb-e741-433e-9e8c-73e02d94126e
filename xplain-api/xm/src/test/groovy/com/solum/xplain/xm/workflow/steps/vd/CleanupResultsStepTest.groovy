package com.solum.xplain.xm.workflow.steps.vd

import com.solum.xplain.workflow.service.StepStateOps
import com.solum.xplain.xm.excmngmt.processipv.IpvTradeResultOverlayQueryRepository
import com.solum.xplain.xm.workflow.state.VdDashboardContext
import com.solum.xplain.xm.workflow.state.VdDashboardState
import java.util.stream.Stream
import org.bson.types.ObjectId
import spock.lang.Specification

class CleanupResultsStepTest extends Specification {
  StepStateOps<VdDashboardState, VdDashboardContext> stepStateOps = Mock()
  IpvTradeResultOverlayQueryRepository repository = Mock()

  CleanupResultsStep step = new CleanupResultsStep(repository)

  def "should find duplicate IDs and submit bulk delete operations"() {
    given:
    def businessKey = "urn:dashboard:dashboard123"
    def dashboardId = "dashboard123"
    def duplicateId1 = ObjectId.get()
    def duplicateId2 = ObjectId.get()
    def duplicateId3 = ObjectId.get()

    stepStateOps.getRootBusinessKey() >> businessKey
    repository.findDuplicateIds(dashboardId) >> Stream.of(duplicateId1, duplicateId2, duplicateId3)

    when:
    step.runStep(stepStateOps)

    then:
    1 * stepStateOps.submitBulkDataModification(_) >> { args ->
      def bulkDelete = args[0] as IpvTradeResultOverlayBulkDelete
      assert bulkDelete.objectIds().size() == 3
      assert bulkDelete.objectIds().contains(duplicateId1)
      assert bulkDelete.objectIds().contains(duplicateId2)
      assert bulkDelete.objectIds().contains(duplicateId3)
    }
  }

  def "should handle empty duplicate IDs stream gracefully"() {
    given:
    def businessKey = "urn:dashboard:dashboard123"
    def dashboardId = "dashboard123"

    stepStateOps.getRootBusinessKey() >> businessKey
    repository.findDuplicateIds(dashboardId) >> Stream.empty()

    when:
    step.runStep(stepStateOps)

    then:
    0 * stepStateOps.submitBulkDataModification(_)
    noExceptionThrown()
  }

  def "should handle large number of duplicates by chunking"() {
    given:
    def businessKey = "urn:dashboard:dashboard123"
    def dashboardId = "dashboard123"
    def duplicateIds = (1..2500).collect { ObjectId.get() }

    stepStateOps.getRootBusinessKey() >> businessKey
    repository.findDuplicateIds(dashboardId) >> duplicateIds.stream()

    when:
    step.runStep(stepStateOps)

    then:
    // Should be chunked into multiple bulk operations (default chunk size is 1000)
    3 * stepStateOps.submitBulkDataModification(_) >> { args ->
      def bulkDelete = args[0] as IpvTradeResultOverlayBulkDelete
      assert bulkDelete.objectIds().size() <= 1000
      assert duplicateIds.containsAll(bulkDelete.objectIds())
    }
  }

  def "should extract dashboard ID from business key correctly"() {
    given:
    def businessKey = "urn:dashboard:myDashboard456"
    def expectedDashboardId = "myDashboard456"
    stepStateOps.getRootBusinessKey() >> businessKey

    when:
    step.runStep(stepStateOps)

    then:
    1 * repository.findDuplicateIds(expectedDashboardId) >> Stream.empty()
    0 * stepStateOps.submitBulkDataModification(_)
  }
}
