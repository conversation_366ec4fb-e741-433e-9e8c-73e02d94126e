package com.solum.xplain.xm.tasks

import static com.solum.xplain.core.curvegroup.instrument.CoreAssetClass.INFLATION_RATE
import static groovy.json.JsonOutput.toJson
import static org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.csrf
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.put

import com.solum.xplain.core.common.EntityId
import com.solum.xplain.core.curvegroup.instrument.CoreAssetClass
import com.solum.xplain.core.instrument.InstrumentTypeResolver
import com.solum.xplain.core.instrument.InstrumentTypeResolverHelper
import com.solum.xplain.core.lock.LockingSupport
import com.solum.xplain.xm.helpers.MockMvcConfiguration
import com.solum.xplain.xm.tasks.service.MdTaskExecutionService
import org.bson.types.ObjectId
import org.spockframework.spring.SpringBean
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.http.MediaType
import org.springframework.test.web.servlet.MockMvc
import spock.lang.Specification
import spock.lang.Unroll

@Unroll
@MockMvcConfiguration
@WebMvcTest(controllers = [TasksController])
class TasksControllerTest extends Specification {

  @Autowired
  private MockMvc mockMvc

  @SpringBean
  private TaskDefinitionControllerService service = Mock()

  @SpringBean
  private MdTaskExecutionService executionService = Mock()

  @SpringBean
  LockingSupport lockingSupport = Mock()

  @SpringBean
  private InstrumentTypeResolver typeResolver = InstrumentTypeResolverHelper.coreResolver()

  def "should create task definition with response #responseBody and code #code "() {
    setup:
    service.update(_) >> EntityId.entityId("1")

    def results = mockMvc.perform(put("/tasks/definitions")
      .with(csrf())
      .content(toJson(form))
      .contentType(MediaType.APPLICATION_JSON))
      .andReturn()

    expect:
    with(results.getResponse()) {
      getStatus() == code
      getContentAsString().indexOf(responseBody) >= 0
    }

    where:
    form                                                                                   | code | responseBody
    taskForm({ c -> c })                                                                   | 200  | """{"id":"1"}"""
    taskForm { c -> c.remove("type") }                                                     | 412  | "NotNull.tasksDefinitionForm.type"
    taskForm { c -> c.remove("granularityByAssetClassType") }                              | 412  | "NotNull.tasksDefinitionForm.granularityByAssetClassType"
    taskForm { c -> c.remove("granularityByRate") }                                        | 412  | "NotNull.tasksDefinitionForm.granularityByRate"
    taskForm { c -> c.remove("granularityBySector") }                                      | 412  | "NotNull.tasksDefinitionForm.granularityBySector"
    taskForm { c -> c.remove("granularityByInstrument") }                                  | 412  | "NotNull.tasksDefinitionForm.granularityByInstrument"
    taskForm { c -> c.remove("granularityByFxCcyPair") }                                   | 412  | "NotNull.tasksDefinitionForm.granularityByFxCcyPair"
    taskForm { c -> c.remove("teams") }                                                    | 412  | "NotEmpty.tasksDefinitionForm.teams"
    taskForm { c -> c.put("teams", [assetClassTaskTeamForm(INFLATION_RATE, { a -> a })]) } | 412  | "Task definition must contain all asset classes"
  }

  def taskForm(Closure c) {
    [
      type                       : "PRELIMINARY",
      teams                      : allExceptionManagementAssetClasses(),
      overrides                  : [],
      granularityByAssetClassType: "NONE",
      granularityByRate          : "NONE",
      granularityBySector        : "NONE",
      granularityByInstrument    : "NONE",
      granularityByFxCcyPair     : "NONE",

    ].with(true, c)
  }

  def allExceptionManagementAssetClasses() {
    Arrays.stream(CoreAssetClass.values())
      .map({ v -> assetClassTaskTeamForm(v, { c -> c }) })
      .toList()
  }

  def assetClassTaskTeamForm(CoreAssetClass assetClass, Closure c) {
    [
      assetClass     : assetClass,
      resolutionTeams: [ObjectId.get().toHexString()],
      approvalTeams  : [ObjectId.get().toHexString()],
    ].with(true, c)
  }
}
