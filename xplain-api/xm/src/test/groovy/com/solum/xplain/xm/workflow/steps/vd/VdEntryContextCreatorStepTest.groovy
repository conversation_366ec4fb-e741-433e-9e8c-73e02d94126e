package com.solum.xplain.xm.workflow.steps.vd

import com.opengamma.strata.basics.currency.Currency
import com.solum.xplain.core.classifiers.pricingslots.PricingSlot
import com.solum.xplain.core.classifiers.sladeadlines.SlaDeadline
import com.solum.xplain.core.common.versions.BitemporalDate
import com.solum.xplain.core.company.value.ProvidersVo
import com.solum.xplain.core.company.value.SlaDeadlinePortfolioView
import com.solum.xplain.core.portfolio.CoreProductType
import com.solum.xplain.core.users.UserBuilder
import com.solum.xplain.xm.dashboards.CachingDashboardTradesService
import com.solum.xplain.xm.dashboards.entity.filter.VdExceptionManagementPortfolioFilter
import com.solum.xplain.xm.excmngmt.data.EntryResultBreakByProvider
import com.solum.xplain.xm.excmngmt.enums.IpvExceptionManagementPhase
import com.solum.xplain.xm.excmngmt.processipv.CachingBreakTestHistoryService
import com.solum.xplain.xm.excmngmt.processipv.CachingBreakTestService
import com.solum.xplain.xm.excmngmt.processipv.CachingPortfolioIpvConverterService
import com.solum.xplain.xm.excmngmt.processipv.CachingPortfolioSettingsService
import com.solum.xplain.xm.excmngmt.processipv.CachingProviderDataService
import com.solum.xplain.xm.excmngmt.processipv.CachingXplainCalculationDataService
import com.solum.xplain.xm.excmngmt.processipv.converter.PortfolioIpvValueConverter
import com.solum.xplain.xm.excmngmt.processipv.data.ProviderDataWithGreeks
import com.solum.xplain.xm.excmngmt.processipv.data.Trade
import com.solum.xplain.xm.excmngmt.processipv.data.TradeResultBreak
import com.solum.xplain.xm.excmngmt.processipv.value.IpvBreakTestCalculations
import com.solum.xplain.xm.excmngmt.processipv.value.TradeBreakCalculator
import com.solum.xplain.xm.excmngmt.processipv.value.TradeBreakScalingData
import com.solum.xplain.xm.excmngmt.processipv.value.TradeDataBreakCalculatorSupplier
import com.solum.xplain.xm.workflow.state.PricingSlotTradeWithProviders
import com.solum.xplain.xm.workflow.state.VdDashboardContext
import com.solum.xplain.xm.workflow.state.VdDashboardState
import com.solum.xplain.xm.workflow.state.VdEntryContextCreator
import java.time.LocalDate
import spock.lang.Specification

class VdEntryContextCreatorStepTest extends Specification {
  def providersNoXplain = new ProvidersVo("PRI", "SEC", "TER", "QUA")

  CachingPortfolioIpvConverterService cachingPortfolioIpvConverterService
  CachingDashboardTradesService dashboardTradesService
  CachingPortfolioSettingsService cachingPortfolioSettingsService
  CachingProviderDataService cachingProviderDataService
  CachingXplainCalculationDataService cachingXplainCalculationDataService
  CachingBreakTestService cachingBreakTestService
  CachingBreakTestHistoryService cachingBreakTestHistoryService

  VdEntryContextCreator step

  def setup() {
    cachingPortfolioIpvConverterService = Mock(CachingPortfolioIpvConverterService)
    dashboardTradesService = Mock(CachingDashboardTradesService)
    cachingPortfolioSettingsService = Mock(CachingPortfolioSettingsService)
    cachingProviderDataService = Mock(CachingProviderDataService)
    cachingXplainCalculationDataService = Mock(CachingXplainCalculationDataService)
    cachingBreakTestService = Mock(CachingBreakTestService)
    cachingBreakTestHistoryService = Mock(CachingBreakTestHistoryService)

    step = new VdEntryContextCreator(
      cachingPortfolioIpvConverterService,
      dashboardTradesService,
      cachingPortfolioSettingsService,
      cachingProviderDataService,
      cachingXplainCalculationDataService,
      cachingBreakTestService,
      cachingBreakTestHistoryService,
      new TradeDataBreakCalculatorSupplier()
      )
  }

  def "it should return list of unbroken and clean trades"() {
    given:
    def stateDate = BitemporalDate.newOfNow()
    def dashboardContext = new VdDashboardContext(
      VdExceptionManagementPortfolioFilter.empty(),
      [
        SlaDeadlinePortfolioView.newOf("portfolioId1", "externalPortfolioId1", "companyId", "externalCompanyId", "entityId", "externalEntityId", SlaDeadline.LDN_1600)
      ],
      stateDate,
      UserBuilder.user())
    def ipvBreakTestCalculations = Mock(IpvBreakTestCalculations)
    def portfolioIpvValueConverter = Mock(PortfolioIpvValueConverter)

    def dashboardState = new VdDashboardState(
      trades: [
        new PricingSlotTradeWithProviders(null, PricingSlot.LDN_1600, "portfolioId1", "tradeId1", CoreProductType.CAP_FLOOR, providersNoXplain),
        new PricingSlotTradeWithProviders(null, PricingSlot.LDN_1600, "portfolioId1", "tradeId2", CoreProductType.FXOPT, providersNoXplain),
      ],
      calculationNeeded: true
      )

    dashboardTradesService.dashboardTrade("portfolioId1", "tradeId1", stateDate) >> new Trade(key: "tradeId1", currency: "EUR")
    dashboardTradesService.dashboardTrade("portfolioId1", "tradeId2", stateDate) >> new Trade(key: "tradeId2", currency: "EUR")
    cachingProviderDataService.providerData(null, _ as Trade, _ as BitemporalDate) >> [ProviderDataWithGreeks.emptyWithProvider("PRI")]
    cachingPortfolioIpvConverterService.getConverter(null, null, null, _ as BitemporalDate) >> portfolioIpvValueConverter
    portfolioIpvValueConverter.convertScalingData(_ as TradeBreakScalingData, _ as Currency) >> null
    portfolioIpvValueConverter.xmCurrency(_ as Currency) >> Optional.of(Currency.EUR)
    cachingBreakTestService.getBreakTestCalculations(IpvExceptionManagementPhase.OVERLAY_1, stateDate) >> ipvBreakTestCalculations
    ipvBreakTestCalculations.filteredFor(_ as Trade) >> ipvBreakTestCalculations
    ipvBreakTestCalculations.processCalc(_ as TradeBreakCalculator, _ as LocalDate) >> [
      new TradeResultBreak(breakTestName: "test1", threshold: 10, providerValue: new EntryResultBreakByProvider(triggered: true, value: 100, triggeredThreshold: 100)),
      new TradeResultBreak(breakTestName: "test2", threshold: 10, providerValue: new EntryResultBreakByProvider(triggered: false, value: 100, triggeredThreshold: 100))
    ]

    when:
    def result = step.subprocessContext(dashboardState, dashboardContext).toList()

    then:
    result.size() == 2
    result[0].brokenTrades.size() == 1
    result[0].cleanTrades.size() == 1
  }
}
