package com.solum.xplain.xm.tasks

import static com.solum.xplain.core.common.EntityId.entityId
import static com.solum.xplain.core.lock.XplainLock.TASK_LOCK_ID
import static io.atlassian.fugue.Either.right
import static org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.csrf
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post

import com.fasterxml.jackson.databind.ObjectMapper
import com.solum.xplain.core.common.RequestPathVariablesSupport
import com.solum.xplain.core.lock.DefaultLockingInterceptor
import com.solum.xplain.core.lock.LockingSupport
import com.solum.xplain.core.lock.XplainLock
import com.solum.xplain.core.lock.XplainLockResolver
import com.solum.xplain.shared.datagrid.ClusterLock
import com.solum.xplain.xm.helpers.MockMvcConfiguration
import com.solum.xplain.xm.tasks.enums.TaskExceptionManagementType
import com.solum.xplain.xm.tasks.service.InstrumentTaskExecutionService
import com.solum.xplain.xm.tasks.service.MdTaskExecutionService
import io.atlassian.fugue.Either
import java.util.function.Supplier
import org.spockframework.spring.SpringBean
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.http.MediaType
import org.springframework.test.web.servlet.MockMvc
import spock.lang.Specification
import spock.lang.Unroll

@Unroll
@MockMvcConfiguration
@WebMvcTest(controllers = [TasksController])
class TasksControllerLockingTest extends Specification {
  @Autowired
  private MockMvc mockMvc

  @SpringBean
  private TaskDefinitionControllerService service = Mock()

  @SpringBean
  private MdTaskExecutionService executionService = Mock()

  @SpringBean
  private InstrumentTaskExecutionService instrumentExecutionService = Mock()

  @SpringBean
  RequestPathVariablesSupport requestPathVariablesSupport = new RequestPathVariablesSupport()

  @SpringBean
  XplainLockResolver lockResolver = new XplainLockResolver(requestPathVariablesSupport)

  @SpringBean
  LockingSupport lockingSupport = Mock()

  @SpringBean
  DefaultLockingInterceptor lockingInterceptor = new DefaultLockingInterceptor(lockResolver, lockingSupport, new ObjectMapper())

  def "should lock when cancelling own task"() {
    given:
    def mockLock = Mock(ClusterLock)

    when:
    def results = mockMvc.perform(post("/tasks/executions/{type}/{id}/cancel", "OVERLAY", "taskId")
      .with(csrf())
      .contentType(MediaType.APPLICATION_JSON))
      .andReturn()

    then: "first of all we lock"
    1 * lockingSupport.tryLock(XplainLock.newOf(TASK_LOCK_ID, "taskId")) >> right(mockLock)

    and: "then we call the services"
    1 * executionService.getTaskService(TaskExceptionManagementType.OVERLAY) >> instrumentExecutionService
    1 * instrumentExecutionService.cancelOwnTask(_, "taskId") >> right(entityId("taskId"))

    and: "then we unlock"
    1 * lockingSupport.releaseLock(mockLock)

    and: "we get an OK response"
    results != null
    results.getResponse().getStatus() == 200
  }

  def "should lock when cancelling any task"() {
    given:
    def mockLock = Mock(ClusterLock)

    when:
    def results = mockMvc.perform(post("/tasks/executions/{type}/{id}/cancel/admin", "OVERLAY", "taskId")
      .with(csrf())
      .contentType(MediaType.APPLICATION_JSON))
      .andReturn()

    then: "first of all we lock"
    1 * lockingSupport.tryLock(XplainLock.newOf(TASK_LOCK_ID, "taskId")) >> right(mockLock)

    and: "then we call the services"
    1 * executionService.getTaskService(TaskExceptionManagementType.OVERLAY) >> instrumentExecutionService
    1 * instrumentExecutionService.cancelTask(_, "taskId") >> right(entityId("taskId"))

    and: "then we unlock"
    1 * lockingSupport.releaseLock(mockLock)

    and: "we get an OK response"
    results != null
    results.getResponse().getStatus() == 200
  }

  def "should lock when starting resolution of a list of tasks"() {
    when:
    def results = mockMvc.perform(post("/tasks/executions/{type}/start", "OVERLAY")
      .with(csrf())
      .contentType(MediaType.APPLICATION_JSON)
      .content('{"taskIds": ["taskId1", "taskId2"]}'))
      .andReturn()

    then: "we call the services inside a lock block"
    1 * lockingSupport.doEitherWithLocks([XplainLock.newOf(TASK_LOCK_ID, "taskId1"), XplainLock.newOf(TASK_LOCK_ID, "taskId2")], _ as Supplier) >> { List<?> args ->
      (args[1] as Supplier<Either<?, ?>>).get() as Either<?, ?>
    }
    1 * executionService.getTaskService(TaskExceptionManagementType.OVERLAY) >> instrumentExecutionService
    1 * instrumentExecutionService.startTasksProgress(_, ["taskId1", "taskId2"]) >> right([entityId("taskId1"), entityId("taskId2")])

    and: "we get an OK response"
    results != null
    results.getResponse().getStatus() == 200
  }

  def "should lock when submitting resolution of a list of tasks"() {
    when:
    def results = mockMvc.perform(post("/tasks/executions/{type}/submit", "OVERLAY")
      .with(csrf())
      .contentType(MediaType.APPLICATION_JSON)
      .content('{"taskIds": ["taskId1", "taskId2"]}'))
      .andReturn()

    then: "we call the services inside a lock block"
    1 * lockingSupport.doEitherWithLocks([XplainLock.newOf(TASK_LOCK_ID, "taskId1"), XplainLock.newOf(TASK_LOCK_ID, "taskId2")], _ as Supplier) >> { List<?> args ->
      (args[1] as Supplier<Either<?, ?>>).get() as Either<?, ?>
    }
    1 * executionService.getTaskService(TaskExceptionManagementType.OVERLAY) >> instrumentExecutionService
    1 * instrumentExecutionService.submitTasksResolution(_, ["taskId1", "taskId2"]) >> right([entityId("taskId1"), entityId("taskId2")])

    and: "we get an OK response"
    results != null
    results.getResponse().getStatus() == 200
  }

  def "should lock when starting approval of a list of tasks"() {
    when:
    def results = mockMvc.perform(post("/tasks/executions/{type}/start-verify", "OVERLAY")
      .with(csrf())
      .contentType(MediaType.APPLICATION_JSON)
      .content('{"taskIds": ["taskId1", "taskId2"]}'))
      .andReturn()

    then: "we call the services inside a lock block"
    1 * lockingSupport.doEitherWithLocks([XplainLock.newOf(TASK_LOCK_ID, "taskId1"), XplainLock.newOf(TASK_LOCK_ID, "taskId2")], _ as Supplier) >> { List<?> args ->
      (args[1] as Supplier<Either<?, ?>>).get() as Either<?, ?>
    }
    1 * executionService.getTaskService(TaskExceptionManagementType.OVERLAY) >> instrumentExecutionService
    1 * instrumentExecutionService.startTasksApproval(_, ["taskId1", "taskId2"]) >> right([entityId("taskId1"), entityId("taskId2")])

    and: "we get an OK response"
    results != null
    results.getResponse().getStatus() == 200
  }

  def "should lock when submitting approval of a list of tasks"() {
    when:
    def results = mockMvc.perform(post("/tasks/executions/{type}/verify", "OVERLAY")
      .with(csrf())
      .contentType(MediaType.APPLICATION_JSON)
      .content('{"taskIds": ["taskId1", "taskId2"]}'))
      .andReturn()

    then: "we call the services inside a lock block"
    1 * lockingSupport.doEitherWithLocks([XplainLock.newOf(TASK_LOCK_ID, "taskId1"), XplainLock.newOf(TASK_LOCK_ID, "taskId2")], _ as Supplier) >> { List<?> args ->
      (args[1] as Supplier<Either<?, ?>>).get() as Either<?, ?>
    }
    1 * executionService.getTaskService(TaskExceptionManagementType.OVERLAY) >> instrumentExecutionService
    1 * instrumentExecutionService.submitTasksApproval(_, ["taskId1", "taskId2"]) >> right([entityId("taskId1"), entityId("taskId2")])

    and: "we get an OK response"
    results != null
    results.getResponse().getStatus() == 200
  }
}
