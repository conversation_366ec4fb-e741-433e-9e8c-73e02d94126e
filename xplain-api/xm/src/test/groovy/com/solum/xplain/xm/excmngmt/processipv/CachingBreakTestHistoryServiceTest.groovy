package com.solum.xplain.xm.excmngmt.processipv

import com.solum.xplain.core.common.daterange.DateRange
import com.solum.xplain.core.common.versions.BitemporalDate
import com.solum.xplain.xm.dashboards.views.DashboardDateView
import com.solum.xplain.xm.excmngmt.enums.IpvExceptionManagementPhase
import com.solum.xplain.xm.excmngmt.processipv.data.Trade
import com.solum.xplain.xm.excmngmt.value.BreakTestHistory
import com.solum.xplain.xm.excmngmt.value.EntryBreakHistory
import io.micrometer.core.instrument.simple.SimpleMeterRegistry
import java.time.LocalDate
import spock.lang.Specification

class CachingBreakTestHistoryServiceTest extends Specification {
  def ipvExceptionManagementDataService = Mock(IpvExceptionManagementDataService)
  def ipvExceptionManagementCalculationRepository = Mock(IpvExceptionManagementCalculationRepository)
  def service = new CachingBreakTestHistoryService(ipvExceptionManagementDataService, ipvExceptionManagementCalculationRepository, new SimpleMeterRegistry())

  def "getBreakTestHistory calls repository only once for the same portfolio ID"() {
    given:
    def stateDate = BitemporalDate.newOfNow()
    def tMinus1 = LocalDate.now().minusDays(2)
    def ddv = new DashboardDateView("previousDashboardId", DateRange.newOf(tMinus1, tMinus1))
    def history = [new EntryBreakHistory("trade1", [new BreakTestHistory("T1", 5), new BreakTestHistory("T2", 7)])]
    1 * ipvExceptionManagementDataService.previousDashboard("portfolioId", LocalDate.now()) >> Optional.of(ddv)
    1 * ipvExceptionManagementCalculationRepository.portfolioDashboardBreaksHistory("previousDashboardId", IpvExceptionManagementPhase.OVERLAY_1, "portfolioId") >> history


    when:
    def first = service.previousDashboardBreaks(new Trade(key: "trade1", portfolioId: "portfolioId"), IpvExceptionManagementPhase.OVERLAY_1, stateDate)
    def second = service.previousDashboardBreaks(new Trade(key: "trade1", portfolioId: "portfolioId"), IpvExceptionManagementPhase.OVERLAY_1, stateDate)
    def third = service.previousDashboardBreaks(new Trade(key: "tradeUnknown", portfolioId: "portfolioId"), IpvExceptionManagementPhase.OVERLAY_1, stateDate)

    then:
    first == history[0]
    second == history[0]
    third == null
  }

  def "getBreakTestHistory handles duplicate records for the same trade gracefully"() {
    given:
    def stateDate = BitemporalDate.newOfNow()
    def tMinus1 = LocalDate.now().minusDays(2)
    def ddv = new DashboardDateView("previousDashboardId", DateRange.newOf(tMinus1, tMinus1))
    def history = [
      new EntryBreakHistory("trade1", [new BreakTestHistory("T1", 5), new BreakTestHistory("T2", 7)]),
      new EntryBreakHistory("trade1", [new BreakTestHistory("T1", 5), new BreakTestHistory("T2", 7)])
    ]
    1 * ipvExceptionManagementDataService.previousDashboard("portfolioId", LocalDate.now()) >> Optional.of(ddv)
    1 * ipvExceptionManagementCalculationRepository.portfolioDashboardBreaksHistory("previousDashboardId", IpvExceptionManagementPhase.OVERLAY_1, "portfolioId") >> history

    when:
    def first = service.previousDashboardBreaks(new Trade(key: "trade1", portfolioId: "portfolioId"), IpvExceptionManagementPhase.OVERLAY_1, stateDate)

    then:
    first == history[0]
  }
}
