package com.solum.xplain.calibration.volatility

import static com.opengamma.strata.basics.currency.Currency.EUR
import static com.opengamma.strata.basics.currency.Currency.USD
import static com.opengamma.strata.basics.currency.CurrencyPair.of
import static com.solum.xplain.core.curvegroup.volatilityfx.entity.CurveGroupFxVolatilityNodeBuilder.sampleFxOptVolNode
import static com.solum.xplain.core.market.MarketDataSample.ogMarketData
import static org.hamcrest.Matchers.closeTo
import static spock.util.matcher.HamcrestSupport.that

import com.opengamma.strata.basics.ReferenceData
import com.opengamma.strata.basics.currency.CurrencyPair
import com.opengamma.strata.collect.array.DoubleArray
import com.opengamma.strata.market.curve.ConstantNodalCurve
import com.opengamma.strata.market.curve.InterpolatedNodalCurve
import com.opengamma.strata.pricer.fxopt.BlackFxOptionFlatVolatilities
import com.opengamma.strata.pricer.fxopt.BlackFxOptionSmileVolatilities
import com.opengamma.strata.pricer.fxopt.FxOptionVolatilitiesName
import com.solum.xplain.core.common.AuditUserMapperImpl
import com.solum.xplain.core.curvegroup.volatilityfx.CurveGroupFxVolatilityMapper
import com.solum.xplain.core.curvegroup.volatilityfx.CurveGroupFxVolatilityMapperImpl
import com.solum.xplain.core.curvegroup.volatilityfx.entity.CurveGroupFxVolatilityBuilder
import com.solum.xplain.core.curvemarket.node.NodeInstrumentWrapper
import com.solum.xplain.core.curvemarket.node.ValidNodesFilter
import com.solum.xplain.core.error.Error
import com.solum.xplain.core.market.MarketDataSample
import org.spockframework.spring.SpringBean
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.test.context.ContextConfiguration
import spock.lang.Specification
import spock.lang.Unroll

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.NONE)
@ContextConfiguration(classes = [CurveGroupFxVolatilityMapperImpl.class, AuditUserMapperImpl.class])
class FxVolatilitiesCalibrationTest extends Specification {
  def static final CCY_PAIRS = [of(EUR, USD)] as Set
  @SpringBean
  ReferenceData referenceData = ReferenceData.standard()
  @Autowired
  CurveGroupFxVolatilityMapper curveGroupFxVolatilityMapper

  def "should construct strata FX Option Volatility"() {
    setup:
    def fxVolatility = new CurveGroupFxVolatilityBuilder()
      .nodes([sampleFxOptVolNode()] as Set)
      .build()

    when:
    def result = FxVolatilitiesCalibration.newOf(fxVolatility, ogMarketData(), ValidNodesFilter.EMPTY_FILTER, curveGroupFxVolatilityMapper).calibrate(CCY_PAIRS)

    then:
    def res = result.get(CurrencyPair.parse("EUR/USD"))
    res.isRight()
    def blackVol = (BlackFxOptionSmileVolatilities) res.getOrNull()
    blackVol.getName() == FxOptionVolatilitiesName.of("EUR/USD")
    blackVol.getCurrencyPair() == of(EUR, USD)
    blackVol.getValuationDate() == MarketDataSample.VAL_DT
    blackVol.getSmile().delta == DoubleArray.of(0.01, 0.02)
    blackVol.getSmile().expiries.size() == 1
    that blackVol.getSmile().expiries.get(0), closeTo(1.0d, 0.00001d)
    blackVol.getSmile().volatilityTerm.size() == 1
    blackVol.getSmile().volatilityTerm[0].volatility.size() == 5
    that blackVol.getSmile().volatilityTerm[0].volatility.get(0), closeTo(0.025d, 0.00001d)
    that blackVol.getSmile().volatilityTerm[0].volatility.get(1), closeTo(0.035d, 0.00001d)
    that blackVol.getSmile().volatilityTerm[0].volatility.get(2), closeTo(0.01d, 0.00001d)
    that blackVol.getSmile().volatilityTerm[0].volatility.get(3), closeTo(0.105d, 0.00001d)
    that blackVol.getSmile().volatilityTerm[0].volatility.get(4), closeTo(0.075d, 0.00001d)
  }

  def "should construct strata FX Option Volatility [Flat Constant] with ATM only if deltas are not defined and only 1 expiry point"() {
    setup:
    def vol = sampleFxOptVolNode()
    vol.delta1 = null
    vol.delta2 = null
    def fxVolatility = new CurveGroupFxVolatilityBuilder()
      .nodes([vol] as Set)
      .build()

    when:
    def result = FxVolatilitiesCalibration.newOf(fxVolatility, ogMarketData(), ValidNodesFilter.EMPTY_FILTER, curveGroupFxVolatilityMapper).calibrate(CCY_PAIRS)

    then:
    def res = result.get(CurrencyPair.parse("EUR/USD"))
    res.isRight()
    def blackVol = (BlackFxOptionFlatVolatilities) res.getOrNull()
    blackVol.getName() == FxOptionVolatilitiesName.of("EUR/USD")
    blackVol.getCurrencyPair() == of(EUR, USD)
    blackVol.getValuationDate() == MarketDataSample.VAL_DT
    blackVol.getCurve() instanceof ConstantNodalCurve
    def expiry = ((ConstantNodalCurve) blackVol.getCurve()).XValue
    that expiry, closeTo(1.0d, 0.00001d)
    ((ConstantNodalCurve) blackVol.getCurve()).YValue == 0.01d
  }

  def "should construct strata FX Option Volatility [Flat Interpolated] with ATM only if deltas are not defined and 2 expiry points"() {
    setup:
    // payment date = 2018-11-13, expiry date = 2018-11-09
    def vol1 = sampleFxOptVolNode("1Y")
    vol1.delta1 = null
    vol1.delta2 = null
    // payment date = 2019-11-13, expiry date = 2019-11-11
    def vol2 = sampleFxOptVolNode("2Y")
    vol2.delta1 = null
    vol2.delta2 = null
    def fxVolatility = new CurveGroupFxVolatilityBuilder()
      .nodes([vol1, vol2] as Set)
      .build()

    when:
    def result = FxVolatilitiesCalibration.newOf(fxVolatility, ogMarketData(), ValidNodesFilter.EMPTY_FILTER, curveGroupFxVolatilityMapper).calibrate(CCY_PAIRS)

    then:
    def res = result.get(CurrencyPair.parse("EUR/USD"))
    res.isRight()
    def blackVol = (BlackFxOptionFlatVolatilities) res.getOrNull()
    blackVol.getName() == FxOptionVolatilitiesName.of("EUR/USD")
    blackVol.getCurrencyPair() == of(EUR, USD)
    blackVol.getValuationDate() == MarketDataSample.VAL_DT // T = 2017-11-09, spot date = 2017-11-13
    blackVol.getCurve() instanceof InterpolatedNodalCurve
    def expiries = ((InterpolatedNodalCurve) blackVol.getCurve()).XValues
    that expiries.get(0), closeTo(1.0d, 0.00001d)
    that expiries.get(1), closeTo(2.00548d, 0.00001d)
    ((InterpolatedNodalCurve) blackVol.getCurve()).YValues == DoubleArray.of(0.01d, 0.02d)
  }

  @Unroll
  def "should throw validation error when ticker values empty #d1 #d2 #expiry #message"() {
    setup:
    def vol = sampleFxOptVolNode()
    vol.expiry = expiry
    vol.delta1 = d1
    vol.delta2 = d2
    def fxVolatility = new CurveGroupFxVolatilityBuilder()
      .nodes([vol] as Set)
      .build()

    expect:
    def result = FxVolatilitiesCalibration.newOf(fxVolatility, ogMarketData(), ValidNodesFilter.EMPTY_FILTER, curveGroupFxVolatilityMapper).calibrate(CCY_PAIRS)
    def vols = result[of(EUR, USD)]
    vols.isLeft()
    vols.left().get().reason == Error.CALIBRATION_ERROR
    vols.left().get().description == message

    where:
    d1 | d2 | expiry | message
    1  | 2  | "99Y"  | "Market data required by FX volatility 99Y_EUR/USDV not found"
    99 | 2  | "1Y"   | "Market data required by FX volatility 1Y_EUR/USD99B not found"
    1  | 55 | "1Y"   | "Market data required by FX volatility 1Y_EUR/USD55B not found"
  }

  def "should return empty results when fxVolatility is null"() {
    setup:
    def fxVolatility = null

    when:
    def result = FxVolatilitiesCalibration.newOf(fxVolatility, ogMarketData(), ValidNodesFilter.EMPTY_FILTER, curveGroupFxVolatilityMapper).calibrate(CCY_PAIRS)

    then:
    result.isEmpty()
  }

  def "should remove and log unmapped node"() {
    setup:
    def node1 = sampleFxOptVolNode()
    def node2 = sampleFxOptVolNode("50Y")
    def fxVolatility = new CurveGroupFxVolatilityBuilder()
      .nodes([node1, node2] as Set)
      .build()
    def instrument1 = node1.fxAtmInstrumentDefinition()
    def instrument2 = node2.fxAtmInstrumentDefinition()
    def filter = Mock(ValidNodesFilter)
    1 * filter.filterNodes([
      NodeInstrumentWrapper.of(node1, instrument1),
      NodeInstrumentWrapper.of(node2, instrument2),
    ]) >> [node1]

    when:
    def result = FxVolatilitiesCalibration.newOf(fxVolatility, ogMarketData(), filter, curveGroupFxVolatilityMapper).calibrate(CCY_PAIRS)

    then:
    def res = result.get(CurrencyPair.parse("EUR/USD"))
    res.isRight()
    def blackVol = (BlackFxOptionFlatVolatilities) res.getOrNull()
    blackVol.getName() == FxOptionVolatilitiesName.of("EUR/USD")
    blackVol.getCurrencyPair() == of(EUR, USD)
    blackVol.getValuationDate() == MarketDataSample.VAL_DT
    blackVol.getCurve() instanceof ConstantNodalCurve
  }

  def "should remove and log unmapped delta leaving only second delta"() {
    setup:
    def node1 = sampleFxOptVolNode()
    def fxVolatility = new CurveGroupFxVolatilityBuilder()
      .nodes([node1] as Set)
      .build()
    def instrument1 = node1.fxAtmInstrumentDefinition()
    def filter = Mock(ValidNodesFilter)
    1 * filter.filterNodes([NodeInstrumentWrapper.of(node1, instrument1),]) >> [node1]
    1 * filter.filterNodes([
      NodeInstrumentWrapper.of(node1.fxSkewInstrumentD1BfDefinition(), node1.fxSkewInstrumentD1BfDefinition()),
    ]) >> []
    1 * filter.filterNodes([
      NodeInstrumentWrapper.of(node1.fxSkewInstrumentD1RrDefinition(), node1.fxSkewInstrumentD1RrDefinition()),
    ]) >> []
    1 * filter.filterNodes([
      NodeInstrumentWrapper.of(node1.fxSkewInstrumentD2BfDefinition(), node1.fxSkewInstrumentD2BfDefinition()),
    ]) >> [node1.fxSkewInstrumentD2BfDefinition()]
    1 * filter.filterNodes([
      NodeInstrumentWrapper.of(node1.fxSkewInstrumentD2RrDefinition(), node1.fxSkewInstrumentD2RrDefinition()),
    ]) >> [node1.fxSkewInstrumentD2RrDefinition()]

    when:
    def result = FxVolatilitiesCalibration.newOf(fxVolatility, ogMarketData(), filter, curveGroupFxVolatilityMapper).calibrate(CCY_PAIRS)

    then:
    def res = result.get(CurrencyPair.parse("EUR/USD"))
    res.isRight()
    def blackVol = (BlackFxOptionSmileVolatilities) res.getOrNull()
    blackVol.getName() == FxOptionVolatilitiesName.of("EUR/USD")
    blackVol.getCurrencyPair() == of(EUR, USD)
    blackVol.getValuationDate() == MarketDataSample.VAL_DT
    blackVol.getSmile().delta == DoubleArray.of(0.02d)
    blackVol.getSmile().expiries.size() == 1
    that blackVol.getSmile().expiries.get(0), closeTo(1.0d, 0.00001d)
    blackVol.getSmile().volatilityTerm.size() == 1
    blackVol.getSmile().volatilityTerm[0].volatility.size() == 3
    that blackVol.getSmile().volatilityTerm[0].volatility.get(0), closeTo(0.035d, 0.00001d)
    that blackVol.getSmile().volatilityTerm[0].volatility.get(1), closeTo(0.01d, 0.00001d)
    that blackVol.getSmile().volatilityTerm[0].volatility.get(2), closeTo(0.105d, 0.00001d)
  }

  def "should remove and log unmapped delta and fail when BF is missing but RR is present"() {
    setup:
    def node1 = sampleFxOptVolNode()
    def fxVolatility = new CurveGroupFxVolatilityBuilder()
      .nodes([node1] as Set)
      .build()
    def instrument1 = node1.fxAtmInstrumentDefinition()
    def filter = Mock(ValidNodesFilter)
    1 * filter.filterNodes([NodeInstrumentWrapper.of(node1, instrument1),]) >> [node1]
    1 * filter.filterNodes([
      NodeInstrumentWrapper.of(node1.fxSkewInstrumentD1BfDefinition(), node1.fxSkewInstrumentD1BfDefinition()),
    ]) >> []
    1 * filter.filterNodes([
      NodeInstrumentWrapper.of(node1.fxSkewInstrumentD1RrDefinition(), node1.fxSkewInstrumentD1RrDefinition()),
    ]) >> [node1.fxSkewInstrumentD1RrDefinition()]

    when:
    def result = FxVolatilitiesCalibration.newOf(fxVolatility, ogMarketData(), filter, curveGroupFxVolatilityMapper).calibrate(CCY_PAIRS)

    then:
    def res = result.get(CurrencyPair.parse("EUR/USD"))
    res.isLeft()
    res.left().get().reason == Error.CALIBRATION_ERROR
    res.left().get().description == "The number of Butterfly and Risk Reversal data for FX volatility EUR/USD has to be the same as the number of expiry points / ATM vol data"
  }
}
