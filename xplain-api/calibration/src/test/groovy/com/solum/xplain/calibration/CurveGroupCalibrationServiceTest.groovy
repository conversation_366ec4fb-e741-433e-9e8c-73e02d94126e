package com.solum.xplain.calibration

import static com.solum.xplain.calibration.CurveGroupCalibrationService.CALIBRATION_WITH_WARNINGS
import static com.solum.xplain.core.common.EntityId.entityId
import static com.solum.xplain.core.curvemarket.datasource.MarketDataSourceType.RAW_PRIMARY
import static com.solum.xplain.core.curvemarket.node.ValidNodesFilter.EMPTY_FILTER
import static com.solum.xplain.core.market.MarketDataSample.STATE_DATE
import static com.solum.xplain.core.market.MarketDataSample.VAL_DT
import static com.solum.xplain.core.market.MarketDataSample.marketData
import static com.solum.xplain.core.portfolio.value.CalculationDiscountingType.DISCOUNT_EUR
import static com.solum.xplain.core.portfolio.value.CalculationStrippingType.LIBOR
import static io.atlassian.fugue.Either.left
import static io.atlassian.fugue.Either.right
import static java.time.LocalDate.now

import com.opengamma.strata.basics.ReferenceData
import com.solum.xplain.calibration.bond.BondCurveCalibrationResults
import com.solum.xplain.calibration.bond.BondCurvesCalibrationService
import com.solum.xplain.calibration.credit.CreditCurvesCalibrationService
import com.solum.xplain.calibration.market.CalibrationMarketDataService
import com.solum.xplain.calibration.rates.CalibrationCombinedResultRates
import com.solum.xplain.calibration.rates.CurvesCalibrationService
import com.solum.xplain.calibration.rates.set.CalibrationBundle
import com.solum.xplain.calibration.value.CalibrateCurveForm
import com.solum.xplain.calibration.value.CalibrationOptionsForm
import com.solum.xplain.calibration.value.CalibrationResponse
import com.solum.xplain.calibration.volatility.FxVolatilitiesCalibrationService
import com.solum.xplain.core.audit.AuditEntryService
import com.solum.xplain.core.audit.entity.AuditEntry
import com.solum.xplain.core.common.value.CurveDiscountingForm
import com.solum.xplain.core.curveconfiguration.CurveConfigurationInstrumentResolver
import com.solum.xplain.core.curveconfiguration.CurveConfigurationRepository
import com.solum.xplain.core.curvegroup.curvegroup.CurveGroupRepository
import com.solum.xplain.core.curvegroup.curvegroup.entity.CurveGroupBuilder
import com.solum.xplain.core.error.Error
import com.solum.xplain.core.error.ErrorItem
import org.bson.types.ObjectId
import spock.lang.Specification

class CurveGroupCalibrationServiceTest extends Specification {
  CurveGroupRepository curveGroupRepository = Mock()
  CurvesCalibrationService curvesCalibrationService = Mock()
  CreditCurvesCalibrationService creditCurvesCalibrationService = Mock()
  BondCurvesCalibrationService bondCurvesCalibrationService = Mock()
  FxVolatilitiesCalibrationService fxVolatilitiesCalibrationService = Mock()
  CalibrationMarketDataService marketDataService = Mock()
  CurveConfigurationRepository curveConfigRepository = Mock()
  AuditEntryService auditEntryService = Mock()

  def service = new CurveGroupCalibrationService(
  curveGroupRepository,
  curvesCalibrationService,
  creditCurvesCalibrationService,
  bondCurvesCalibrationService,
  fxVolatilitiesCalibrationService,
  marketDataService,
  curveConfigRepository,
  auditEntryService
  )

  def "should calibrate curve group"() {
    setup:
    def auditEntryId = ObjectId.get().toHexString()
    def curveGroup = CurveGroupBuilder.curveGroup()
    curveGroup.id = "groupId"
    def calibrationSetResult = Mock(CalibrationCombinedResultRates)
    def form = calibrationForm()
    def calibrationBundle = Mock(CalibrationBundle)
    1 * calibrationBundle.discountCurves() >> []
    1 * curveGroupRepository.clearCalibrationResults("groupId") >> right(entityId("groupId"))
    1 * curveGroupRepository.getFull("groupId") >> right(curveGroup)
    1 * curveConfigRepository.curveConfigInstrResolver(STATE_DATE, "configId") >> right(CurveConfigurationInstrumentResolver.empty())
    1 * auditEntryService.newEntry(_) >> { AuditEntry entry -> right(entry.tap { it.setId(auditEntryId) }) }
    auditEntryService.addLogs(auditEntryId, [Error.CALIBRATION_WARNING.entity("Warning")]) >> { String id, List<ErrorItem> errors ->
      return right(Mock(AuditEntry))
    }
    1 * marketDataService.getCalibrationMD(VAL_DT.minusDays(1), _) >> right(marketData())
    1 * marketDataService.validMdNodesFilter(_, RAW_PRIMARY, _, STATE_DATE, _) >> EMPTY_FILTER
    1 * curvesCalibrationService.resolveCalibrationBundles(curveGroup.id, STATE_DATE, form, _) >> [calibrationBundle]
    1 * curvesCalibrationService.calibrateCurves(_, _, [calibrationBundle], _) >> right(calibrationSetResult)
    1 * creditCurvesCalibrationService.calibrateCreditCurves(_, _, calibrationSetResult, _,)
    1 * bondCurvesCalibrationService.calibrate(_, _, _) >> { opts, md, logConsumer ->
      logConsumer.accept([Error.CALIBRATION_WARNING.entity("Warning")])
      new BondCurveCalibrationResults([:], ReferenceData.standard())
    }
    1 * fxVolatilitiesCalibrationService.calibrate(_, _, _)
    1 * curveGroupRepository.updateCurveGroupCalibration(
      curveGroup.id, DISCOUNT_EUR, LIBOR, VAL_DT.minusDays(1), form.getCalibrationOptions().curveConfigMarketStateKey(STATE_DATE)) >> right(entityId("groupId"))
    when:
    def result = service.calibrate("groupId", form, STATE_DATE)

    then:
    result.isRight()
    result.getOrNull() == CalibrationResponse.calibrationResponse(auditEntryId, CALIBRATION_WITH_WARNINGS)
  }

  def "should fail to calibrate curve group"() {
    setup:
    def auditEntryId = ObjectId.get().toHexString()
    def curveGroup = CurveGroupBuilder.curveGroup()
    curveGroup.id = "groupId"
    def form = calibrationForm()
    def calibrationBundle = Mock(CalibrationBundle)
    1 * calibrationBundle.discountCurves() >> []
    1 * curveGroupRepository.clearCalibrationResults("groupId") >> right(entityId("groupId"))
    1 * curveGroupRepository.getFull("groupId") >> right(curveGroup)
    1 * curveConfigRepository.curveConfigInstrResolver(STATE_DATE, "configId") >> right(CurveConfigurationInstrumentResolver.empty())
    1 * auditEntryService.newEntry(_) >> { AuditEntry entry -> right(entry.tap { it.setId(auditEntryId) }) }
    0 * auditEntryService.addLogs_
    1 * marketDataService.getCalibrationMD(VAL_DT.minusDays(1), _) >> right(marketData())
    1 * marketDataService.validMdNodesFilter(_, RAW_PRIMARY, _, STATE_DATE, _) >> EMPTY_FILTER

    1 * curvesCalibrationService.resolveCalibrationBundles(curveGroup.id, STATE_DATE, form, _) >> [calibrationBundle]
    1 * curvesCalibrationService.calibrateCurves(_, _, [calibrationBundle], _) >> left(Error.UNEXPECTED_ERROR.entity("Error"))
    0 * creditCurvesCalibrationService.calibrateCreditCurves(_, _, _, _,)
    1 * bondCurvesCalibrationService.calibrate(_, _, _) >> new BondCurveCalibrationResults([:], ReferenceData.standard())
    0 * fxVolatilitiesCalibrationService.calibrate(_, _, _)
    0 * curveGroupRepository.updateCurveGroupCalibration(_, _, _, _, _)
    when:
    def result = service.calibrate("groupId", form, STATE_DATE)

    then:
    result.isLeft()
    result.left().get() == Error.UNEXPECTED_ERROR.entity("Error")
  }

  CalibrateCurveForm calibrationForm() {
    new CalibrateCurveForm(
      new CalibrationOptionsForm(
      "mId",
      "configId",
      RAW_PRIMARY.name(),
      STATE_DATE.getActualDate(),
      now(),
      VAL_DT.minusDays(1),
      null),
      new CurveDiscountingForm(
      DISCOUNT_EUR.name(),
      LIBOR.name(),
      "USD"
      ))
  }
}
