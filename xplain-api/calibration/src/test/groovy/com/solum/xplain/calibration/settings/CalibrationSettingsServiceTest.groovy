package com.solum.xplain.calibration.settings

import com.opengamma.strata.basics.ReferenceData
import com.solum.xplain.core.settings.entity.GlobalValuationSettings
import com.solum.xplain.core.settings.entity.InflationSeasonalitySettings
import com.solum.xplain.core.settings.repository.ConvexitySettingsRepository
import com.solum.xplain.core.settings.repository.CurveStrippingProductSettingsRepository
import com.solum.xplain.core.settings.repository.GlobalValuationSettingsRepository
import com.solum.xplain.core.settings.repository.InflationSeasonalitySettingsRepository
import spock.lang.Specification

class CalibrationSettingsServiceTest extends Specification {

  def inflationSeasonalitySettingsRepository = Mock(InflationSeasonalitySettingsRepository)
  def productSettingsRepository = Mock(CurveStrippingProductSettingsRepository)
  def convexitySettingsRepository = Mock(ConvexitySettingsRepository)
  def globalValuationSettingsRepository = Mock(GlobalValuationSettingsRepository)

  def service = new CalibrationSettingsService(
  inflationSeasonalitySettingsRepository,
  productSettingsRepository,
  convexitySettingsRepository,
  globalValuationSettingsRepository,
  ReferenceData.standard())


  def "should return inflationSeasonalities"() {
    setup:
    def expectedResult = new InflationSeasonalitySettings()
    1 * inflationSeasonalitySettingsRepository.getInflationSeasonalitySettings(null) >> expectedResult
    when:
    def result = service.inflationSeasonalities(null)
    then:
    result == expectedResult
  }

  def "should return forceIsdaInterpolators"() {
    setup:
    1 * globalValuationSettingsRepository.getGlobalValuationSettings(null) >> new GlobalValuationSettings(forceIsdaInterpolatorsForCds: true)
    when:
    def result = service.forceIsdaInterpolators(null)
    then:
    result
  }

  def "should return excludeFixingsFromValuationDate"() {
    setup:
    1 * globalValuationSettingsRepository.getGlobalValuationSettings(null) >> new GlobalValuationSettings(excludeFixingsFromValuationDate: true)
    when:
    def result = service.excludeFixingsFromValuationDate(null)
    then:
    result
  }
}
