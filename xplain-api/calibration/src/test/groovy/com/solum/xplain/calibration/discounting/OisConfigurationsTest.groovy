package com.solum.xplain.calibration.discounting

import static com.solum.xplain.core.classifiers.discounting.IndexBasedDiscountCurrencies.EUR_EONIA
import static com.solum.xplain.core.classifiers.discounting.IndexBasedDiscountCurrencies.EUR_ESTR
import static com.solum.xplain.core.classifiers.discounting.IndexBasedDiscountCurrencies.USD_FEDFUNDS
import static com.solum.xplain.core.classifiers.discounting.IndexBasedDiscountCurrencies.USD_SOFR
import static com.solum.xplain.core.classifiers.discounting.IndexBasedDiscountCurrencies.getOf
import static java.time.LocalDate.ofEpochDay

import com.opengamma.strata.basics.currency.Currency
import com.opengamma.strata.basics.index.OvernightIndices
import com.solum.xplain.calibration.rates.CalibrationEntry
import com.solum.xplain.core.classifiers.discounting.IndexBasedDiscountCurrency
import java.time.LocalDate
import spock.lang.Specification
import spock.lang.Unroll

class OisConfigurationsTest extends Specification {
  def static EUR_ESTR_IMPOSITION_DATE = LocalDate.of(2020, 7, 27)
  def static USD_SOFR_IMPOSITION_DATE = LocalDate.of(2020, 9, 16)
  def static EUR = getOf(Currency.EUR) as IndexBasedDiscountCurrency
  def static USD = getOf(Currency.USD) as IndexBasedDiscountCurrency

  @Unroll
  def "should impose #currency OIS curves for #date when #curves"() {
    when:
    def results = OisConfigurations.of(date).imposeCurves(currency, curves)

    then:
    results.size() == expected.size()
    if (results.size() > 0) {
      expected.eachWithIndex { CalibrationEntry expectedEntry, int idx ->
        expectedEntry.curveName == results[idx].curveName
      }
    }

    where:
    date                                 | currency     | curves                               | expected
    //EUR EONIA
    ofEpochDay(0)                        | EUR_EONIA    | [EUR_EONIA_OIS()]                    | [EUR_EONIA_OIS()]
    ofEpochDay(0)                        | EUR_EONIA    | [EUR_ESTR_OIS()]                     | []
    ofEpochDay(0)                        | EUR_EONIA    | [EUR_EONIA_OIS(), EUR_ESTR_OIS()]    | [EUR_EONIA_OIS()]
    //EUR ESTR
    ofEpochDay(0)                        | EUR_ESTR     | [EUR_EONIA_OIS()]                    | []
    ofEpochDay(0)                        | EUR_ESTR     | [EUR_ESTR_OIS()]                     | [EUR_ESTR_OIS()]
    ofEpochDay(0)                        | EUR_ESTR     | [EUR_EONIA_OIS(), EUR_ESTR_OIS()]    | [EUR_ESTR_OIS()]
    //EUR
    ofEpochDay(0)                        | EUR          | [EUR_EONIA_OIS()]                    | [EUR_EONIA_OIS()]
    ofEpochDay(0)                        | EUR          | [EUR_ESTR_OIS()]                     | [EUR_ESTR_OIS()]
    ofEpochDay(0)                        | EUR          | [EUR_EONIA_OIS(), EUR_ESTR_OIS()]    | [EUR_EONIA_OIS()]
    EUR_ESTR_IMPOSITION_DATE             | EUR          | [EUR_EONIA_OIS(), EUR_ESTR_OIS()]    | [EUR_ESTR_OIS()]
    EUR_ESTR_IMPOSITION_DATE.plusDays(1) | EUR          | [EUR_EONIA_OIS(), EUR_ESTR_OIS()]    | [EUR_ESTR_OIS()]
    EUR_ESTR_IMPOSITION_DATE.plusDays(1) | EUR          | [EUR_ESTR_OIS()]                     | [EUR_ESTR_OIS()]
    EUR_ESTR_IMPOSITION_DATE.plusDays(1) | EUR          | [EUR_EONIA_OIS()]                    | [EUR_EONIA_OIS()]
    //USD FED-FEDFUNDS
    ofEpochDay(0)                        | USD_FEDFUNDS | [USD_FED_FUND_OIS()]                 | [USD_FED_FUND_OIS()]
    ofEpochDay(0)                        | USD_FEDFUNDS | [USD_SOFR_OIS()]                     | []
    ofEpochDay(0)                        | USD_FEDFUNDS | [USD_FED_FUND_OIS(), USD_SOFR_OIS()] | [USD_FED_FUND_OIS()]
    //USD USD_SOFR
    ofEpochDay(0)                        | USD_SOFR     | [USD_FED_FUND_OIS()]                 | []
    ofEpochDay(0)                        | USD_SOFR     | [USD_SOFR_OIS()]                     | [USD_SOFR_OIS()]
    ofEpochDay(0)                        | USD_SOFR     | [USD_FED_FUND_OIS(), USD_SOFR_OIS()] | [USD_SOFR_OIS()]
    // USD
    ofEpochDay(0)                        | USD          | [USD_FED_FUND_OIS()]                 | [USD_FED_FUND_OIS()]
    ofEpochDay(0)                        | USD          | [USD_SOFR_OIS()]                     | [USD_SOFR_OIS()]
    ofEpochDay(0)                        | USD          | [USD_FED_FUND_OIS(), USD_SOFR_OIS()] | [USD_FED_FUND_OIS()]
    USD_SOFR_IMPOSITION_DATE             | USD          | [USD_FED_FUND_OIS(), USD_SOFR_OIS()] | [USD_SOFR_OIS()]
    USD_SOFR_IMPOSITION_DATE.plusDays(1) | USD          | [USD_FED_FUND_OIS(), USD_SOFR_OIS()] | [USD_SOFR_OIS()]
    USD_SOFR_IMPOSITION_DATE.plusDays(1) | USD          | [USD_SOFR_OIS()]                     | [USD_SOFR_OIS()]
    USD_SOFR_IMPOSITION_DATE.plusDays(1) | USD          | [USD_FED_FUND_OIS()]                 | [USD_FED_FUND_OIS()]
  }

  @Unroll
  def "should impose #currency Overnight indices for #date when #indices"() {
    when:
    def results = OisConfigurations.of(date).imposeIndices(currency, indices)

    then:
    results.size() == expected.size()
    results == expected

    where:
    date                                 | currency     | indices                                                    | expected
    //EUR EONIA
    ofEpochDay(0)                        | EUR_EONIA    | [OvernightIndices.EUR_EONIA]                               | [OvernightIndices.EUR_EONIA]
    ofEpochDay(0)                        | EUR_EONIA    | [OvernightIndices.EUR_ESTR]                                | []
    ofEpochDay(0)                        | EUR_EONIA    | [OvernightIndices.EUR_EONIA, OvernightIndices.EUR_ESTR]    | [OvernightIndices.EUR_EONIA]
    //EUR ESTR
    ofEpochDay(0)                        | EUR_ESTR     | [OvernightIndices.EUR_EONIA]                               | []
    ofEpochDay(0)                        | EUR_ESTR     | [OvernightIndices.EUR_ESTR]                                | [OvernightIndices.EUR_ESTR]
    ofEpochDay(0)                        | EUR_ESTR     | [OvernightIndices.EUR_EONIA, OvernightIndices.EUR_ESTR]    | [OvernightIndices.EUR_ESTR]
    //EUR
    ofEpochDay(0)                        | EUR          | [OvernightIndices.EUR_EONIA]                               | [OvernightIndices.EUR_EONIA]
    ofEpochDay(0)                        | EUR          | [OvernightIndices.EUR_ESTR]                                | [OvernightIndices.EUR_ESTR]
    ofEpochDay(0)                        | EUR          | [OvernightIndices.EUR_EONIA, OvernightIndices.EUR_ESTR]    | [OvernightIndices.EUR_EONIA]
    EUR_ESTR_IMPOSITION_DATE             | EUR          | [OvernightIndices.EUR_EONIA, OvernightIndices.EUR_ESTR]    | [OvernightIndices.EUR_ESTR]
    EUR_ESTR_IMPOSITION_DATE.plusDays(1) | EUR          | [OvernightIndices.EUR_EONIA, OvernightIndices.EUR_ESTR]    | [OvernightIndices.EUR_ESTR]
    EUR_ESTR_IMPOSITION_DATE.plusDays(1) | EUR          | [OvernightIndices.EUR_ESTR]                                | [OvernightIndices.EUR_ESTR]
    EUR_ESTR_IMPOSITION_DATE.plusDays(1) | EUR          | [OvernightIndices.EUR_EONIA]                               | [OvernightIndices.EUR_EONIA]
    //USD FED-FEDFUNDS
    ofEpochDay(0)                        | USD_FEDFUNDS | [OvernightIndices.USD_FED_FUND]                            | [OvernightIndices.USD_FED_FUND]
    ofEpochDay(0)                        | USD_FEDFUNDS | [OvernightIndices.USD_SOFR]                                | []
    ofEpochDay(0)                        | USD_FEDFUNDS | [OvernightIndices.USD_FED_FUND, OvernightIndices.USD_SOFR] | [OvernightIndices.USD_FED_FUND]
    //USD USD_SOFR
    ofEpochDay(0)                        | USD_SOFR     | [OvernightIndices.USD_FED_FUND]                            | []
    ofEpochDay(0)                        | USD_SOFR     | [OvernightIndices.USD_SOFR]                                | [OvernightIndices.USD_SOFR]
    ofEpochDay(0)                        | USD_SOFR     | [OvernightIndices.USD_FED_FUND, OvernightIndices.USD_SOFR] | [OvernightIndices.USD_SOFR]
    // USD
    ofEpochDay(0)                        | USD          | [OvernightIndices.USD_FED_FUND]                            | [OvernightIndices.USD_FED_FUND]
    ofEpochDay(0)                        | USD          | [OvernightIndices.USD_SOFR]                                | [OvernightIndices.USD_SOFR]
    ofEpochDay(0)                        | USD          | [OvernightIndices.USD_FED_FUND, OvernightIndices.USD_SOFR] | [OvernightIndices.USD_FED_FUND]
    USD_SOFR_IMPOSITION_DATE             | USD          | [OvernightIndices.USD_FED_FUND, OvernightIndices.USD_SOFR] | [OvernightIndices.USD_SOFR]
    USD_SOFR_IMPOSITION_DATE.plusDays(1) | USD          | [OvernightIndices.USD_FED_FUND, OvernightIndices.USD_SOFR] | [OvernightIndices.USD_SOFR]
    USD_SOFR_IMPOSITION_DATE.plusDays(1) | USD          | [OvernightIndices.USD_SOFR]                                | [OvernightIndices.USD_SOFR]
    USD_SOFR_IMPOSITION_DATE.plusDays(1) | USD          | [OvernightIndices.USD_FED_FUND]                            | [OvernightIndices.USD_FED_FUND]
  }


  def EUR_EONIA_OIS() {
    def entry = Mock(CalibrationEntry)
    entry.curveName >> "EUR EONIA"
    return entry
  }

  def EUR_ESTR_OIS() {
    def entry = Mock(CalibrationEntry)
    entry.curveName >> "EUR ESTR"
    return entry
  }

  def USD_FED_FUND_OIS() {
    def entry = Mock(CalibrationEntry)
    entry.curveName >> "USD FEDFUNDS"
    return entry
  }

  def USD_SOFR_OIS() {
    def entry = Mock(CalibrationEntry)
    entry.curveName >> "USD SOFR"
    return entry
  }
}
