package com.solum.xplain.calibration.endpoint.curvegroup


import static io.atlassian.fugue.Either.right
import static org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.csrf
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post

import com.fasterxml.jackson.databind.ObjectMapper
import com.solum.xplain.calibration.CurveGroupCalibrationService
import com.solum.xplain.calibration.helpers.MockMvcConfiguration
import com.solum.xplain.calibration.trades.CalibrationPortfolioService
import com.solum.xplain.calibration.trades.GenerateCalibrationPortfolioForm
import com.solum.xplain.calibration.value.CalibrateCurveForm
import com.solum.xplain.calibration.value.CalibrationResponse
import com.solum.xplain.core.common.EntityId
import com.solum.xplain.core.common.RequestPathVariablesSupport
import com.solum.xplain.core.common.value.AllowedTeamsForm
import com.solum.xplain.core.common.value.EntityNameView
import com.solum.xplain.core.common.versions.BitemporalDate
import com.solum.xplain.core.company.entity.Company
import com.solum.xplain.core.company.repository.CompanyRepository
import com.solum.xplain.core.curveconfiguration.CurveConfigurationRepository
import com.solum.xplain.core.lock.ResourceValidationService
import com.solum.xplain.core.market.repository.MarketDataGroupRepository
import com.solum.xplain.core.portfolio.repository.PortfolioRepository
import java.time.LocalDate
import java.util.function.Supplier
import org.spockframework.spring.SpringBean
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.http.MediaType
import org.springframework.security.test.context.support.WithMockUser
import org.springframework.test.web.servlet.MockMvc
import spock.lang.Specification
import spock.lang.Unroll

@WebMvcTest(controllers = [CurveGroupCalibrationController])
@MockMvcConfiguration
class CurveGroupCalibrationControllerTest extends Specification {

  static def LOCK_STATE_DATE = BitemporalDate.newOf(LocalDate.now())

  @SpringBean
  private CurveGroupCalibrationService calibrationService = Mock()
  @SpringBean
  private MarketDataGroupRepository marketDataGroupRepository = Mock()
  @SpringBean
  private CalibrationPortfolioService calibrationPortfolioService = Mock()
  @SpringBean
  private ResourceValidationService resourceValidationService = Mock()
  @SpringBean
  private CurveConfigurationRepository configurationRepository = Mock()
  @SpringBean
  private RequestPathVariablesSupport requestPathVariablesSupport = new RequestPathVariablesSupport()
  @SpringBean
  private CompanyRepository companyRepository = Mock()
  @SpringBean
  private PortfolioRepository portfolioRepository = Mock()

  @Autowired
  private MockMvc mockMvc
  @Autowired
  private ObjectMapper objectMapper

  private static final String CURVE_GROUP_API = "/curve-group"

  @Unroll
  @WithMockUser
  def "should perform form (#form) validation with response #code #responseBody for calibrate"() {
    setup:
    resourceValidationService.ifLocksAvailable(_ as List, _ as Supplier) >> {
      locks, supplier -> right(supplier.get())
    }
    calibrationService.calibrate("1", _ as CalibrateCurveForm, _ as BitemporalDate) >> right(
    CalibrationResponse.calibrationResponse("logId", null))
    marketDataGroupRepository.dataGroupName("marketDataGroupId") >> Optional.of(new EntityNameView())
    configurationRepository.validCurveConfigurationId("configurationId") >> true
    def results = mockMvc.perform(post(CURVE_GROUP_API + "/1/calibrate")
    .with(csrf())
    .content(objectMapper.writeValueAsString(form))
    .contentType(MediaType.APPLICATION_JSON))
    .andReturn()

    expect:
    with(results.getResponse()) {
      getStatus() == code
      getContentAsString().indexOf(responseBody) >= 0
    }

    where:
    form                                                                                                                         | code | responseBody
    calibrateCurvesForm(c -> c)                                                                                                  | 200  | "logId"
    calibrateCurvesForm(b -> b.remove("curveDiscountingForm"))                                                                   | 412  | "NotNull.calibrateCurveForm.curveDiscountingForm"
    calibrateCurvesForm(b -> b, calibrationOptions(c -> c), curveDiscounting(c -> c.remove("discountingType")))                  | 412  | "NotEmpty.calibrateCurveForm.curveDiscountingForm.discountingType"
    calibrateCurvesForm(b -> b, calibrationOptions(c -> c), curveDiscounting(c -> c.replace("discountingType", "AAA")))          | 412  | "ValidStringSet.calibrateCurveForm.curveDiscountingForm.discountingType"
    calibrateCurvesForm(b -> b, calibrationOptions(c -> c), curveDiscounting(c -> c.replace("discountingType", "DISCOUNT_EUR"))) | 412  | "ValidCurveDiscountingForm.calibrateCurveForm.curveDiscounting"
    calibrateCurvesForm(b -> b, calibrationOptions(c -> c), curveDiscounting(c -> c.remove("strippingType")))                    | 412  | "NotEmpty.calibrateCurveForm.curveDiscountingForm.strippingType"
    calibrateCurvesForm(b -> b, calibrationOptions(c -> c), curveDiscounting(c -> c.replace("strippingType", "AAA")))            | 412  | "ValidStringSet.calibrateCurveForm.curveDiscountingForm.strippingType"
    calibrateCurvesForm(b -> b.remove("calibrationOptions"))                                                                     | 412  | "NotNull.calibrateCurveForm.calibrationOptions"
    calibrateCurvesForm(c -> c, calibrationOptions(o -> o.remove("valuationDate")))                                              | 412  | "NotNull.calibrateCurveForm.calibrationOptions.valuationDate"
    calibrateCurvesForm(c -> c, calibrationOptions(o -> o.remove("curveDate")))                                                  | 412  | "NotNull.calibrateCurveForm.calibrationOptions.curveDate"
    calibrateCurvesForm(c -> c, calibrationOptions(o -> o.remove("stateDate")))                                                  | 412  | "NotNull.calibrateCurveForm.calibrationOptions.stateDate"
    calibrateCurvesForm(c -> c, calibrationOptions(o -> o.remove("curveConfigurationId")))                                       | 412  | "NotEmpty.calibrateCurveForm.calibrationOptions.curveConfigurationId"
    calibrateCurvesForm(c -> c, calibrationOptions(o -> o.remove("marketDataSource")))                                           | 412  | "NotEmpty.calibrateCurveForm.calibrationOptions.marketDataSource"
    calibrateCurvesForm(c -> c, calibrationOptions(o -> o.replace("marketDataSource", "AAA")))                                   | 412  | "ValidStringSet.calibrateCurveForm.calibrationOptions.marketDataSource"
  }

  static calibrateCurvesForm(Closure closure, calibrationOptions = calibrationOptions(c -> c), curveDiscounting = curveDiscounting(c -> c)) {
    [
      calibrationOptions  : calibrationOptions,
      curveDiscountingForm: curveDiscounting,
    ].with(true, closure)
  }

  static curveDiscounting(Closure closure) {
    [
      discountingType : "LOCAL_CURRENCY",
      strippingType   : "LIBOR",
      triangulationCcy: "USD",
    ].with(true, closure)
  }

  @Unroll
  @WithMockUser
  def "should perform calibration portfolio generation (#form) validation with response #code #responseBody"() {
    setup:
    resourceValidationService.ifLocksAvailable(_ as List, _ as Supplier) >> {
      locks, supplier -> right(supplier.get())
    }
    calibrationPortfolioService.generatePortfolio("1", _ as GenerateCalibrationPortfolioForm, _ as BitemporalDate) >> right(EntityId.entityId("1"))
    marketDataGroupRepository.dataGroupName("marketDataGroupId") >> Optional.of(new EntityNameView())
    configurationRepository.validCurveConfigurationId("configurationId") >> true
    companyRepository.companyEntity("507c7f79bcf86cd7994f6c0e") >> right(new Company())
    portfolioRepository.existsByExternalIdExcludingSelf("507c7f79bcf86cd7994f6c0e", "000000000000000000000000", "ext", null) >> true
    def results = mockMvc.perform(post(CURVE_GROUP_API + "/1/calibrate/generate-portfolio")
    .with(csrf())
    .content(objectMapper.writeValueAsString(form))
    .contentType(MediaType.APPLICATION_JSON))
    .andReturn()

    expect:
    with(results.getResponse()) {
      getStatus() == code
      getContentAsString().indexOf(responseBody) >= 0
    }

    where:
    form                                                                        | code | responseBody
    generatePortfolioForm(b -> b)                                               | 200  | "id"
    generatePortfolioForm(b -> b.remove("externalPortfolioId"))                 | 412  | "NotEmpty.generateCalibrationPortfolioForm.externalPortfolioId"
    generatePortfolioForm(b -> b.remove("name"))                                | 412  | "NotEmpty.generateCalibrationPortfolioForm.name"
    generatePortfolioForm(b -> b.remove("calibrationOptions"))                  | 412  | "NotNull.generateCalibrationPortfolioForm.calibrationOptions"
    generatePortfolioForm(b -> b.remove("allowedTeamsForm"))                    | 412  | "NotNull.generateCalibrationPortfolioForm.allowedTeamsForm"
    generatePortfolioForm(b -> b.remove("companyId"))                           | 412  | "NotEmpty.generateCalibrationPortfolioForm.companyId"
    generatePortfolioForm(b -> b.replace("externalPortfolioId", "ext"))         | 412  | "UniquePortfolioExtId.generateCalibrationPortfolioForm.externalPortfolioId"
    generatePortfolioForm(b -> b.replace("externalPortfolioId", "with spaces")) | 412  | "ValidIdentifier.generateCalibrationPortfolioForm.externalPortfolioId"
  }

  static generatePortfolioForm(Closure closure, calibrationOptions = calibrationOptions(c -> c)) {
    [
      calibrationOptions : calibrationOptions,
      externalPortfolioId: "EXTERNAL_ID",
      name               : "NAME",
      companyId          : "507c7f79bcf86cd7994f6c0e",
      entityId           : "000000000000000000000000",
      allowedTeamsForm   : new AllowedTeamsForm(true, [])
    ].with(true, closure)
  }

  static calibrationOptions(Closure closure) {
    [
      marketDataGroupId   : "marketDataGroupId",
      curveConfigurationId: "configurationId",
      marketDataSource    : "OVERLAY",
      stateDate           : LocalDate.now(),
      curveDate           : LocalDate.now(),
      valuationDate       : LocalDate.now(),
      priceRequirements   : [
        curvesPriceType   : "BID_PRICE",
        dscCurvesPriceType: "BID_PRICE",
        fxRatesPriceType  : "BID_PRICE",
        volsPriceType     : "BID_PRICE",
        volsSkewsPriceType: "BID_PRICE",
      ]
    ].with(true, closure)
  }
}
