package com.solum.xplain.calibration.discounting

import static com.opengamma.strata.basics.index.IborIndices.USD_LIBOR_6M
import static com.opengamma.strata.product.fx.type.FxSwapConventions.EUR_USD
import static com.opengamma.strata.product.swap.type.FixedIborSwapConventions.EUR_FIXED_1Y_EURIBOR_3M
import static com.opengamma.strata.product.swap.type.FixedIborSwapConventions.USD_FIXED_1Y_LIBOR_3M
import static com.opengamma.strata.product.swap.type.FixedIborSwapConventions.USD_FIXED_6M_LIBOR_3M
import static com.opengamma.strata.product.swap.type.FixedOvernightSwapConventions.EUR_FIXED_1Y_EONIA_OIS
import static com.opengamma.strata.product.swap.type.IborIborSwapConventions.USD_LIBOR_1M_LIBOR_3M
import static com.opengamma.strata.product.swap.type.IborIborSwapConventions.USD_LIBOR_3M_LIBOR_6M
import static com.opengamma.strata.product.swap.type.XCcyIborIborSwapConventions.EUR_EURIBOR_3M_USD_LIBOR_3M
import static com.solum.xplain.core.classifiers.CurveNodeTypes.FIXED_IBOR_SWAP_NODE
import static com.solum.xplain.core.classifiers.CurveNodeTypes.FIXED_OVERNIGHT_SWAP_NODE
import static com.solum.xplain.core.classifiers.CurveNodeTypes.FX_SWAP_NODE
import static com.solum.xplain.core.classifiers.CurveNodeTypes.IBOR_FIXING_DEPOSIT_NODE
import static com.solum.xplain.core.classifiers.CurveNodeTypes.IBOR_IBOR_SWAP_NODE
import static com.solum.xplain.core.classifiers.CurveNodeTypes.XCCY_IBOR_IBOR_SWAP_NODE
import static com.solum.xplain.core.curvegroup.curve.entity.CurveNodeBuilder.curveNode
import static com.solum.xplain.core.error.Error.CALIBRATION_ERROR

import com.opengamma.strata.basics.currency.Currency
import com.opengamma.strata.product.fx.type.FxSwapConventions
import com.solum.xplain.calibration.rates.CalibrationEntry
import com.solum.xplain.calibration.value.CurveShifts
import com.solum.xplain.core.curvegroup.conventions.ClearingHouse
import com.solum.xplain.core.curvegroup.curve.classifier.CurveType
import com.solum.xplain.core.curvegroup.curve.entity.Curve
import com.solum.xplain.core.curvegroup.curve.entity.CurveBuilder
import java.util.function.Consumer
import spock.lang.Specification

class ForeignRequiredGroupsResolverTest extends Specification {

  Consumer logsConsumer = Mock(Consumer)

  def "should correctly resolve direct foreign XCCY curve"() {
    setup:
    def discountEntry = new CalibrationEntry(
    new CurveBuilder()
    .entityId("EUR-EONIA")
    .name("EUR EONIA")
    .curveType(CurveType.INDEX_BASIS)
    .nodes([curveNode(FIXED_OVERNIGHT_SWAP_NODE, EUR_FIXED_1Y_EONIA_OIS.name)])
    .build(),
    null,
    Currency.EUR
    )
    def curves =
    [
      new CurveBuilder()
      .entityId("EUR-EONIA")
      .name("EUR EONIA")
      .curveType(CurveType.INDEX_BASIS)
      .nodes([curveNode(FIXED_OVERNIGHT_SWAP_NODE, EUR_FIXED_1Y_EONIA_OIS.name)])
      .build(),
      new CurveBuilder()
      .entityId("EUR3M")
      .name("EUR 3M")
      .curveType(CurveType.IR_INDEX)
      .nodes([curveNode(FIXED_IBOR_SWAP_NODE, EUR_FIXED_1Y_EURIBOR_3M.name)])
      .build(),
      new CurveBuilder()
      .entityId("USD3M")
      .name("USD 3M")
      .curveType(CurveType.IR_INDEX)
      .nodes([curveNode(FIXED_IBOR_SWAP_NODE, USD_FIXED_6M_LIBOR_3M.name)])
      .build(),
      new CurveBuilder()
      .entityId("EURUSDBASIS")
      .name("EUR/USD")
      .curveType(CurveType.XCCY)
      .nodes([curveNode(XCCY_IBOR_IBOR_SWAP_NODE, EUR_EURIBOR_3M_USD_LIBOR_3M.name)])
      .build(),
    ]
    def resolver = ForeignRequiredGroupsResolver.foreignRequiredGroupsResolver(
    discountEntry,
    ClearingHouse.NONE,
    toCalibrationEntries(curves),
    Currency.EUR,
    Set.of(Currency.USD),
    Set.of(),
    logsConsumer
    )

    1 * logsConsumer.accept([])

    when:
    def result = resolver.resolveForeignGroups()

    then:
    result.size() == 1
    def group = result.get(0)
    group.discountCurve().resolvedDiscountCurrency == Currency.USD
    group.discountCurve().curveName == "EUR/USD"
    group.calibrationEntries.size() == 4
    def entriesCurveNames = curveNames(group.calibrationEntries)
    entriesCurveNames.contains("EUR EONIA")
    entriesCurveNames.contains("EUR/USD")
    entriesCurveNames.contains("USD 3M")
    entriesCurveNames.contains("EUR 3M")
  }

  def "should not resolve XCCY foreign curve when missing required index"() {
    setup:
    def discountEntry = new CalibrationEntry(
    new CurveBuilder()
    .entityId("EUR-EONIA")
    .name("EUR EONIA")
    .curveType(CurveType.INDEX_BASIS)
    .nodes([curveNode(FIXED_OVERNIGHT_SWAP_NODE, EUR_FIXED_1Y_EONIA_OIS.name)])
    .build(),
    null,
    Currency.EUR
    )
    def curves =
    [
      new CurveBuilder()
      .entityId("EUR-EONIA")
      .name("EUR EONIA")
      .curveType(CurveType.INDEX_BASIS)
      .nodes([curveNode(FIXED_OVERNIGHT_SWAP_NODE, EUR_FIXED_1Y_EONIA_OIS.name)])
      .build(),
      new CurveBuilder()
      .entityId("USD3M")
      .name("USD 3M")
      .curveType(CurveType.IR_INDEX)
      .nodes([curveNode(FIXED_IBOR_SWAP_NODE, USD_FIXED_6M_LIBOR_3M.name)])
      .build(),
      new CurveBuilder()
      .entityId("EURUSDBASIS")
      .name("EUR/USD")
      .curveType(CurveType.XCCY)
      .nodes([curveNode(XCCY_IBOR_IBOR_SWAP_NODE, EUR_EURIBOR_3M_USD_LIBOR_3M.name)])
      .build(),
    ]
    def resolver = ForeignRequiredGroupsResolver.foreignRequiredGroupsResolver(
    discountEntry,
    ClearingHouse.NONE,
    toCalibrationEntries(curves),
    Currency.EUR,
    Set.of(Currency.USD),
    Set.of(),
    logsConsumer
    )

    1 * logsConsumer.accept([CALIBRATION_ERROR.entity("Required indices for curve EUR/USD missing")])

    when:
    def result = resolver.resolveForeignGroups()

    then:
    result.isEmpty()
  }

  def "should fallback to FX curve when XCCY curve missing"() {
    setup:
    def discountEntry = new CalibrationEntry(
    new CurveBuilder()
    .entityId("EUR-EONIA")
    .name("EUR EONIA")
    .curveType(CurveType.INDEX_BASIS)
    .nodes([curveNode(FIXED_OVERNIGHT_SWAP_NODE, EUR_FIXED_1Y_EONIA_OIS.name)])
    .build(),
    null,
    Currency.EUR
    )
    def curves =
    [
      new CurveBuilder()
      .entityId("EUR-EONIA")
      .name("EUR EONIA")
      .curveType(CurveType.INDEX_BASIS)
      .nodes([curveNode(FIXED_OVERNIGHT_SWAP_NODE, EUR_FIXED_1Y_EONIA_OIS.name)])
      .build(),
      new CurveBuilder()
      .entityId("USD3M")
      .name("USD 3M")
      .curveType(CurveType.IR_INDEX)
      .nodes([curveNode(FIXED_IBOR_SWAP_NODE, USD_FIXED_6M_LIBOR_3M.name)])
      .build(),
      new CurveBuilder()
      .entityId("EUR/USD")
      .name("EUR/USD")
      .curveType(CurveType.XCCY)
      .nodes([curveNode(FX_SWAP_NODE, EUR_USD.name)])
      .build(),
    ]
    def resolver = ForeignRequiredGroupsResolver.foreignRequiredGroupsResolver(
    discountEntry,
    ClearingHouse.NONE,
    toCalibrationEntries(curves),
    Currency.EUR,
    Set.of(Currency.USD),
    Set.of(),
    logsConsumer
    )

    1 * logsConsumer.accept([])

    when:
    def result = resolver.resolveForeignGroups()

    then:
    def group = result.get(0)
    group.discountCurve().resolvedDiscountCurrency == Currency.USD
    group.discountCurve().curveName == "EUR/USD"
    group.calibrationEntries.size() == 2
    def entriesCurveNames = curveNames(group.calibrationEntries)
    entriesCurveNames.contains("EUR EONIA")
    entriesCurveNames.contains("EUR/USD")
  }

  def "should resolve foreign discount with indirect link"() {
    setup:
    def discountEntry = new CalibrationEntry(
    new CurveBuilder()
    .entityId("EUR-EONIA")
    .name("EUR EONIA")
    .curveType(CurveType.INDEX_BASIS)
    .nodes([curveNode(FIXED_OVERNIGHT_SWAP_NODE, EUR_FIXED_1Y_EONIA_OIS.name)])
    .build(),
    null,
    Currency.EUR
    )
    def curves =
    [
      new CurveBuilder()
      .entityId("EUR-EONIA")
      .name("EUR EONIA")
      .curveType(CurveType.INDEX_BASIS)
      .nodes([curveNode(FIXED_OVERNIGHT_SWAP_NODE, EUR_FIXED_1Y_EONIA_OIS.name)])
      .build(),
      new CurveBuilder()
      .entityId("EUR/USD")
      .name("EUR/USD")
      .curveType(CurveType.XCCY)
      .nodes([curveNode(FX_SWAP_NODE, EUR_USD.name)])
      .build(),
      new CurveBuilder()
      .entityId("GBP/USD")
      .name("GBP/USD")
      .curveType(CurveType.XCCY)
      .nodes([curveNode(FX_SWAP_NODE, FxSwapConventions.GBP_USD.name)])
      .build(),
    ]
    def resolver = ForeignRequiredGroupsResolver.foreignRequiredGroupsResolver(
    discountEntry,
    ClearingHouse.NONE,
    toCalibrationEntries(curves),
    Currency.EUR,
    Set.of(Currency.USD, Currency.GBP),
    Set.of(),
    logsConsumer
    )

    1 * logsConsumer.accept([])

    when:
    def result = resolver.resolveForeignGroups()

    then:
    result.size() == 2
    def gbpGroup = result.stream()
    .filter({ v -> v.discountCurve().resolvedDiscountCurrency == Currency.GBP })
    .findFirst().get()
    gbpGroup.discountCurve().curveName == "GBP/USD"
    def entriesCurveNames = curveNames(gbpGroup.calibrationEntries)
    entriesCurveNames.contains("EUR EONIA")
    entriesCurveNames.contains("EUR/USD")
    entriesCurveNames.contains("GBP/USD")
  }

  def "should resolve foreign discount with indirect link and mixed foreign curve types"() {
    setup:
    def discountEntry = new CalibrationEntry(
    new CurveBuilder()
    .entityId("EUR-EONIA")
    .name("EUR EONIA")
    .curveType(CurveType.INDEX_BASIS)
    .nodes([curveNode(FIXED_OVERNIGHT_SWAP_NODE, EUR_FIXED_1Y_EONIA_OIS.name)])
    .build(),
    null,
    Currency.EUR
    )
    def curves =
    [
      new CurveBuilder()
      .entityId("EUR-EONIA")
      .name("EUR EONIA")
      .curveType(CurveType.INDEX_BASIS)
      .nodes([curveNode(FIXED_OVERNIGHT_SWAP_NODE, EUR_FIXED_1Y_EONIA_OIS.name)])
      .build(),
      new CurveBuilder()
      .entityId("EURUSDBASIS")
      .name("EUR/USD")
      .curveType(CurveType.XCCY)
      .nodes([curveNode(XCCY_IBOR_IBOR_SWAP_NODE, EUR_EURIBOR_3M_USD_LIBOR_3M.name)])
      .build(),
      new CurveBuilder()
      .entityId("EUR3M")
      .name("EUR 3M")
      .curveType(CurveType.IR_INDEX)
      .nodes([curveNode(FIXED_IBOR_SWAP_NODE, EUR_FIXED_1Y_EURIBOR_3M.name)])
      .build(),
      new CurveBuilder()
      .entityId("USD3M")
      .name("USD 3M")
      .curveType(CurveType.IR_INDEX)
      .nodes([curveNode(FIXED_IBOR_SWAP_NODE, USD_FIXED_6M_LIBOR_3M.name)])
      .build(),
      new CurveBuilder()
      .entityId("GBP/USD")
      .name("GBP/USD")
      .curveType(CurveType.XCCY)
      .nodes([curveNode(FX_SWAP_NODE, FxSwapConventions.GBP_USD.name)])
      .build(),
    ]
    def resolver = ForeignRequiredGroupsResolver.foreignRequiredGroupsResolver(
    discountEntry,
    ClearingHouse.NONE,
    toCalibrationEntries(curves),
    Currency.EUR,
    Set.of(Currency.USD, Currency.GBP),
    Set.of(),
    logsConsumer
    )

    1 * logsConsumer.accept([])

    when:
    def result = resolver.resolveForeignGroups()

    then:
    result.size() == 2
    result.stream()
    .anyMatch({ c -> c.discountCurve().resolvedDiscountCurrency == Currency.USD })
    result.stream()
    .anyMatch({ c -> c.discountCurve().resolvedDiscountCurrency == Currency.GBP })
  }

  def "should resolve foreign discount with INDEX_BASIS curves"() {
    setup:
    def discountEntry = new CalibrationEntry(
    new CurveBuilder()
    .entityId("USD-SOFR-OIS")
    .name("USD SOFR")
    .curveType(CurveType.IR_INDEX)
    .nodes([curveNode(FIXED_OVERNIGHT_SWAP_NODE, "USD-FIXED-TERM-SOFR-OIS")])
    .build(),
    null,
    Currency.USD
    )
    def curves =
    [
      new CurveBuilder()
      .entityId("USD-SOFR-OIS")
      .name("USD SOFR")
      .curveType(CurveType.INDEX_BASIS)
      .nodes([curveNode(FIXED_OVERNIGHT_SWAP_NODE, "USD-FIXED-TERM-SOFR-OIS")])
      .build(),
      new CurveBuilder()
      .entityId("MXNUSDBASIS")
      .name("USD/MXN")
      .curveType(CurveType.XCCY)
      .nodes([curveNode(XCCY_IBOR_IBOR_SWAP_NODE, "MXN-TIIE-4W-USD-LIBOR-1M")])
      .build(),
      new CurveBuilder()
      .entityId("MXN4W")
      .name("MXN 4W")
      .curveType(CurveType.IR_INDEX)
      .nodes([curveNode(FIXED_IBOR_SWAP_NODE, "MXN-FIXED-4W-TIIE-4W")])
      .build(),
      new CurveBuilder()
      .entityId("USD1M")
      .name("USD 1M")
      .curveType(CurveType.INDEX_BASIS)
      .nodes([curveNode(IBOR_IBOR_SWAP_NODE, USD_LIBOR_1M_LIBOR_3M.name)])
      .build(),
      new CurveBuilder()
      .entityId("USD3M")
      .name("USD 3M")
      .curveType(CurveType.IR_INDEX)
      .nodes([curveNode(FIXED_IBOR_SWAP_NODE, USD_FIXED_1Y_LIBOR_3M.name)])
      .build(),
      new CurveBuilder()
      .entityId("USD6M")
      .name("USD 6M")
      .curveType(CurveType.INDEX_BASIS)
      .nodes([
        curveNode(IBOR_FIXING_DEPOSIT_NODE, USD_LIBOR_6M.name),
        curveNode(IBOR_IBOR_SWAP_NODE, USD_LIBOR_3M_LIBOR_6M.name)
      ])
      .build(),
    ]
    def resolver = ForeignRequiredGroupsResolver.foreignRequiredGroupsResolver(
    discountEntry,
    ClearingHouse.NONE,
    toCalibrationEntries(curves),
    Currency.USD,
    Set.of(Currency.USD, Currency.MXN),
    Set.of(),
    logsConsumer
    )

    1 * logsConsumer.accept([CALIBRATION_ERROR.entity("No discount curve found for currency USD")])

    when:
    def result = resolver.resolveForeignGroups()

    then:
    result.size() == 1
    result[0].discountCurve().resolvedDiscountCurrency == Currency.MXN
    result[0].discountCurve().curveName == "USD/MXN"
    def entriesCurveNames = curveNames(result[0].calibrationEntries)
    entriesCurveNames.size() == 5
    entriesCurveNames.contains("USD SOFR")
    entriesCurveNames.contains("USD/MXN")
    entriesCurveNames.contains("USD 1M")
    entriesCurveNames.contains("USD 3M")
    entriesCurveNames.contains("MXN 4W")
  }

  def "should resolve foreign discount with INDEX_BASIS curves and explicit discount"() {
    setup:
    def discountEntry = new CalibrationEntry(
    new CurveBuilder()
    .entityId("USD3M")
    .name("USD 3M")
    .curveType(CurveType.IR_INDEX)
    .nodes([curveNode(IBOR_IBOR_SWAP_NODE, USD_LIBOR_3M_LIBOR_6M.name)])
    .build(),
    null,
    Currency.USD
    )
    def curves =
    [
      new CurveBuilder()
      .entityId("MXNUSDBASIS")
      .name("USD/MXN")
      .curveType(CurveType.XCCY)
      .nodes([curveNode(XCCY_IBOR_IBOR_SWAP_NODE, "MXN-TIIE-4W-USD-LIBOR-1M")])
      .build(),
      new CurveBuilder()
      .entityId("MXN4W")
      .name("MXN 4W")
      .curveType(CurveType.IR_INDEX)
      .nodes([curveNode(FIXED_IBOR_SWAP_NODE, "MXN-FIXED-4W-TIIE-4W")])
      .build(),
      new CurveBuilder()
      .entityId("USD1M")
      .name("USD 1M")
      .curveType(CurveType.INDEX_BASIS)
      .nodes([curveNode(IBOR_IBOR_SWAP_NODE, USD_LIBOR_1M_LIBOR_3M.name)])
      .build(),
      new CurveBuilder()
      .entityId("USD3M")
      .name("USD 3M")
      .curveType(CurveType.IR_INDEX)
      .nodes([curveNode(IBOR_IBOR_SWAP_NODE, USD_LIBOR_3M_LIBOR_6M.name)])
      .build(),
      new CurveBuilder()
      .entityId("USD6M")
      .name("USD 6M")
      .curveType(CurveType.INDEX_BASIS)
      .nodes([
        curveNode(IBOR_FIXING_DEPOSIT_NODE, USD_LIBOR_6M.name),
        curveNode(IBOR_IBOR_SWAP_NODE, USD_LIBOR_3M_LIBOR_6M.name)
      ])
      .build(),
    ]
    def resolver = ForeignRequiredGroupsResolver.foreignRequiredGroupsResolver(
    discountEntry,
    ClearingHouse.NONE,
    toCalibrationEntries(curves),
    Currency.USD,
    Set.of(Currency.USD, Currency.MXN),
    Set.of(),
    logsConsumer
    )

    1 * logsConsumer.accept([CALIBRATION_ERROR.entity("No discount curve found for currency USD")])

    when:
    def result = resolver.resolveForeignGroups()

    then:
    result.size() == 1
    result[0].discountCurve().curveName == "USD/MXN"
    def entriesCurveNames = curveNames(result[0].calibrationEntries)
    entriesCurveNames.size() == 5
    entriesCurveNames.contains("USD/MXN")
    entriesCurveNames.contains("USD 1M")
    entriesCurveNames.contains("USD 3M")
    entriesCurveNames.contains("USD 6M")
    entriesCurveNames.contains("MXN 4W")
  }

  def "should not resolve XCCY foreign curve when no path available"() {
    setup:
    def discountEntry = new CalibrationEntry(
    new CurveBuilder()
    .entityId("EUR-EONIA")
    .name("EUR EONIA")
    .curveType(CurveType.INDEX_BASIS)
    .nodes([curveNode(FIXED_OVERNIGHT_SWAP_NODE, EUR_FIXED_1Y_EONIA_OIS.name)])
    .build(),
    null,
    Currency.EUR
    )
    def curves = []
    def resolver = ForeignRequiredGroupsResolver.foreignRequiredGroupsResolver(
    discountEntry,
    ClearingHouse.NONE,
    toCalibrationEntries(curves),
    Currency.EUR,
    Set.of(Currency.USD),
    Set.of(),
    logsConsumer
    )

    1 * logsConsumer.accept([CALIBRATION_ERROR.entity("Missing discount path from EUR to USD")])

    when:
    def result = resolver.resolveForeignGroups()

    then:
    result.isEmpty()
  }

  static List<String> curveNames(Set<CalibrationEntry> entries) {
    return entries
    .stream()
    .map({ c -> c.curveName })
    .toList()
  }

  static List<CalibrationEntry> toCalibrationEntries(List<Curve> curves) {
    curves.stream()
    .map(c -> {
      return new CalibrationEntry(c, CurveShifts.empty(), null)
    })
    .toList()
  }
}
