package com.solum.xplain.calibration.discounting

import static com.opengamma.strata.basics.index.IborIndices.EUR_EURIBOR_3M
import static com.opengamma.strata.basics.index.IborIndices.EUR_EURIBOR_6M
import static com.opengamma.strata.basics.index.IborIndices.USD_LIBOR_3M
import static com.opengamma.strata.basics.index.OvernightIndices.EUR_EONIA
import static com.solum.xplain.calibration.discounting.BaseRequiredIndicesGroupsResolver.baseRequiredIndicesGroupsResolver
import static com.solum.xplain.core.curvegroup.conventions.ClearingHouse.LCH
import static com.solum.xplain.core.curvegroup.conventions.ClearingHouse.NONE
import static com.solum.xplain.core.curvegroup.curve.entity.CurveSample.eur3m
import static com.solum.xplain.core.curvegroup.curve.entity.CurveSample.eur6m
import static com.solum.xplain.core.curvegroup.curve.entity.CurveSample.eurExtInflation
import static com.solum.xplain.core.curvegroup.curve.entity.CurveSample.eurExtInflationLch
import static com.solum.xplain.core.curvegroup.curve.entity.CurveSample.eurOis
import static com.solum.xplain.core.curvegroup.curve.entity.CurveSample.gbpOis
import static com.solum.xplain.core.error.Error.CALIBRATION_WARNING

import com.opengamma.strata.basics.currency.Currency
import com.opengamma.strata.basics.index.PriceIndices
import com.solum.xplain.calibration.rates.CalibrationEntry
import java.util.function.Consumer
import spock.lang.Specification

class BaseRequiredIndicesGroupsResolverTest extends Specification {

  Consumer logsConsumer = Mock(Consumer)

  def "should resolve base required indices groups"() {
    setup:
    def baseEntry = new CalibrationEntry(eurOis(), null, Currency.EUR)
    def eur3mEntry = new CalibrationEntry(eur3m(), null, null)
    def eur6mEntry = new CalibrationEntry(eur6m(), null, null)

    def entries = [baseEntry, eur3mEntry, eur6mEntry]
    def resolver = baseRequiredIndicesGroupsResolver(NONE, Set.of(EUR_EONIA, EUR_EURIBOR_3M, EUR_EURIBOR_6M, USD_LIBOR_3M), entries, logsConsumer)


    when:
    def result = resolver.resolveIndicesGroups(baseEntry)

    then:
    0 * logsConsumer.accept(_)
    result.size() == 2
    result[0].discountCurve() == baseEntry
    result[0].calibrationEntries() == Set.of(baseEntry, eur3mEntry)
    result[1].discountCurve() == baseEntry
    result[1].calibrationEntries() == Set.of(baseEntry, eur3mEntry, eur6mEntry)
  }

  def "should fail to resolve base required indices when missing curves"() {
    setup:
    def baseEntry = new CalibrationEntry(eurOis(), null, Currency.EUR)

    def entries = [baseEntry]
    def resolver = baseRequiredIndicesGroupsResolver(NONE, Set.of(EUR_EURIBOR_3M), entries, logsConsumer)

    when:
    def result = resolver.resolveIndicesGroups(baseEntry)

    then:
    1 * logsConsumer.accept([CALIBRATION_WARNING.entity("Curve for required index EUR-EURIBOR-3M missing")])
    result.size() == 0
  }

  def "should fail to resolve required CLEARED index curve"() {
    setup:
    def baseEntry = new CalibrationEntry(gbpOis(), null, Currency.GBP)

    def entries = [baseEntry]
    def resolver = baseRequiredIndicesGroupsResolver(LCH, Set.of(PriceIndices.GB_RPI), entries, logsConsumer)

    when:
    def result = resolver.resolveIndicesGroups(baseEntry)

    then:
    1 * logsConsumer.accept([CALIBRATION_WARNING.entity("Curve for required index GB-RPI-LCH missing")])
    result.size() == 0
  }

  def "should resolve base required indices with correct clearing house"() {
    setup:
    def baseEntry = new CalibrationEntry(eurOis(), null, Currency.EUR)
    def eurInflation = new CalibrationEntry(eurExtInflation(), null, null)
    def eurInflationLch = new CalibrationEntry(eurExtInflationLch(), null, null)

    def entries = [baseEntry, eurInflation, eurInflationLch]
    def resolver = baseRequiredIndicesGroupsResolver(clearingHouse, Set.of(PriceIndices.EU_EXT_CPI), entries, logsConsumer)

    when:
    def result = resolver.resolveIndicesGroups(baseEntry)

    then:
    result.size() == 1
    result[0].discountCurve() == baseEntry
    result[0].calibrationEntries().curveName.size() == 2
    result[0].calibrationEntries().curveName.containsAll(["EUR EONIA", expected])

    where:
    clearingHouse | expected
    NONE          | "EU EXT CPI"
    LCH           | "EU EXT CPI LCH"
  }
}
