package com.solum.xplain.calibration.discounting

import com.opengamma.strata.basics.currency.Currency
import com.solum.xplain.calibration.rates.CalibrationEntry
import spock.lang.Specification

class CalibrationDiscountSubGroupTest extends Specification {

  def "should construct base discount group"() {
    setup:
    def ccy = Currency.USD
    def entry = Mock(CalibrationEntry)
    2 * entry.getResolvedDiscountCurrency() >> Currency.USD
    2 * entry.getCurveName() >> "EUR/USD"

    when:
    def result = CalibrationDiscountSubGroup.newOfBase(ccy, Set.of(entry))

    then:
    result.discountCurve() == entry
    result.calibrationEntries() == Set.of(entry)
    result.groupDescription() == "Base discount group with discount curve EUR/USD [USD], curves [EUR/USD]"
  }

  def "should construct foreign discount group"() {
    setup:
    def ccy = Currency.USD
    def baseEntry = Mock(CalibrationEntry)
    1 * baseEntry.getResolvedDiscountCurrency() >> Currency.EUR
    1 * baseEntry.getCurveName() >> "EUR ESTR"
    def entry = Mock(CalibrationEntry)
    2 * entry.getResolvedDiscountCurrency() >> Currency.USD
    2 * entry.getCurveName() >> "EUR/USD"

    when:
    def result = CalibrationDiscountSubGroup.newOfForeign(baseEntry, ccy, Set.of(entry))

    then:
    result.discountCurve() == entry
    result.calibrationEntries() == Set.of(entry)
    result.groupDescription() == "Foreign discount group with base discount curve EUR ESTR [EUR], foreign discount curve EUR/USD [USD], curves [EUR/USD]"
  }
}
