package com.solum.xplain.calibration.volatility

import static com.solum.xplain.core.curvegroup.volatility.entity.VolatilitySurfaceBuilder.sampleSurface
import static com.solum.xplain.core.curvemarket.node.ValidNodesFilter.EMPTY_FILTER
import static com.solum.xplain.core.market.MarketDataSample.ogMarketData

import com.solum.xplain.core.curvegroup.volatility.classifier.VolatilitySurfaceType
import spock.lang.Specification

class VolatilityCalibrationDataResolverTest extends Specification {
  def "should resolve surface data when surface type STRIKE"() {
    setup:
    def surface = sampleSurface(VolatilitySurfaceType.STRIKE)
    def calculation = new VolatilityCalibrationDataResolver(ogMarketData(), surface, EMPTY_FILTER)

    when:
    def result = calculation.resolveVolatilityData()

    then:
    result.isRight()
    result.getOrNull().valuationDate == ogMarketData().valuationDate
    result.getOrNull().surface == surface
    result.getOrNull().nodes.size() == 4
    result.getOrNull().skews[new BigDecimal("0.01")].size() == 4
    result.getOrNull().skews[new BigDecimal("0.1")].size() == 4
  }

  def "should resolve surface data when surface type ATM"() {
    setup:
    def surface = sampleSurface(VolatilitySurfaceType.ATM_ONLY)
    def calculation = new VolatilityCalibrationDataResolver(ogMarketData(), surface, EMPTY_FILTER)

    when:
    def result = calculation.resolveVolatilityData()

    then:
    result.isRight()
    result.getOrNull().valuationDate == ogMarketData().valuationDate
    result.getOrNull().surface == surface
    result.getOrNull().nodes.size() == 4
    result.getOrNull().nodes[0].getValue() == 0.004
    result.getOrNull().nodes[0].getTenor() == "1Y"
    result.getOrNull().nodes[0].getExpiry() == "1Y"
    result.getOrNull().skews.isEmpty()
  }
}
