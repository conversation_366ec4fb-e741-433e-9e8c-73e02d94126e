package com.solum.xplain.calibration


import com.solum.xplain.core.error.Error
import com.solum.xplain.core.error.ErrorItem
import java.util.function.Consumer
import spock.lang.Specification

class DistinctLogConsumerTest extends Specification {

  def "should consume logs without duplicates"() {
    setup:
    def logConsumer = Mock(Consumer<List<ErrorItem>>)
    def distinctLogConsumer = new DistinctLogConsumer(logConsumer)

    def w1 = Error.CALIBRATION_WARNING.entity("Warning1")
    def w2 = Error.CALIBRATION_WARNING.entity("Warning2")

    when:
    distinctLogConsumer.accept([w1])
    distinctLogConsumer.accept([w1])
    distinctLogConsumer.accept([w1, w2])
    distinctLogConsumer.accept([w2])

    then:
    1 * logConsumer.accept([w1])
    1 * logConsumer.accept([w2])
  }

  def "should check if has logs of type"() {
    setup:
    def logConsumer = Mock(Consumer<List<ErrorItem>>)
    def distinctLogConsumer = new DistinctLogConsumer(logConsumer)
    distinctLogConsumer.accept([Error.CALIBRATION_WARNING.entity("Warning1")])

    when:
    def hasErrors = distinctLogConsumer.hasLogs(Error.CALIBRATION_ERROR)
    def hasWarning = distinctLogConsumer.hasLogs(Error.CALIBRATION_WARNING)

    then:
    !hasErrors
    hasWarning
  }
}
