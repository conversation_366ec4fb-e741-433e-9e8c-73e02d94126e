package com.solum.xplain.calibration

import static com.solum.xplain.core.curveconfiguration.CurveConfigurationHelper.instrumentResolver
import static com.solum.xplain.core.curvemarket.pricetype.InstrumentPriceType.BID_PRICE
import static com.solum.xplain.core.curvemarket.pricetype.InstrumentPriceType.MID_PRICE
import static com.solum.xplain.core.curvemarket.pricetype.InstrumentPriceTypeResolver.priceTypeResolver
import static com.solum.xplain.core.market.value.MdkProviderBidAskType.BID_ONLY

import com.solum.xplain.core.curveconfiguration.CurveConfigurationInstrumentResolver
import com.solum.xplain.core.curveconfiguration.entity.CurveConfiguration
import com.solum.xplain.core.curveconfiguration.entity.MarketDataProviders
import com.solum.xplain.core.curvegroup.curve.entity.CurveNode
import com.solum.xplain.core.curvegroup.instrument.CoreInstrumentType
import com.solum.xplain.core.curvegroup.instrument.InstrumentDefinition
import com.solum.xplain.core.curvemarket.datasource.MarketDataSourceType
import com.solum.xplain.core.curvemarket.node.NodeInstrumentWrapper
import com.solum.xplain.core.curvemarket.node.ValidMarketDataNodesFilter
import com.solum.xplain.core.curvemarket.pricetype.InstrumentPriceRequirements
import com.solum.xplain.core.curvemarket.pricetype.InstrumentPriceType
import com.solum.xplain.core.error.ErrorItem
import com.solum.xplain.core.market.value.MarketDataKeyView
import com.solum.xplain.core.market.value.MarketDataProviderTickerView
import spock.lang.Specification

class ValidMarketDataNodesFilterTest extends Specification {

  def "should return all nodes with no errors"() {
    setup:
    def errorList = [] as List<ErrorItem>
    def filter = filter(errorList, sourceType)
    def node = new CurveNode()
    when:
    def result = filter.filterNodes([
      NodeInstrumentWrapper.of(node, InstrumentDefinition.ofIrCurve(
      "EUR",
      "EUR 3M",
      CoreInstrumentType.FIXED_IBOR_SWAP,
      "3M",
      "3M",
      "key",
      "name"
      ))
    ])
    then:
    result.size() == 1
    result[0] == node
    errorList.isEmpty()
    where:
    sourceType                               | _
    MarketDataSourceType.RAW_PRIMARY         | _
    MarketDataSourceType.PRELIMINARY_PRIMARY | _
    MarketDataSourceType.OVERLAY             | _
  }

  def "should return error for missing curve configuration mapping"() {
    setup:
    def errorList = [] as List<ErrorItem>
    def filter = filter(errorList, MarketDataSourceType.RAW_PRIMARY)
    def node = new CurveNode()
    when:
    def result = filter.filterNodes([
      NodeInstrumentWrapper.of(node, InstrumentDefinition.ofIrCurve(
      "EUR",
      "EUR 3M",
      CoreInstrumentType.FRA,
      "3M",
      "6Mx9M",
      "key",
      "name"
      ))
    ])
    then:
    result.size() == 0
    errorList.size() == 1
    errorList[0].description == "No curve configuration mapping found for instrument: key"
  }

  def "should return error for missing MDK mapping"() {
    setup:
    def errorList = [] as List<ErrorItem>
    def filter = filter(errorList, MarketDataSourceType.RAW_PRIMARY)
    def node = new CurveNode()
    when:
    def result = filter.filterNodes([
      NodeInstrumentWrapper.of(node, InstrumentDefinition.ofIrCurve(
      "EUR",
      "EUR 3M",
      CoreInstrumentType.FIXED_IBOR_SWAP,
      "3M",
      "3M",
      "key2",
      "name"
      ))
    ])
    then:
    result.size() == 0
    errorList.size() == 1
    errorList[0].description == "There is no mapping for key: key2"
  }

  def "should return error for missing MDK mapping provider when MDK has no providers"() {
    setup:
    def errorList = [] as List<ErrorItem>
    def filter = ValidMarketDataNodesFilter.ofValidMarketData(
      MarketDataSourceType.RAW_SECONDARY,
      instrumentResolver(
      "name",
      "curveGroupId",
      [(CoreInstrumentType.FIXED_IBOR_SWAP): new MarketDataProviders(primary: "BBG", secondary: "CCH")]
      ),
      priceTypeResolver(InstrumentPriceRequirements.bidRequirements()),
      [new MarketDataKeyView(key: "key")], { v ->
        errorList.addAll(v)
      }
      )
    def node = new CurveNode()

    when:
    def result = filter.filterNodes([
      NodeInstrumentWrapper.of(node, InstrumentDefinition.ofIrCurve(
      "EUR",
      "EUR 3M",
      CoreInstrumentType.FIXED_IBOR_SWAP,
      "3M",
      "3M",
      "key",
      "name"
      ))
    ])

    then:
    result.size() == 0
    errorList.size() == 1
    errorList[0].description == "Market data key: key does not have mapping for provider: CCH"
  }

  def "should return error for missing MDK mapping provider"() {
    setup:
    def errorList = [] as List<ErrorItem>
    def filter = filter(errorList, MarketDataSourceType.RAW_SECONDARY)
    def node = new CurveNode()
    when:
    def result = filter.filterNodes([
      NodeInstrumentWrapper.of(node, InstrumentDefinition.ofIrCurve(
      "EUR",
      "EUR 3M",
      CoreInstrumentType.FIXED_IBOR_SWAP,
      "3M",
      "3M",
      "key",
      "name"
      ))
    ])
    then:
    result.size() == 0
    errorList.size() == 1
    errorList[0].description == "Market data key: key does not have mapping for provider: CCH"
  }

  def "should return error for missing MDK mapping bidAsk"() {
    setup:
    def errorList = [] as List<ErrorItem>
    def filter = filter(errorList, MarketDataSourceType.RAW_PRIMARY, MID_PRICE)
    def node = new CurveNode()
    when:
    def result = filter.filterNodes([
      NodeInstrumentWrapper.of(node, InstrumentDefinition.ofIrCurve(
      "EUR",
      "EUR 3M",
      CoreInstrumentType.FIXED_IBOR_SWAP,
      "3M",
      "3M",
      "key",
      "name"
      ))
    ])
    then:
    result.size() == 0
    errorList.size() == 1
    errorList[0].description == "Market data key: key does not have mapping for required price type: Mid or Bid/Ask"
  }

  def "should return error for curve config provider mapping"() {
    setup:
    def config = new CurveConfiguration(
      instruments: [(CoreInstrumentType.FIXED_IBOR_SWAP): new MarketDataProviders()]
      )
    def errorList = [] as List<ErrorItem>
    def filter = ValidMarketDataNodesFilter.ofValidMarketData(
      MarketDataSourceType.RAW_PRIMARY,
      CurveConfigurationInstrumentResolver.fromConfiguration(config),
      priceTypeResolver(InstrumentPriceRequirements.bidRequirements()),
      [], { v ->
        errorList.addAll(v)
      }
      )
    def node = new CurveNode()
    when:
    def result = filter.filterNodes([
      NodeInstrumentWrapper.of(node, InstrumentDefinition.ofIrCurve(
      "EUR",
      "EUR 3M",
      CoreInstrumentType.FIXED_IBOR_SWAP,
      "3M",
      "3M",
      "key",
      "name"
      ))
    ])
    then:
    result.size() == 0
    errorList.size() == 1
    errorList[0].description == "Curve configuration doesn't have mapping for required provider"
  }

  def "should not return error for curve secondary provider when primary exists"() {
    def config = new CurveConfiguration(
      instruments: [(CoreInstrumentType.FIXED_IBOR_SWAP): new MarketDataProviders(primary: "BBG")]
      )

    setup:
    def errorList = [] as List<ErrorItem>
    def filter = ValidMarketDataNodesFilter.ofValidMarketData(
      sourceType,
      CurveConfigurationInstrumentResolver.fromConfiguration(config),
      priceTypeResolver(InstrumentPriceRequirements.bidRequirements()),
      [
        new MarketDataKeyView(
        key: "key",
        providerTickers: [new MarketDataProviderTickerView(code: "BBG", bidAskType: BID_ONLY),]
        )
      ], { v ->
        errorList.addAll(v)
      }
      )
    def node = new CurveNode()
    when:
    def result = filter.filterNodes([
      NodeInstrumentWrapper.of(node, InstrumentDefinition.ofIrCurve(
      "EUR",
      "EUR 3M",
      CoreInstrumentType.FIXED_IBOR_SWAP,
      "3M",
      "3M",
      "key",
      "name"
      ))
    ])
    then:
    result.size() == 1
    errorList.empty
    where:
    sourceType                                 | _
    MarketDataSourceType.PRELIMINARY_SECONDARY | _
    MarketDataSourceType.RAW_SECONDARY         | _
  }

  def filter(List<ErrorItem> errorLog, MarketDataSourceType sourceType, InstrumentPriceType priceType = BID_PRICE) {
    return ValidMarketDataNodesFilter.ofValidMarketData(
      sourceType,
      instrumentResolver(
      "name",
      "curveGroupId",
      [(CoreInstrumentType.FIXED_IBOR_SWAP): new MarketDataProviders(primary: "BBG", secondary: "CCH")]
      ),
      priceTypeResolver(new InstrumentPriceRequirements(priceType, priceType, priceType, priceType, priceType)),
      [
        new MarketDataKeyView(
        key: "key",
        providerTickers: [
          new MarketDataProviderTickerView(code: "BBG", bidAskType: BID_ONLY),
          new MarketDataProviderTickerView(code: "MMR", bidAskType: BID_ONLY)
        ]
        )
      ], { v ->
        errorLog.addAll(v)
      }
      )
  }
}
