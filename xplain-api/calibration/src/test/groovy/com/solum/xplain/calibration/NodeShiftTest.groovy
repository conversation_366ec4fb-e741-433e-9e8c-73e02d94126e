package com.solum.xplain.calibration

import com.solum.xplain.calibration.value.NodeShift
import spock.lang.Specification
import spock.lang.Unroll

class NodeShiftTest extends Specification {

  def "should resolve correct shift"() {
    setup:
    def shift = new NodeShift("nodeId", 1d)

    when:
    def result = shift.resolvedAdditionalSpread()

    then:
    result == 1e-4
  }

  @Unroll
  "should return result node #nodeId with #result"() {
    setup:
    def shift = new NodeShift(nodeId, 1d)

    expect:
    shift.nodeIdIs("nodeId") == result

    where:
    nodeId   | result
    "nodeId" | true
    "other"  | false
  }
}
