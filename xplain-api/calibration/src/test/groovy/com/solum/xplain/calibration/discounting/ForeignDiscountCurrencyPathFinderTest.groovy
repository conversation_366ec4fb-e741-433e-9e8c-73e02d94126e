package com.solum.xplain.calibration.discounting

import static com.opengamma.strata.basics.currency.Currency.AUD
import static com.opengamma.strata.basics.currency.Currency.EUR
import static com.opengamma.strata.basics.currency.Currency.GBP
import static com.opengamma.strata.basics.currency.Currency.MXN
import static com.opengamma.strata.basics.currency.Currency.USD
import static java.util.Optional.empty
import static java.util.Optional.of

import com.opengamma.strata.basics.currency.CurrencyPair
import spock.lang.Specification
import spock.lang.Unroll

class ForeignDiscountCurrencyPathFinderTest extends Specification {

  @Unroll
  def "should resolve path #base #triangulation #target #xccys then #result"() {
    setup:
    def finder = new ForeignDiscountCurrencyPathFinder((List<CurrencyPair>) xccys, base, triangulation)

    expect:
    finder.resolvePathTo(target) == result

    where:
    base | triangulation | target | xccys                                                                   | result
    EUR  | GBP           | USD    | List.of()                                                               | empty()
    EUR  | GBP           | USD    | List.of(pair(EUR, GBP))                                                 | empty()
    EUR  | GBP           | USD    | List.of(pair(EUR, USD))                                                 | of(new ForeignDiscountCurrencyPath([EUR, USD]))

    EUR  | GBP           | USD    | List.of(pair(EUR, USD), pair(EUR, GBP), pair(GBP, USD))                 | of(new ForeignDiscountCurrencyPath([EUR, USD]))
    EUR  | AUD           | USD    | List.of(pair(EUR, GBP), pair(EUR, AUD), pair(GBP, USD), pair(AUD, USD)) | of(new ForeignDiscountCurrencyPath([EUR, AUD, USD]))
    EUR  | GBP           | USD    | List.of(pair(EUR, GBP), pair(EUR, AUD), pair(GBP, USD), pair(AUD, USD)) | of(new ForeignDiscountCurrencyPath([EUR, GBP, USD]))

    EUR  | EUR           | USD    | List.of(pair(EUR, GBP), pair(EUR, AUD), pair(GBP, USD), pair(AUD, USD)) | of(new ForeignDiscountCurrencyPath([EUR, GBP, USD]))
    EUR  | EUR           | USD    | List.of(pair(EUR, AUD), pair(EUR, GBP), pair(GBP, USD))                 | of(new ForeignDiscountCurrencyPath([EUR, GBP, USD]))
    EUR  | EUR           | MXN    | List.of(pair(EUR, GBP), pair(GBP, AUD), pair(AUD, USD), pair(USD, MXN)) | of(new ForeignDiscountCurrencyPath([EUR, GBP, AUD, USD, MXN]))
    EUR  | EUR           | MXN    | List.of(
      pair(EUR, GBP),
      pair(EUR, USD),
      pair(EUR, AUD),
      pair(AUD, MXN),
      pair(GBP, AUD),
      pair(GBP, USD),
      pair(USD, GBP),
      pair(USD, AUD),
      pair(USD, MXN))                                                                                     | of(new ForeignDiscountCurrencyPath([EUR, GBP, AUD, MXN]))
    EUR  | USD           | MXN    | List.of(
      pair(EUR, GBP),
      pair(EUR, USD),
      pair(EUR, AUD),
      pair(AUD, MXN),
      pair(GBP, AUD),
      pair(GBP, USD),
      pair(USD, GBP),
      pair(USD, AUD),
      pair(USD, MXN))                                                                                     | of(new ForeignDiscountCurrencyPath([EUR, USD, MXN]))
  }

  private static CurrencyPair pair(ccy1, ccy2) {
    return CurrencyPair.of(ccy1, ccy2)
  }
}
