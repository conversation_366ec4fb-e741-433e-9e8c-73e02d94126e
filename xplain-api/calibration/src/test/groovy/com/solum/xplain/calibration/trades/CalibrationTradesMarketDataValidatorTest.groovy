package com.solum.xplain.calibration.trades

import static com.opengamma.strata.product.index.type.IborFutureContractSpecs.GBP_LIBOR_3M_IMM_ICE
import static com.solum.xplain.core.market.mapping.MarketDataUtils.quoteId

import com.opengamma.strata.basics.date.SequenceDate
import com.opengamma.strata.data.ImmutableMarketData
import com.opengamma.strata.product.index.type.IborFutureTemplate
import com.solum.xplain.core.curvegroup.curve.extension.IborFutureCurveNode
import java.time.LocalDate
import spock.lang.Specification

class CalibrationTradesMarketDataValidatorTest extends Specification {

  def "should return error for negative ibor future data"() {
    setup:
    def rateId = quoteId("KEY")
    def marketData = ImmutableMarketData.builder(LocalDate.now())
      .addValue(rateId, -1d)
      .build()
    def template = IborFutureTemplate.of(SequenceDate.base(1), GBP_LIBOR_3M_IMM_ICE)
    def node = IborFutureCurveNode.of(template, rateId)
    def errors = CalibrationTradesMarketDataValidator.newOf(marketData, "NAME")
      .nodeErrors(node)
    expect:
    errors.size() == 1
    errors[0].description == "Invalid market data required by curve NAME identifier: 'QuoteId:XPL~KEY/MarketValue'. Value must be positive, but was -1.0"
  }

  def "should return error for missing market data"() {
    setup:
    def rateId = quoteId("KEY")
    def marketData = ImmutableMarketData.builder(LocalDate.now())
      .build()
    def template = IborFutureTemplate.of(SequenceDate.base(1), GBP_LIBOR_3M_IMM_ICE)
    def node = IborFutureCurveNode.of(template, rateId)
    def errors = CalibrationTradesMarketDataValidator.newOf(marketData, "NAME")
      .nodeErrors(node)
    expect:
    errors.size() == 1
    errors[0].description == "Market data required by curve NAME not found for identifier: 'QuoteId:XPL~KEY/MarketValue' of class 'QuoteId'"
  }
}
