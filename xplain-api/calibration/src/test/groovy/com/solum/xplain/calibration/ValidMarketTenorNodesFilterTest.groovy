package com.solum.xplain.calibration

import com.opengamma.strata.basics.ReferenceData
import com.opengamma.strata.market.curve.CurveNodeDateOrder
import com.opengamma.strata.product.fx.type.FxSwapConvention
import com.solum.xplain.core.classifiers.CurveNodeTypes
import com.solum.xplain.core.curvegroup.curve.entity.CurveNode
import com.solum.xplain.core.curvegroup.instrument.CoreAssetClass
import com.solum.xplain.core.curvegroup.instrument.CoreInstrumentType
import com.solum.xplain.core.curvegroup.instrument.InstrumentDefinition
import com.solum.xplain.core.curvemarket.node.NodeInstrumentWrapper
import com.solum.xplain.core.curvemarket.node.ValidMarketTenorNodesFilter
import com.solum.xplain.core.error.ErrorItem
import com.solum.xplain.extensions.calendar.ValuationDateReferenceData
import java.time.LocalDate
import spock.lang.Specification

class ValidMarketTenorNodesFilterTest extends Specification {

  static VAL_DT = LocalDate.parse("2023-06-01")
  static VAL_DT_ON_EQ_SPOT = LocalDate.parse("2023-06-16") // when a US ON node end date will == spot date

  def "should return all nodes with no errors"() {
    setup:
    def refData = ValuationDateReferenceData.wrap(ReferenceData.standard(), VAL_DT)
    def warningList = [] as List<ErrorItem>
    def filter = filter(VAL_DT, refData, warningList)
    def node = new CurveNode()
    node.setType(CurveNodeTypes.FX_SWAP_NODE)
    node.setConvention(FxSwapConvention.of("USD/ZAR").toString())
    node.setPeriod("TN")
    when:
    def result = filter.filterNodes([
      NodeInstrumentWrapper.of(node, InstrumentDefinition.ofFx(
      CoreAssetClass.FX_SWAP,
      "USD/ZAR",
      CoreInstrumentType.FX_SWAP,
      "TN_USD/ZAR",
      "USD/ZAR"
      ))
    ])
    then:
    result.size() == 1
    result[0] == node
    warningList.isEmpty()
  }

  def "should return dropping TN node warning if TN node end date > spot date"() {
    setup:
    def refData = ValuationDateReferenceData.wrap(ReferenceData.standard(), VAL_DT_ON_EQ_SPOT)
    def warningList = [] as List<ErrorItem>
    def filter = filter(VAL_DT_ON_EQ_SPOT, refData, warningList)
    when:
    def result = filter.filterNodes([
      NodeInstrumentWrapper.of(usdZarSwapNode("TN"), usdZarInstrumentDef("TN_USD/ZAR")),
      NodeInstrumentWrapper.of(usdZarSwapNode("1D"), usdZarInstrumentDef("1D_USD/ZAR"))
    ])
    then:
    result.size() == 1
    result[0].period == "1D"
    warningList.size() == 1
    warningList[0].description == "Market tenor node with key TN_USD/ZAR was dropped since its end date exceeds the spot date"
  }

  def filter(LocalDate valuationDate, ReferenceData referenceData, List<ErrorItem> warningLog) {
    return ValidMarketTenorNodesFilter.ofValidMarketTenor(
      valuationDate,
      referenceData,
      CurveNodeDateOrder.DEFAULT,
      { v -> warningLog.addAll(v) })
  }

  def usdZarSwapNode(String period) {
    def node = new CurveNode()
    node.setType(CurveNodeTypes.FX_SWAP_NODE)
    node.setConvention(FxSwapConvention.of("USD/ZAR").toString())
    node.setPeriod(period)
    return node
  }

  def usdZarInstrumentDef(String key) {
    return InstrumentDefinition.ofFx(
      CoreAssetClass.FX_SWAP,
      "USD/ZAR",
      CoreInstrumentType.FX_SWAP,
      key,
      "USD/ZAR"
      )
  }
}
