package com.solum.xplain.calibration.volatility

import static com.solum.xplain.core.curvegroup.curvegroup.entity.CurveGroupBuilder.curveGroup
import static com.solum.xplain.core.curvemarket.datasource.MarketDataSourceType.RAW_PRIMARY
import static com.solum.xplain.core.curvemarket.node.ValidNodesFilter.EMPTY_FILTER

import com.solum.xplain.calibration.value.CalibrationOptions
import com.solum.xplain.core.common.versions.BitemporalDate
import com.solum.xplain.core.curvegroup.volatilityfx.CurveGroupFxVolatilityMapper
import com.solum.xplain.core.curvegroup.volatilityfx.CurveGroupFxVolatilityRepository
import com.solum.xplain.core.curvegroup.volatilityfx.entity.CurveGroupFxVolatility
import com.solum.xplain.core.curvegroup.volatilityfx.entity.CurveGroupFxVolatilityNode
import com.solum.xplain.core.curvemarket.CurveConfigMarketStateKey
import com.solum.xplain.core.curvemarket.pricetype.InstrumentPriceRequirements
import com.solum.xplain.core.error.Error
import com.solum.xplain.core.market.MarketDataSample
import io.atlassian.fugue.Either
import spock.lang.Specification

class FxVolatilitiesCalibrationServiceTest extends Specification {

  def "should calibrate and verify fxvols"() {
    setup:
    def errors = []

    def stateDate = BitemporalDate.newOfNow()
    def curveDate = stateDate.getActualDate()
    def stateForm = new CurveConfigMarketStateKey("mdId", stateDate, curveDate, "ccId", RAW_PRIMARY,
    InstrumentPriceRequirements.bidRequirements())
    def options = new CalibrationOptions(curveGroup(), EMPTY_FILTER, null, stateForm, null, null, null, null)

    def repository = Mock(CurveGroupFxVolatilityRepository)
    1 * repository.getActiveVolatility(options.curveGroup.id, stateDate) >> Either.right(
    new CurveGroupFxVolatility(
    nodes: [new CurveGroupFxVolatilityNode(expiry: "25Y", domesticCurrency: "EUR", foreignCurrency: "USD", delta1: 1)]
    ))
    def mapper = Mock(CurveGroupFxVolatilityMapper)

    def service = new FxVolatilitiesCalibrationService(repository, mapper)

    when:
    service.calibrate(options, MarketDataSample.ogMarketData(), l -> errors.addAll(l))

    then:
    errors == [Error.CALIBRATION_ERROR.entity("Market data required by FX volatility 25Y_EUR/USDV not found")]
  }
}
