package com.solum.xplain.calibration

import static com.solum.xplain.core.market.MarketDataSample.VAL_DT

import com.opengamma.strata.basics.ReferenceData
import com.opengamma.strata.basics.index.IborIndices
import com.opengamma.strata.collect.array.DoubleArray
import com.opengamma.strata.pricer.model.HullWhiteOneFactorPiecewiseConstantParameters
import com.solum.xplain.calibration.settings.MeasuresWithConvexityAdjustment
import com.solum.xplain.core.settings.entity.ConvexityAdjustmentsSettings
import spock.lang.Specification

class MeasuresWithConvexityAdjustmentTest extends Specification {

  def "should return construct adjusted measures"() {
    setup:
    def model = HullWhiteOneFactorPiecewiseConstantParameters.of(0.1,
      DoubleArray.of(1, 2),
      DoubleArray.of(1))
    def settings = Mock(ConvexityAdjustmentsSettings.class)
    settings.definitions() >> [(IborIndices.EUR_EURIBOR_3M): model]
    def measures = MeasuresWithConvexityAdjustment.ofConvexitySettings(settings, VAL_DT, ReferenceData.standard())

    when:
    def calibrationMeasures = measures.calibrationMeasures()

    then:
    calibrationMeasures.name == "ParSpreadWithAdjustedIborFuture"
  }
}
