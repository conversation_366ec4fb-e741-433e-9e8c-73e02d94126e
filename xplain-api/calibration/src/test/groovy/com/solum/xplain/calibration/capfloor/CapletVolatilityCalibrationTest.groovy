package com.solum.xplain.calibration.capfloor

import static com.solum.xplain.core.calibration.CurveSample.discountCurve
import static com.solum.xplain.core.calibration.CurveSample.indexCurve
import static com.solum.xplain.core.curvegroup.volatility.entity.VolatilitySurfaceBuilder.sampleSurface
import static com.solum.xplain.core.curvemarket.node.ValidNodesFilter.EMPTY_FILTER
import static com.solum.xplain.core.error.Error.CALIBRATION_ERROR
import static com.solum.xplain.core.market.MarketDataSample.ogMarketData
import static java.time.LocalDate.now

import com.opengamma.strata.basics.currency.Currency
import com.opengamma.strata.basics.currency.CurrencyPair
import com.opengamma.strata.basics.index.IborIndices
import com.opengamma.strata.pricer.rate.ImmutableRatesProvider
import com.solum.xplain.calibration.rates.RatesCalibrationResult
import com.solum.xplain.calibration.rates.spot01.ShiftedFxRatesProvider
import com.solum.xplain.core.curvegroup.volatility.classifier.VolatilitySurfaceType
import spock.lang.Specification

class CapletVolatilityCalibrationTest extends Specification {

  def "should calibrate volatilities"() {
    setup:
    def surface = sampleSurface(VolatilitySurfaceType.ATM_ONLY)
    def ratesProvider = ImmutableRatesProvider.builder(now())
      .discountCurve(Currency.EUR, discountCurve())
      .indexCurve(IborIndices.EUR_EURIBOR_3M, indexCurve())
      .build()

    def rates = new RatesCalibrationResult(ratesProvider, [])

    when:
    def result = CapletVolatilityCalibration.of(surface, rates, ogMarketData(), EMPTY_FILTER)
      .flatMap({ it.calibrate() })

    then:
    result.isRight()
  }

  def "should calibrate volatilities with shifted rates provider"() {
    setup:
    def surface = sampleSurface(VolatilitySurfaceType.ATM_ONLY)
    def ratesProvider = ImmutableRatesProvider.builder(now())
      .discountCurve(Currency.EUR, discountCurve())
      .indexCurve(IborIndices.EUR_EURIBOR_3M, indexCurve())
      .build()

    def rates = new RatesCalibrationResult(ratesProvider, [new ShiftedFxRatesProvider(CurrencyPair.of(Currency.EUR, Currency.USD), ratesProvider)])

    when:
    def result = CapletVolatilityCalibration.of(surface, rates, ogMarketData(), EMPTY_FILTER)
      .flatMap({ it.calibrate() })

    then:
    result.isRight()
    with(result.getOrNull()) {
      getResult() != null
      fxShiftedResults.size() == 1
      fxShiftedResults[0].getResult() == getResult()
    }
  }

  def "should return error when failed to calibrate volatilities"() {
    setup:
    def surface = sampleSurface(VolatilitySurfaceType.ATM_ONLY)
    def ratesProvider = ImmutableRatesProvider.builder(now()).build()
    def rates = new RatesCalibrationResult(ratesProvider, [])

    when:
    def result = CapletVolatilityCalibration.of(surface, rates, ogMarketData(), EMPTY_FILTER)
      .flatMap({ it.calibrate() })

    then:
    result.isLeft()
    result.left().get().getReason() == CALIBRATION_ERROR
    result.left().get().getDescription() == "Error calibrating volatility curve (cap/floors) EUR 3M Vols: Unable to find discount curve: EUR"
  }
}
