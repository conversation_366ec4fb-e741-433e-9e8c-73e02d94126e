package com.solum.xplain.calibration.discounting

import static BaseDiscountSubGroupResolver.baseDiscountGroupResolver
import static BaseRequiredIndicesGroupsResolver.baseRequiredIndicesGroupsResolver
import static CalibrationDiscountSubGroupsResolver.subGroupsResolver
import static ForeignRequiredGroupsResolver.foreignRequiredGroupsResolver
import static com.solum.xplain.core.curvegroup.conventions.ClearingHouse.NONE

import com.opengamma.strata.basics.currency.Currency
import com.opengamma.strata.basics.index.IborIndices
import com.solum.xplain.calibration.rates.CalibrationEntry
import com.solum.xplain.core.classifiers.discounting.IndexBasedDiscountCurrencies
import io.atlassian.fugue.Either
import java.time.LocalDate
import java.util.function.Consumer
import spock.lang.Specification

class CalibrationDiscountSubGroupsResolverTest extends Specification {
  def static CURVE_DATE = LocalDate.parse("2017-01-01")

  def "should construct groups resolver"() {
    setup:
    def ccy = IndexBasedDiscountCurrencies.getOf(Currency.EUR)
    def calibrationEntries = []
    def triangulationCcy = Currency.USD
    def foreignCcys = Set.of(Currency.USD)
    def requiredIndices = Set.of(IborIndices.USD_LIBOR_3M)
    def logs = Mock(Consumer)
    def dscCurve = Mock(CalibrationEntry)
    2 * dscCurve.getResolvedDiscountCurrency() >> Currency.USD

    when:
    def result = subGroupsResolver(ccy, false, NONE, CURVE_DATE, null, triangulationCcy, calibrationEntries, foreignCcys, requiredIndices, logs)

    then:
    result.baseDiscountSubGroupResolver == baseDiscountGroupResolver(CURVE_DATE, ccy, false, NONE, null, calibrationEntries)
    result.baseRequiredIndicesGroupsResolver == baseRequiredIndicesGroupsResolver(NONE, requiredIndices, calibrationEntries, logs)
    result.foreignGroupsResolver.apply(dscCurve) == foreignRequiredGroupsResolver(
      dscCurve,
      NONE,
      calibrationEntries,
      triangulationCcy,
      foreignCcys,
      requiredIndices,
      logs)
  }

  def "should correctly resolve expected groups"() {
    setup:
    def entry = Mock(CalibrationEntry)
    def group = Mock(CalibrationDiscountSubGroup)
    2 * group.discountCurve() >> entry
    def additionalGroup = Mock(CalibrationDiscountSubGroup)
    def foreignGroup = Mock(CalibrationDiscountSubGroup)
    def baseDiscountGroupResolver = Mock(BaseDiscountSubGroupResolver)
    1 * baseDiscountGroupResolver.resolveBaseSubGroup() >> Either.right(group)
    def additionalGroupResolver = Mock(BaseRequiredIndicesGroupsResolver)
    1 * additionalGroupResolver.resolveIndicesGroups(entry) >> [additionalGroup]
    def foreignDiscountGroupsResolver = Mock(ForeignRequiredGroupsResolver)
    1 * foreignDiscountGroupsResolver.resolveForeignGroups() >> [foreignGroup]

    def resolver = new CalibrationDiscountSubGroupsResolver(baseDiscountGroupResolver, additionalGroupResolver, { CalibrationEntry e -> foreignDiscountGroupsResolver })

    when:
    def result = resolver.resolveSubGroups()

    then:
    result.isRight()
    result.getOrNull() == [group, additionalGroup, foreignGroup]
  }
}
