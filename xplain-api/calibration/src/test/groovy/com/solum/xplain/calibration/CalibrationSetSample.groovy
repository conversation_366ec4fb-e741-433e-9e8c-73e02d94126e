package com.solum.xplain.calibration

import static com.opengamma.strata.basics.currency.Currency.EUR
import static com.opengamma.strata.basics.currency.Currency.GBP
import static com.opengamma.strata.basics.currency.Currency.USD
import static com.opengamma.strata.basics.index.OvernightIndices.EUR_EONIA
import static com.opengamma.strata.basics.index.OvernightIndices.GBP_SONIA
import static com.opengamma.strata.basics.index.OvernightIndices.USD_FED_FUND
import static com.opengamma.strata.basics.index.PriceIndices.EU_AI_CPI
import static com.solum.xplain.core.curvegroup.curve.entity.CurveSample.eur3m
import static com.solum.xplain.core.curvegroup.curve.entity.CurveSample.eur6m
import static com.solum.xplain.core.curvegroup.curve.entity.CurveSample.eurExtInflation
import static com.solum.xplain.core.curvegroup.curve.entity.CurveSample.eurInflation
import static com.solum.xplain.core.curvegroup.curve.entity.CurveSample.eurOis
import static com.solum.xplain.core.curvegroup.curve.entity.CurveSample.frInflation
import static com.solum.xplain.core.curvegroup.curve.entity.CurveSample.gbpInflation
import static com.solum.xplain.core.curvegroup.curve.entity.CurveSample.gbpOis
import static com.solum.xplain.core.curvegroup.curve.entity.CurveSample.usd3m
import static com.solum.xplain.core.curvegroup.curve.entity.CurveSample.usdOis
import static com.solum.xplain.core.curvegroup.curvecredit.entity.CreditCurveSample.self
import static com.solum.xplain.core.curvemarket.node.ValidNodesFilter.EMPTY_FILTER
import static com.solum.xplain.extensions.utils.StandardIdUtils.curveIdStandardId

import com.opengamma.strata.basics.ReferenceData
import com.opengamma.strata.basics.StandardId
import com.opengamma.strata.basics.date.DayCounts
import com.opengamma.strata.basics.index.IborIndices
import com.opengamma.strata.basics.index.PriceIndices
import com.opengamma.strata.collect.array.DoubleMatrix
import com.opengamma.strata.market.ValueType
import com.opengamma.strata.market.curve.ConstantNodalCurve
import com.opengamma.strata.market.curve.CurveGroupName
import com.opengamma.strata.market.curve.CurveName
import com.opengamma.strata.market.curve.CurveParameterSize
import com.opengamma.strata.market.curve.DefaultCurveMetadata
import com.opengamma.strata.market.curve.JacobianCalibrationMatrix
import com.opengamma.strata.market.curve.RatesCurveGroupDefinition
import com.opengamma.strata.pricer.rate.ImmutableRatesProvider
import com.solum.xplain.calibration.credit.CreditRatesDataProviderBuilder
import com.solum.xplain.core.curvegroup.curvecredit.entity.CreditCurve
import com.solum.xplain.core.market.MarketDataSample
import java.time.LocalDate

class CalibrationSetSample {

  public static final StandardId SELF_ID = curveIdStandardId("SELF_EUR_SNRFOR_CR14")
  static final REF_DATA = ReferenceData.standard()
  static final MARKET_DATA = MarketDataSample.ogMarketData()
  static final VALUATION_DATE = LocalDate.of(2023, 1, 1)
  static WARNINGS = { it -> null }

  static RatesCurveGroupDefinition sampleCurveGroupDefinition() {
    RatesCurveGroupDefinition.builder()
      .name(CurveGroupName.of("Curve Group"))
      .addForwardCurve(
      eurInflation().curveDefinition({ 0d }, VALUATION_DATE, EMPTY_FILTER, MARKET_DATA, REF_DATA, WARNINGS, false, false).getOrNull(),
      EU_AI_CPI
      )
      .addCurve(
      eurOis().curveDefinition({ 0d }, VALUATION_DATE, EMPTY_FILTER, MARKET_DATA, REF_DATA, WARNINGS, false, false).getOrNull(),
      EUR,
      EUR_EONIA)
      .addCurve(
      usdOis().curveDefinition({ 0d }, VALUATION_DATE, EMPTY_FILTER, MARKET_DATA, REF_DATA, WARNINGS, false, false).getOrNull(),
      USD,
      USD_FED_FUND)
      .addForwardCurve(
      eur3m().curveDefinition({ 0d }, VALUATION_DATE, EMPTY_FILTER, MARKET_DATA, REF_DATA, WARNINGS, false, false).getOrNull(), IborIndices.EUR_EURIBOR_3M)
      .addForwardCurve(
      usd3m().curveDefinition({ 0d }, VALUATION_DATE, EMPTY_FILTER, MARKET_DATA, REF_DATA, WARNINGS, false, false).getOrNull(), IborIndices.USD_LIBOR_3M)
      .build()
  }

  static sampleCurveGroupDefinitionWithVariousNodeTypes() {
    RatesCurveGroupDefinition.builder()
      .name(CurveGroupName.of("Curve Group"))
      .addForwardCurve(
      eur6m().curveDefinition({ 0d }, VALUATION_DATE, EMPTY_FILTER, MARKET_DATA, REF_DATA, WARNINGS, false, false).getOrNull(),
      IborIndices.EUR_EURIBOR_3M)
      .build()
  }

  static strataCurveGroupInflation() {
    RatesCurveGroupDefinition.builder()
      .name(CurveGroupName.of("Curve Group"))
      .addDiscountCurve(
      gbpOis().curveDefinition({ 0d }, VALUATION_DATE, EMPTY_FILTER, MARKET_DATA, REF_DATA, WARNINGS, false, false).getOrNull(),
      GBP
      )
      .addForwardCurve(
      gbpOis().curveDefinition({ 0d }, VALUATION_DATE, EMPTY_FILTER, MARKET_DATA, REF_DATA, WARNINGS, false, false).getOrNull(),
      GBP_SONIA
      )
      .addForwardCurve(
      gbpInflation().curveDefinition({ 0d }, VALUATION_DATE, EMPTY_FILTER, MARKET_DATA, REF_DATA, WARNINGS, false, false).getOrNull(),
      PriceIndices.GB_RPI
      )
      .build()
  }

  static strataCurveGroupAdditionalInflation() {
    RatesCurveGroupDefinition.builder()
      .name(CurveGroupName.of("Curve Group"))
      .addDiscountCurve(
      gbpOis().curveDefinition({ 0d }, VALUATION_DATE, EMPTY_FILTER, MARKET_DATA, REF_DATA, WARNINGS, false, false).getOrNull(),
      GBP
      )
      .addCurve(
      eurOis().curveDefinition({ 0d }, VALUATION_DATE, EMPTY_FILTER, MARKET_DATA, REF_DATA, WARNINGS, false, false).getOrNull(),
      EUR,
      EUR_EONIA
      )
      .addForwardCurve(
      gbpOis().curveDefinition({ 0d }, VALUATION_DATE, EMPTY_FILTER, MARKET_DATA, REF_DATA, WARNINGS, false, false).getOrNull(),
      GBP_SONIA
      )
      .addForwardCurve(
      gbpInflation().curveDefinition({ 0d }, VALUATION_DATE, EMPTY_FILTER, MARKET_DATA, REF_DATA, WARNINGS, false, false).getOrNull(),
      PriceIndices.GB_RPI
      )
      .addForwardCurve(
      eurInflation().curveDefinition({ 0d }, VALUATION_DATE, EMPTY_FILTER, MARKET_DATA, REF_DATA, WARNINGS, false, false).getOrNull(),
      PriceIndices.EU_AI_CPI
      )
      .addForwardCurve(
      eurExtInflation().curveDefinition({ 0d }, VALUATION_DATE, EMPTY_FILTER, MARKET_DATA, REF_DATA, WARNINGS, false, false).getOrNull(),
      PriceIndices.EU_EXT_CPI
      )
      .addForwardCurve(
      frInflation().curveDefinition({ 0d }, VALUATION_DATE, EMPTY_FILTER, MARKET_DATA, REF_DATA, WARNINGS, false, false).getOrNull(),
      PriceIndices.FR_EXT_CPI
      )
      .build()
  }

  static ImmutableRatesProvider ratesProvider(LocalDate valuationDate) {
    ImmutableRatesProvider
      .builder(valuationDate)
      .discountCurve(EUR, ConstantNodalCurve.of(
      DefaultCurveMetadata.builder().curveName("EUR discount")
      .xValueType(ValueType.YEAR_FRACTION)
      .yValueType(ValueType.ZERO_RATE)
      .dayCount(DayCounts.ACT_365F)
      .jacobian(JacobianCalibrationMatrix.of([CurveParameterSize.of(CurveName.of("EUR discount")
        , 1)], DoubleMatrix.of(1, 1, 1d)))
      .build(), 0, 0.01))
      .build()
  }

  static CreditRatesDataProviderBuilder creditRatesProviderBuilder(LocalDate valuationDate) {
    creditRatesProviderBuilder(valuationDate, self(valuationDate))
  }

  static CreditRatesDataProviderBuilder creditRatesProviderBuilder(LocalDate valuationDate, CreditCurve creditCurve) {
    new CreditRatesDataProviderBuilder(valuationDate)
      .withCreditDiscountFactors(ratesProvider(valuationDate))
      .withRecoveryRate(creditCurve)
  }
}
