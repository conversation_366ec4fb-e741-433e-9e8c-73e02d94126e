package com.solum.xplain.calibration.volatility


import static com.opengamma.strata.basics.index.IborIndices.EUR_EURIBOR_3M

import com.opengamma.strata.basics.index.IborIndices
import com.opengamma.strata.basics.index.RateIndex
import com.opengamma.strata.data.ImmutableMarketData
import com.solum.xplain.calibration.CalibrationSetSample
import com.solum.xplain.calibration.capfloor.CapletFloorletVolatilityCalibrationResults
import com.solum.xplain.calibration.rates.RatesCalibrationResult
import com.solum.xplain.core.curvegroup.volatility.entity.VolatilitySurfaceBuilder
import com.solum.xplain.core.curvegroup.volatility.value.caplet.CapletVolatilityNodeValueView
import com.solum.xplain.core.curvemarket.node.NodeInstrumentWrapper
import com.solum.xplain.core.curvemarket.node.ValidNodesFilter
import com.solum.xplain.core.error.Error
import com.solum.xplain.core.error.ErrorItem
import java.time.LocalDate
import spock.lang.Specification

class VolatilitiesCalibrationTest extends Specification {

  static final String CURVE_GROUP_ID = "curveGroupId"

  def "should filter empty volatilities and return empty calibration result"() {
    setup:
    def marketData = ImmutableMarketData.builder(LocalDate.now()).build()
    def ratesProvider = CalibrationSetSample.ratesProvider(LocalDate.now())
    def rates = new RatesCalibrationResult(ratesProvider, [])
    List<ErrorItem> errors = []
    when:
    def calculator = VolatilitiesCalibration.builder()
      .curveGroupId(CURVE_GROUP_ID)
      .marketData(marketData)
      .validNodesFilter(ValidNodesFilter.EMPTY_FILTER)
      .volatilitySurfaces([new VolatilitySurfaceBuilder().build()])
      .logConsumer({ v ->
        errors.addAll(v)
        v
      })
      .build()

    def result = calculator.calibrateCaplets(rates, [] as Set<RateIndex>)

    then:
    result == CapletFloorletVolatilityCalibrationResults.of([])
  }

  def "should return empty calibration result when no valid nodes and log error"() {
    setup:
    def marketData = ImmutableMarketData.builder(LocalDate.now()).build()
    def ratesProvider = CalibrationSetSample.ratesProvider(LocalDate.now())
    def rates = new RatesCalibrationResult(ratesProvider, [])

    def filter = Mock(ValidNodesFilter)
    filter.filterNodes(_ as List<NodeInstrumentWrapper<CapletVolatilityNodeValueView>>) >> []
    List<ErrorItem> errors = []

    when:
    def calculator = VolatilitiesCalibration.builder()
      .curveGroupId(CURVE_GROUP_ID)
      .marketData(marketData)
      .validNodesFilter(filter)
      .volatilitySurfaces([new VolatilitySurfaceBuilder().surfaceWithNodes()])
      .logConsumer({ v ->
        errors.addAll(v)
        v
      })
      .build()
    def result = calculator.calibrateCaplets(rates, [EUR_EURIBOR_3M] as Set<RateIndex>)

    then:
    result == CapletFloorletVolatilityCalibrationResults.of([])
    errors.size() == 1
    errors[0].description == "Error creating caplet value matrix for surface EUR 3M Vols: Surface must have at least 2 valid caplet nodes"
    errors[0].reason == Error.CALIBRATION_ERROR
  }

  def "should return empty results when no matching capfloor indices"() {
    setup:
    def marketData = ImmutableMarketData.builder(LocalDate.now()).build()
    def ratesProvider = CalibrationSetSample.ratesProvider(LocalDate.now())
    def rates = new RatesCalibrationResult(ratesProvider, [])

    def filter = Mock(ValidNodesFilter)
    filter.filterNodes(_ as List<NodeInstrumentWrapper<CapletVolatilityNodeValueView>>) >> []
    List<ErrorItem> errors = []

    when:
    def calculator = VolatilitiesCalibration.builder()
      .curveGroupId(CURVE_GROUP_ID)
      .marketData(marketData)
      .validNodesFilter(filter)
      .volatilitySurfaces([new VolatilitySurfaceBuilder().surfaceWithNodes()])
      .logConsumer({ v ->
        errors.addAll(v)
        v
      })
      .build()
    def result = calculator.calibrateCaplets(rates, [IborIndices.USD_LIBOR_1M] as Set<RateIndex>)

    then:
    result == CapletFloorletVolatilityCalibrationResults.of([])
  }
}
