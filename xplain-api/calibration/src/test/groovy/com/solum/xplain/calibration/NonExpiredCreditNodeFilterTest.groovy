package com.solum.xplain.calibration

import com.opengamma.strata.basics.ReferenceData
import com.opengamma.strata.basics.currency.Currency
import com.solum.xplain.core.curvegroup.curvecredit.classifiers.CreditCurveType
import com.solum.xplain.core.curvegroup.curvecredit.entity.CreditCurve
import com.solum.xplain.core.curvegroup.curvecredit.entity.CreditCurveCdsNode
import com.solum.xplain.core.curvemarket.node.NodeInstrumentWrapper
import com.solum.xplain.core.curvemarket.node.NonExpiredCreditNodeFilter
import com.solum.xplain.core.error.ErrorItem
import java.time.LocalDate
import spock.lang.Specification

class NonExpiredCreditNodeFilterTest extends Specification {

  def "should return all nodes with no errors"() {
    setup:
    def errorList = [] as List<ErrorItem>
    def nodes = [
      new CreditCurveCdsNode(
      tenor: "0M",
      ),
      new CreditCurveCdsNode(
      tenor: "3M",
      ),
    ]
    def creditCurve = new CreditCurve(
      cdsNodes: nodes,
      quoteConvention: "PAR_SPREAD",
      curveType: CreditCurveType.CDS,
      currency: Currency.GBP,
      name: "9DDGBAAQ2",
      )
    def filter = filter(errorList, valuationDate, creditCurve)

    def wrappedNodes = nodes.stream().map { node ->
      NodeInstrumentWrapper.of(node, node.instrument(creditCurve))
    }.toList()

    when:
    def result = filter.filterNodes(wrappedNodes)
    then:
    result.size() == 2
    result[0] == nodes[0]
    result[1] == nodes[1]
    errorList.isEmpty()

    where:
    valuationDate << [LocalDate.parse("2022-09-21"), LocalDate.parse("2022-12-19"), LocalDate.parse("2022-03-21")]
  }

  def "should return error for expired zero month node"() {
    setup:
    def errorList = [] as List<ErrorItem>
    def nodes = [
      new CreditCurveCdsNode(
      tenor: "0M",
      ),
      new CreditCurveCdsNode(
      tenor: "3M",
      ),
    ]
    def creditCurve = new CreditCurve(
      cdsNodes: nodes,
      quoteConvention: "PAR_SPREAD",
      curveType: CreditCurveType.CDS,
      currency: Currency.GBP,
      name: "9DDGBAAQ2",
      )
    def filter = filter(errorList, valuationDate, creditCurve)

    def wrappedNodes = nodes.stream().map { node ->
      NodeInstrumentWrapper.of(node, node.instrument(creditCurve))
    }.toList()

    when:
    def result = filter.filterNodes(wrappedNodes)
    then:
    result.size() == 1
    errorList.size() == 1
    errorList[0].description == "Market tenor node with key 0M_9DDGBAAQ2_SPREAD was dropped since it has expired"

    where:
    valuationDate << [LocalDate.parse("2022-12-20"), LocalDate.parse("2022-12-21"), LocalDate.parse("2022-06-21")]
  }

  def filter(List<ErrorItem> errorLog, LocalDate valuationDate, CreditCurve creditCurve) {
    return NonExpiredCreditNodeFilter.ofNonExpiredZeroMonthCdsNodes(
      valuationDate,
      ReferenceData.standard(),
      creditCurve, { v ->
        errorLog.addAll(v)
      }
      )
  }
}
