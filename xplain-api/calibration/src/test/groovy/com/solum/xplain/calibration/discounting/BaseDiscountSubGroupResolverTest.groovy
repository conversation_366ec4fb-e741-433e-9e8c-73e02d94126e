package com.solum.xplain.calibration.discounting

import static com.opengamma.strata.product.fx.type.FxSwapConventions.EUR_USD
import static com.opengamma.strata.product.swap.type.FixedIborSwapConventions.EUR_FIXED_1Y_EURIBOR_3M
import static com.opengamma.strata.product.swap.type.FixedIborSwapConventions.EUR_FIXED_1Y_EURIBOR_6M
import static com.opengamma.strata.product.swap.type.FixedIborSwapConventions.GBP_FIXED_3M_LIBOR_3M
import static com.opengamma.strata.product.swap.type.FixedIborSwapConventions.JPY_FIXED_6M_LIBOR_6M
import static com.opengamma.strata.product.swap.type.FixedIborSwapConventions.USD_FIXED_6M_LIBOR_3M
import static com.opengamma.strata.product.swap.type.FixedOvernightSwapConventions.GBP_FIXED_TERM_SONIA_OIS
import static com.opengamma.strata.product.swap.type.FixedOvernightSwapConventions.JPY_FIXED_TERM_TONAR_OIS
import static com.opengamma.strata.product.swap.type.FixedOvernightSwapConventions.USD_FIXED_TERM_FED_FUND_OIS
import static com.opengamma.strata.product.swap.type.XCcyIborIborSwapConventions.GBP_LIBOR_3M_USD_LIBOR_3M
import static com.solum.xplain.core.classifiers.CurveNodeTypes.FIXED_IBOR_SWAP_NODE
import static com.solum.xplain.core.classifiers.CurveNodeTypes.FIXED_OVERNIGHT_SWAP_NODE
import static com.solum.xplain.core.classifiers.CurveNodeTypes.FX_SWAP_NODE
import static com.solum.xplain.core.classifiers.CurveNodeTypes.OVERNIGHT_IBOR_BASIS_SWAP_NODE
import static com.solum.xplain.core.classifiers.CurveNodeTypes.XCCY_IBOR_IBOR_SWAP_NODE
import static com.solum.xplain.core.curvegroup.curve.entity.CurveNodeBuilder.curveNode
import static com.solum.xplain.core.curvegroup.curve.entity.CurveSample.eurInflation
import static com.solum.xplain.core.curvegroup.curve.entity.CurveSample.eurOis
import static com.solum.xplain.core.error.Error.CALIBRATION_ERROR
import static io.atlassian.fugue.Either.left

import com.opengamma.strata.basics.currency.Currency
import com.opengamma.strata.basics.index.IborIndices
import com.solum.xplain.calibration.rates.CalibrationEntry
import com.solum.xplain.calibration.value.CurveShifts
import com.solum.xplain.core.classifiers.discounting.IndexBasedDiscountCurrencies
import com.solum.xplain.core.curvegroup.conventions.ClearingHouse
import com.solum.xplain.core.curvegroup.curve.classifier.CurveType
import com.solum.xplain.core.curvegroup.curve.entity.Curve
import com.solum.xplain.core.curvegroup.curve.entity.CurveBuilder
import java.time.LocalDate
import spock.lang.Specification
import spock.lang.Unroll

class BaseDiscountSubGroupResolverTest extends Specification {
  def static CURVE_DATE = LocalDate.parse("2017-01-01")

  @Unroll
  def "should find base calibration set for currency #currency"() {
    setup:
    def calibrationEntries = toCalibrationEntries(createGroupCurves())
    def resolver = BaseDiscountSubGroupResolver.baseDiscountGroupResolver(CURVE_DATE,
    IndexBasedDiscountCurrencies.getOf(currency),
    isOffshore,
    ClearingHouse.NONE,
    null,
    calibrationEntries)

    when:
    def result = resolver.resolveBaseSubGroup()

    then:
    result.isRight()
    result.getOrNull().discountCurve().curveName == discountCurve
    result.getOrNull().calibrationEntries.collect { it.curveName } == calibrationSet
    where:
    currency     | isOffshore | discountCurve       | calibrationSet
    Currency.EUR | false      | "EUR EONIA"         | ["EUR EONIA", "EUR 3M"]
    Currency.USD | false      | "USD FEDFUNDS"      | ["USD FEDFUNDS"]
    Currency.THB | false      | "THB THOR"          | ["THB THOR"]
    Currency.THB | true       | "THB THOR Offshore" | ["THB THOR Offshore"]
  }

  def "should return error when more than one discount curve for currency"() {
    setup:
    def resolver = BaseDiscountSubGroupResolver.baseDiscountGroupResolver(
    CURVE_DATE,
    IndexBasedDiscountCurrencies.getOf(Currency.EUR),
    false,
    ClearingHouse.NONE,
    null,
    toCalibrationEntries([eurOis(), eurOis()]))

    when:
    def result = resolver.resolveBaseSubGroup()

    then:
    result.isLeft()
    result == left(CALIBRATION_ERROR.entity("Multiple discount curves found for currency EUR"))
  }


  def "should find base calibration set with libor discounting"() {
    setup:
    def expectedSet = ["EUR EONIA", "EUR 3M"]

    def calibrationEntries = toCalibrationEntries(createGroupCurves())
    def resolver = BaseDiscountSubGroupResolver.baseDiscountGroupResolver(
    CURVE_DATE,
    IndexBasedDiscountCurrencies.getOf(Currency.EUR),
    null,
    ClearingHouse.NONE,
    IborIndices.EUR_EURIBOR_3M,
    calibrationEntries)

    when:
    def result = resolver.resolveBaseSubGroup()

    then:
    result.isRight()
    result.getOrNull().discountCurve().curveName == "EUR 3M"
    result.getOrNull().calibrationEntries.stream()
    .map({ c -> c.curveName })
    .forEach({ c ->
      assert expectedSet.contains(c)
    })
  }

  def "should not find base discount curve"() {
    setup:
    List<Curve> curves = [
      new CurveBuilder()
      .entityId("EUR3M")
      .name("EUR 3M")
      .curveType(CurveType.IR_INDEX)
      .nodes([curveNode(FIXED_IBOR_SWAP_NODE, EUR_FIXED_1Y_EURIBOR_3M.name)])
      .build(),
      new CurveBuilder()
      .entityId("USDOIS")
      .name("USD FEDFUNDS")
      .curveType(CurveType.IR_INDEX)
      .nodes([curveNode(FIXED_OVERNIGHT_SWAP_NODE, USD_FIXED_TERM_FED_FUND_OIS.name)])
      .build()
    ]
    def calibrationEntries = toCalibrationEntries(curves)
    def resolver = BaseDiscountSubGroupResolver.baseDiscountGroupResolver(
    CURVE_DATE,
    IndexBasedDiscountCurrencies.getOf(Currency.EUR),
    false,
    ClearingHouse.NONE,
    null,
    calibrationEntries)

    when:
    def result = resolver.resolveBaseSubGroup()

    then:
    result.isLeft()
  }

  static createGroupCurves() {
    [
      new CurveBuilder()
      .entityId("EUR-EONIA")
      .name("EUR EONIA")
      .curveType(CurveType.INDEX_BASIS)
      .nodes([curveNode(OVERNIGHT_IBOR_BASIS_SWAP_NODE, "EUR-EONIA-OIS-1Y-EURIBOR-3M")])
      .build(),
      new CurveBuilder()
      .entityId("EUR3M")
      .name("EUR 3M")
      .curveType(CurveType.IR_INDEX)
      .nodes([curveNode(FIXED_IBOR_SWAP_NODE, EUR_FIXED_1Y_EURIBOR_3M.name)])
      .build(),
      new CurveBuilder()
      .entityId("EUR6M")
      .name("EUR 6M")
      .curveType(CurveType.IR_INDEX)
      .nodes([curveNode(FIXED_IBOR_SWAP_NODE, EUR_FIXED_1Y_EURIBOR_6M.name)])
      .build(),
      new CurveBuilder()
      .entityId("USDOIS")
      .name("USD FEDFUNDS")
      .curveType(CurveType.IR_INDEX)
      .nodes([curveNode(FIXED_OVERNIGHT_SWAP_NODE, USD_FIXED_TERM_FED_FUND_OIS.name)])
      .build(),
      new CurveBuilder()
      .entityId("USD3M")
      .name("USD 3M")
      .curveType(CurveType.IR_INDEX)
      .nodes([curveNode(FIXED_IBOR_SWAP_NODE, USD_FIXED_6M_LIBOR_3M.name)])
      .build(),
      new CurveBuilder()
      .entityId("EURUSDBASIS")
      .name("EUR 3M vs USD 3M")
      .curveType(CurveType.XCCY)
      .nodes([curveNode(FX_SWAP_NODE, EUR_USD.name)])
      .build(),
      new CurveBuilder()
      .entityId("GBPOIS")
      .name("GBP SONIA")
      .curveType(CurveType.IR_INDEX)
      .nodes([curveNode(FIXED_OVERNIGHT_SWAP_NODE, GBP_FIXED_TERM_SONIA_OIS.name)])
      .build(),
      new CurveBuilder()
      .entityId("GBP3M")
      .name("GBP 3M")
      .curveType(CurveType.IR_INDEX)
      .nodes([curveNode(FIXED_IBOR_SWAP_NODE, GBP_FIXED_3M_LIBOR_3M.name)])
      .build(),
      new CurveBuilder()
      .entityId("GBPUSDBASIS")
      .name("GBP 3M vs USD 3M")
      .curveType(CurveType.XCCY)
      .nodes([curveNode(XCCY_IBOR_IBOR_SWAP_NODE, GBP_LIBOR_3M_USD_LIBOR_3M.name)])
      .build(),
      /* JPY */
      new CurveBuilder()
      .entityId("JPYOIS")
      .name("JPY TONAR")
      .curveType(CurveType.INDEX_BASIS)
      .nodes([curveNode(OVERNIGHT_IBOR_BASIS_SWAP_NODE, JPY_FIXED_TERM_TONAR_OIS.name)])
      .build(),
      new CurveBuilder()
      .entityId("JPY6M")
      .name("JPY 6M")
      .curveType(CurveType.IR_INDEX)
      .nodes([curveNode(FIXED_IBOR_SWAP_NODE, JPY_FIXED_6M_LIBOR_6M.name)])
      .build(),
      new CurveBuilder()
      .entityId("USD/JPY")
      .name("USD/JPY")
      .curveType(CurveType.XCCY)
      .nodes([curveNode(FX_SWAP_NODE, "USD/JPY")])
      .build(),
      /* CAD */
      new CurveBuilder()
      .entityId("CADOIS")
      .name("CAD CORRA")
      .curveType(CurveType.INDEX_BASIS)
      .nodes([curveNode(OVERNIGHT_IBOR_BASIS_SWAP_NODE, "CAD-FIXED-1Y-CORRA-OIS")])
      .build(),
      new CurveBuilder()
      .entityId("CAD3M")
      .name("CAD 3M")
      .curveType(CurveType.IR_INDEX)
      .nodes([curveNode(FIXED_IBOR_SWAP_NODE, "CAD-FIXED-6M-CDOR-3M")])
      .build(),
      new CurveBuilder()
      .entityId("USDCADBASIS")
      .name("CAD 3M vs USD 3M")
      .curveType(CurveType.XCCY)
      .nodes([curveNode(XCCY_IBOR_IBOR_SWAP_NODE, "CAD-CDOR-3M-USD-LIBOR-3M")])
      .build(),
      eurInflation(),
      /* THB */
      new CurveBuilder()
      .entityId("THBTHOR")
      .name("THB THOR")
      .curveType(CurveType.IR_INDEX)
      .nodes([curveNode(FIXED_OVERNIGHT_SWAP_NODE, "THB-FIXED-TERM-THOR-OIS")])
      .build(),
      new CurveBuilder()
      .entityId("THBTHOROF")
      .name("THB THOR Offshore")
      .curveType(CurveType.IR_INDEX)
      .nodes([curveNode(FIXED_OVERNIGHT_SWAP_NODE, "THB-FIXED-TERM-THOR-OIS-OFFSHORE")])
      .build(),
    ]
  }

  static List<CalibrationEntry> toCalibrationEntries(List<Curve> curves) {
    curves.stream()
    .map(c -> {
      return new CalibrationEntry(c, CurveShifts.empty(), null)
    })
    .toList()
  }
}
