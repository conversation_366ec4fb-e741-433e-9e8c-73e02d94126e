package com.solum.xplain.calibration

import com.solum.xplain.core.common.versions.BitemporalDate
import com.solum.xplain.core.curvegroup.curve.entity.Curve
import com.solum.xplain.core.helper.XplainDefaultIntegrationSpecification
import com.solum.xplain.core.market.repository.MarketDataKeyRepository
import groovy.util.logging.Slf4j
import org.spockframework.runtime.model.parallel.ExecutionMode
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.data.mongodb.core.MongoTemplate
import org.springframework.test.context.ActiveProfiles
import spock.lang.Execution

@SpringBootTest
@ActiveProfiles("test")
@Slf4j
@Execution(ExecutionMode.SAME_THREAD)
class XplainDefaultDataIntegrationTest extends XplainDefaultIntegrationSpecification {

  @Autowired
  MongoTemplate mongoOperations

  @Autowired
  MarketDataKeyRepository marketDataKeyRepository

  def "should restore xplain default data and count market data keys"() {
    when: "counting market data keys in the restored database"
    def stateDate = BitemporalDate.newOf(java.time.LocalDate.now())
    def mdkViews = marketDataKeyRepository.marketDataKeyViews(stateDate)
    def mdkCount = mdkViews.size()

    log.info("Found {} market data keys in the restored database", mdkCount)

    then: "market data keys should be present and numerous"
    mdkCount > 0
    mdkCount > 1000  // Expect substantial number based on "a lot" requirement

    and: "log the count for verification"
    log.info("Market data keys count verification: {} > 0 and {} > 1000", mdkCount, mdkCount)
  }

  def "should have curves in the restored database"() {
    when: "querying for curves"
    def curves = mongoOperations.findAll(Curve.class)
    def curveCount = curves.size()

    then: "curves should be present"
    assert curveCount > 10
  }
}
