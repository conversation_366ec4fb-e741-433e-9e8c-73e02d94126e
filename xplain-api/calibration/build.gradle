plugins {
  id "java-test-fixtures"
}

sourceSets {
  integrationTest {
    groovy.srcDirs = ['src/integrationTest/groovy']
    resources.srcDirs = ['src/integrationTest/resources']
    compileClasspath += sourceSets.main.output + sourceSets.test.output
    runtimeClasspath += output + compileClasspath
  }
}

dependencies {
  implementation project(":xplain-api:core")
  implementation project(":shared:strata-extension")
  implementation project(":shared:spring-async")
  implementation project(":shared:versions")

  implementation "jakarta.inject:jakarta.inject-api:2.0.1"
  implementation 'org.springframework.boot:spring-boot-starter-security'
  implementation 'org.springframework.boot:spring-boot-starter-web'
  implementation 'org.springframework.boot:spring-boot-starter-validation'
  implementation 'org.springframework.boot:spring-boot-starter-data-mongodb'
  implementation "one.util:streamex:${streamExVersion}"

  implementation "com.opengamma.strata:strata-measure:${strataVersion}"

  testImplementation(testFixtures(project(":xplain-api:core")))

  testFixturesImplementation(project(":xplain-api:core"))
  testFixturesImplementation(project(":shared:strata-extension"))
  testFixturesImplementation(project(":shared:versions"))
  testFixturesImplementation "org.apache.groovy:groovy:${groovyJsonVersion}"
  testFixturesImplementation "org.spockframework:spock-core:${spockVersion}"
  testFixturesImplementation('org.springframework.boot:spring-boot-starter-test')
  testFixturesImplementation 'org.springframework.boot:spring-boot-starter-data-mongodb'
  testFixturesImplementation "com.opengamma.strata:strata-measure:${strataVersion}"

  integrationTestImplementation(testFixtures(project(":xplain-api:core")))
  integrationTestImplementation project(":shared:strata-extension")
  integrationTestImplementation project(":shared:versions")
  integrationTestImplementation "org.apache.groovy:groovy:${groovyJsonVersion}"
  integrationTestImplementation "org.spockframework:spock-core:${spockVersion}"
  integrationTestImplementation "org.spockframework:spock-spring:${spockVersion}"
  integrationTestImplementation 'org.springframework.boot:spring-boot-starter-test'
  integrationTestImplementation 'org.springframework.boot:spring-boot-starter-data-mongodb'
  integrationTestImplementation "com.opengamma.strata:strata-measure:${strataVersion}"
}

// Configure integration test to include xplain-dump folder in classpath
tasks.named('processIntegrationTestResources') {
  from("${rootProject.projectDir}/dumps/xplain-dump") {
    into 'xplain-dump'
  }
}

