package com.solum.xplain.generic.type.helpers;

import com.solum.xplain.core.config.JacksonConfig;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;
import org.springframework.boot.autoconfigure.web.servlet.HttpEncodingAutoConfiguration;
import org.springframework.context.annotation.Import;
import org.springframework.data.web.config.EnableSpringDataWebSupport;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.context.ActiveProfiles;

@WithMockUser
@ActiveProfiles("mockMvc")
@EnableSpringDataWebSupport
@Target({ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Import({HttpEncodingAutoConfiguration.class, TestMvcConfig.class, JacksonConfig.class})
public @interface MockMvcConfiguration {}
