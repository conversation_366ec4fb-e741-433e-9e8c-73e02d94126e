package com.solum.xplain.generic.type.csv

import static com.solum.xplain.core.portfolio.PortfolioCSVFields.GENERIC_ADDITIONAL_INFO
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.GENERIC_ASSET_TYPE
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.GENERIC_NOTIONAL
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.GENERIC_SUB_ASSET_TYPE
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.GENERIC_UNDERLYING
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.LEG_1
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.LEG_2
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.LEG_CURRENCY
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.LEG_NOTIONAL
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.PAY_RECEIVE
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.TRADE_ACCRUAL_SCHEDULE_END_DATE
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.TRADE_ACCRUAL_SCHEDULE_START_DATE
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.TRADE_CREDIT_SECTOR
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.TRADE_CURRENCY
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.TRADE_REF_SEC_FX_RATE
import static com.solum.xplain.generic.type.product.GenericProductType.CUSTOM_COMMODITY_1
import static com.solum.xplain.generic.type.product.GenericProductType.CUSTOM_COMMODITY_2
import static com.solum.xplain.generic.type.product.GenericProductType.CUSTOM_COMMODITY_3
import static com.solum.xplain.generic.type.product.GenericProductType.CUSTOM_COMMODITY_4
import static com.solum.xplain.generic.type.product.GenericProductType.CUSTOM_COMMODITY_5
import static com.solum.xplain.generic.type.product.GenericProductType.CUSTOM_CREDIT_1
import static com.solum.xplain.generic.type.product.GenericProductType.CUSTOM_CREDIT_2
import static com.solum.xplain.generic.type.product.GenericProductType.CUSTOM_CREDIT_3
import static com.solum.xplain.generic.type.product.GenericProductType.CUSTOM_CREDIT_4
import static com.solum.xplain.generic.type.product.GenericProductType.CUSTOM_CREDIT_5
import static com.solum.xplain.generic.type.product.GenericProductType.CUSTOM_EQUITY_1
import static com.solum.xplain.generic.type.product.GenericProductType.CUSTOM_EQUITY_2
import static com.solum.xplain.generic.type.product.GenericProductType.CUSTOM_EQUITY_3
import static com.solum.xplain.generic.type.product.GenericProductType.CUSTOM_EQUITY_4
import static com.solum.xplain.generic.type.product.GenericProductType.CUSTOM_EQUITY_5
import static com.solum.xplain.generic.type.product.GenericProductType.CUSTOM_FX_1
import static com.solum.xplain.generic.type.product.GenericProductType.CUSTOM_FX_2
import static com.solum.xplain.generic.type.product.GenericProductType.CUSTOM_FX_3
import static com.solum.xplain.generic.type.product.GenericProductType.CUSTOM_FX_4
import static com.solum.xplain.generic.type.product.GenericProductType.CUSTOM_FX_5
import static com.solum.xplain.generic.type.product.GenericProductType.CUSTOM_OTHER_1
import static com.solum.xplain.generic.type.product.GenericProductType.CUSTOM_OTHER_2
import static com.solum.xplain.generic.type.product.GenericProductType.CUSTOM_OTHER_3
import static com.solum.xplain.generic.type.product.GenericProductType.CUSTOM_OTHER_4
import static com.solum.xplain.generic.type.product.GenericProductType.CUSTOM_OTHER_5

import com.solum.xplain.core.common.csv.CsvOutputFile
import com.solum.xplain.core.common.csv.CsvRow
import com.solum.xplain.core.utils.PathUtils
import com.solum.xplain.generic.type.helpers.GenericPortfolioItemBuilder
import com.solum.xplain.generic.type.product.GenericProductType
import org.apache.commons.collections4.IterableUtils
import spock.lang.Specification

class GenericTradeCsvMapperTest extends Specification {
  def HEADER = [
    TRADE_ACCRUAL_SCHEDULE_START_DATE,
    TRADE_ACCRUAL_SCHEDULE_END_DATE,
    GENERIC_ASSET_TYPE,
    GENERIC_SUB_ASSET_TYPE,
    GENERIC_ADDITIONAL_INFO,
    TRADE_CURRENCY,
    GENERIC_NOTIONAL,
    GENERIC_UNDERLYING,
    TRADE_CREDIT_SECTOR,
    PathUtils.joinPaths(LEG_1, PAY_RECEIVE),
    PathUtils.joinPaths(LEG_1, LEG_NOTIONAL),
    PathUtils.joinPaths(LEG_1, LEG_CURRENCY),
    PathUtils.joinPaths(LEG_2, PAY_RECEIVE),
    PathUtils.joinPaths(LEG_2, LEG_NOTIONAL),
    PathUtils.joinPaths(LEG_2, LEG_CURRENCY),
    TRADE_REF_SEC_FX_RATE
  ]

  def MAPPER = new GenericTradeCsvMapper()

  def "should return product types"() {
    expect:
    MAPPER.productTypes() == Arrays.asList(CUSTOM_FX_1,
      CUSTOM_FX_2,
      CUSTOM_FX_3,
      CUSTOM_FX_4,
      CUSTOM_FX_5,
      CUSTOM_EQUITY_1,
      CUSTOM_EQUITY_2,
      CUSTOM_EQUITY_3,
      CUSTOM_EQUITY_4,
      CUSTOM_EQUITY_5,
      CUSTOM_COMMODITY_1,
      CUSTOM_COMMODITY_2,
      CUSTOM_COMMODITY_3,
      CUSTOM_COMMODITY_4,
      CUSTOM_COMMODITY_5,
      CUSTOM_CREDIT_1,
      CUSTOM_CREDIT_2,
      CUSTOM_CREDIT_3,
      CUSTOM_CREDIT_4,
      CUSTOM_CREDIT_5,
      CUSTOM_OTHER_1,
      CUSTOM_OTHER_2,
      CUSTOM_OTHER_3,
      CUSTOM_OTHER_4,
      CUSTOM_OTHER_5
      )
  }

  def "should correctly map Credit trade"() {
    setup:
    def trade = GenericPortfolioItemBuilder.singleCreditCcyGenericTrade(GenericProductType.CUSTOM_CREDIT_2)

    when:
    def columns = MAPPER.toCsvFields(trade.tradeDetails)
    def csv = new CsvOutputFile(HEADER, [new CsvRow(IterableUtils.toList(columns))])
    def result = csv.write()

    then:
    result == """Start Date,End Date,Asset Type,Sub-Asset Type,Additional Information,Trade Currency,Custom Product Notional,Custom Product Underlying,Credit Sector,Leg1.PayReceive,Leg1.Notional,Leg1.Ccy,Leg2.PayReceive,Leg2.Notional,Leg2.Ccy,Fx Rate
1970-01-01,2031-06-19,SINGLE_CCY,SUB_SINGLE,ADDITIONAL,EUR,10,UNDERLYING,CONSUMER_SERVICES,,,,,,,
"""
  }

  def "should correctly map non credit trade"() {
    setup:
    def trade = GenericPortfolioItemBuilder.singleCcyGenericTrade(GenericProductType.CUSTOM_CREDIT_2)

    when:
    def columns = MAPPER.toCsvFields(trade.tradeDetails)
    def csv = new CsvOutputFile(HEADER, [new CsvRow(IterableUtils.toList(columns))])
    def result = csv.write()

    then:
    result == """Start Date,End Date,Asset Type,Sub-Asset Type,Additional Information,Trade Currency,Custom Product Notional,Custom Product Underlying,Credit Sector,Leg1.PayReceive,Leg1.Notional,Leg1.Ccy,Leg2.PayReceive,Leg2.Notional,Leg2.Ccy,Fx Rate
1970-01-01,2031-06-19,SINGLE_CCY,SUB_SINGLE,ADDITIONAL,EUR,10,UNDERLYING,,,,,,,,
"""
  }

  def "should correctly map FX trade"() {
    setup:
    def trade = GenericPortfolioItemBuilder.fxGenericTrade(GenericProductType.CUSTOM_FX_1)

    when:
    def columns = MAPPER.toCsvFields(trade.tradeDetails)
    def csv = new CsvOutputFile(HEADER, [new CsvRow(IterableUtils.toList(columns))])
    def result = csv.write()

    then:
    result == """Start Date,End Date,Asset Type,Sub-Asset Type,Additional Information,Trade Currency,Custom Product Notional,Custom Product Underlying,Credit Sector,Leg1.PayReceive,Leg1.Notional,Leg1.Ccy,Leg2.PayReceive,Leg2.Notional,Leg2.Ccy,Fx Rate
1970-01-01,2031-06-19,SINGLE_CCY,SUB_SINGLE,ADDITIONAL,EUR,,UNDERLYING,,Receive,10,EUR,Pay,11,USD,
"""
  }
}
