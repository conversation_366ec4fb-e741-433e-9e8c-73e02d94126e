package com.solum.xplain.generic.type

import com.solum.xplain.secmaster.SecMasterConfig
import org.springframework.boot.SpringBootConfiguration
import org.springframework.boot.autoconfigure.EnableAutoConfiguration
import org.springframework.boot.autoconfigure.ImportAutoConfiguration
import org.springframework.context.annotation.Import

@EnableAutoConfiguration
@SpringBootConfiguration
@ImportAutoConfiguration
@Import(SecMasterConfig)
class TestGenericProductConfig extends GenericProductConfig {
}
