package com.solum.xplain.generic.type.secmaster

import static com.solum.xplain.generic.type.product.GenericProductType.CUSTOM_FX_1
import static com.solum.xplain.generic.type.product.GenericProductType.CUSTOM_FX_2
import static com.solum.xplain.generic.type.product.GenericProductType.CUSTOM_FX_3
import static com.solum.xplain.generic.type.product.GenericProductType.CUSTOM_FX_4
import static com.solum.xplain.generic.type.product.GenericProductType.CUSTOM_FX_5

import com.solum.xplain.core.portfolio.trade.AllocationTradeDetails
import com.solum.xplain.core.portfolio.trade.GenericTradeDetails
import com.solum.xplain.core.portfolio.trade.TradeDetails
import com.solum.xplain.secmaster.trademerge.product.FxProductDetailsMerger
import spock.lang.Specification

class GenericFxProductMergerTest extends Specification {
  FxProductDetailsMerger fxMerger = Mock(FxProductDetailsMerger)
  GenericFxProductMerger merger = new GenericFxProductMerger(fxMerger)

  def "should return product types"() {
    expect:
    merger.productTypes() == [CUSTOM_FX_1, CUSTOM_FX_2, CUSTOM_FX_3, CUSTOM_FX_4, CUSTOM_FX_5]
  }

  def "should invoke fx product merger"() {
    setup:
    def allocationDetails = new AllocationTradeDetails(allocationNotional: 10)
    def tradeDetails = new TradeDetails(customTradeDetails: new GenericTradeDetails())

    when:
    merger.mergeDetails(CUSTOM_FX_1, tradeDetails, allocationDetails)

    then:
    1 * fxMerger.mergeDetails(CUSTOM_FX_1, tradeDetails, allocationDetails)
  }
}
