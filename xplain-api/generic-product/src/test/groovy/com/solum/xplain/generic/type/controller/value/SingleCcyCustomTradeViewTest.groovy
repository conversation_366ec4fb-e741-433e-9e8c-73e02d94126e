package com.solum.xplain.generic.type.controller.value

import com.solum.xplain.generic.type.helpers.GenericPortfolioItemBuilder
import com.solum.xplain.generic.type.product.GenericProductType
import spock.lang.Specification

class SingleCcyCustomTradeViewTest extends Specification {
  def "should correctly map to view"() {
    setup:
    def trade = GenericPortfolioItemBuilder.singleCreditCcyGenericTrade(GenericProductType.CUSTOM_CREDIT_1)

    when:
    def view = NonFxCustomTradeView.of(trade)

    then:
    view.isRight()
    with(view.getOrNull()) {
      productType == GenericProductType.CUSTOM_CREDIT_1
      startDate == trade.getTradeDetails().getStartDate()
      endDate == trade.getTradeDetails().getEndDate()

      tradeDate == trade.tradeDetails.info.tradeDate
      tradeCounterpartyType == trade.tradeDetails.info.counterPartyType
      tradeCounterparty == trade.tradeDetails.info.counterParty
      externalTradeId == trade.externalTradeId
      tradeCurrency == trade.tradeDetails.info.tradeCurrency
      assetType == trade.tradeDetails.customTradeDetails.assetType
      subAssetType == trade.tradeDetails.customTradeDetails.subAssetType
      additionalInfo == trade.tradeDetails.customTradeDetails.additionalInfo
      notionalValue == trade.tradeDetails.customTradeDetails.notional
      underlying == trade.tradeDetails.customTradeDetails.underlying
      optionPosition == trade.tradeDetails.customTradeDetails.optionPosition
      sector == trade.tradeDetails.creditTradeDetails.sector.toString()
      protection == trade.tradeDetails.creditTradeDetails.protection

      updatedAt != null
      updatedBy != null
      tradeId == trade.entityId
    }
  }
}
