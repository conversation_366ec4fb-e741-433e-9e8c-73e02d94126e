package com.solum.xplain.generic.type.controller.value

import com.solum.xplain.generic.type.helpers.GenericPortfolioItemBuilder
import com.solum.xplain.generic.type.product.GenericProductType
import spock.lang.Specification

class FxCustomTradeViewTest extends Specification {

  def "should correctly map to view"() {
    setup:
    def trade = GenericPortfolioItemBuilder.fxGenericTrade(GenericProductType.CUSTOM_FX_1)

    when:
    def view = FxCustomTradeView.of(trade)

    then:
    view.isRight()
    with(view.getOrNull()) {
      productType == GenericProductType.CUSTOM_FX_1
      startDate == trade.getTradeDetails().getStartDate()
      endDate == trade.getTradeDetails().getEndDate()

      tradeDate == trade.tradeDetails.info.tradeDate
      tradeCounterpartyType == trade.tradeDetails.info.counterPartyType
      tradeCounterparty == trade.tradeDetails.info.counterParty
      externalTradeId == trade.externalTradeId

      foreignCurrencyAmount.currency == trade.tradeDetails.payLeg.currency
      foreignCurrencyAmount.amount == trade.tradeDetails.payLeg.notional
      domesticCurrencyAmount.currency == trade.tradeDetails.receiveLeg.currency
      domesticCurrencyAmount.amount == trade.tradeDetails.receiveLeg.notional

      assetType == trade.tradeDetails.customTradeDetails.assetType
      subAssetType == trade.tradeDetails.customTradeDetails.subAssetType
      additionalInfo == trade.tradeDetails.customTradeDetails.additionalInfo
      underlying == trade.tradeDetails.customTradeDetails.underlying
      optionPosition == trade.tradeDetails.customTradeDetails.optionPosition

      updatedAt != null
      updatedBy != null
      tradeId == trade.entityId
    }
  }
}
