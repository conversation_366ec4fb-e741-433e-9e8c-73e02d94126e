package com.solum.xplain.generic.type.csv

import com.solum.xplain.core.helper.IntegrationSpecification
import com.solum.xplain.core.product.ProductType
import com.solum.xplain.core.product.csv.ProductCsvLoaders
import com.solum.xplain.generic.type.product.GenericProductType
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.test.context.ActiveProfiles

@SpringBootTest
@ActiveProfiles("test")
class GenericProductCsvLoadersTest extends IntegrationSpecification {

  @Autowired
  private ProductCsvLoaders loaders

  def "should have loader for #type"() {
    expect:
    loaders.loader((ProductType) type, 1).isRight()
    where:
    type << GenericProductType.values()
  }
}
