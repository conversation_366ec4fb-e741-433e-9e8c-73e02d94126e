package com.solum.xplain.generic.type.csv

import com.solum.xplain.core.helper.IntegrationSpecification
import com.solum.xplain.core.product.ProductType
import com.solum.xplain.core.product.csv.ProductCsvMappers
import com.solum.xplain.generic.type.product.GenericProductType
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.test.context.ActiveProfiles

@SpringBootTest
@ActiveProfiles("test")
class GenericProductCsvMappersTest extends IntegrationSpecification {

  @Autowired
  private ProductCsvMappers mappers

  def "should have loader for #type"() {
    expect:
    mappers.mapper((ProductType) type) != null
    where:
    type << GenericProductType.values()
  }
}
