package com.solum.xplain.generic.type.helpers

import static com.solum.xplain.core.users.UserBuilder.user

import com.opengamma.strata.basics.currency.Currency
import com.solum.xplain.core.common.versions.State
import com.solum.xplain.core.common.versions.VersionedDataAggregations
import com.solum.xplain.core.common.versions.daterange.DateRangeVersionValidity
import com.solum.xplain.core.portfolio.ClientMetrics
import com.solum.xplain.core.portfolio.PortfolioItemBuilder
import com.solum.xplain.core.portfolio.trade.TradeDetails
import com.solum.xplain.core.portfolio.trade.TradeInfoDetails
import com.solum.xplain.core.portfolio.value.CounterpartyType
import com.solum.xplain.core.users.AuditUser
import com.solum.xplain.generic.type.product.GenericProductType
import com.solum.xplain.generic.type.product.details.ResolvableGenericFxTradeDetails
import com.solum.xplain.generic.type.product.details.ResolvableGenericSingleCcyTradeDetails
import java.time.LocalDate
import org.bson.types.ObjectId

class GenericPortfolioItemBuilder {
  static TRADE_DATE = LocalDate.of(2023, 06, 9)
  static START_DATE = LocalDate.ofEpochDay(0)
  static END_DATE = LocalDate.of(2023, 06, 9).plusDays(10)

  def static singleCcyGenericTrade(GenericProductType type) {

    var details = ResolvableGenericSingleCcyTradeDetails.builder()
      .startDate(START_DATE)
      .endDate(END_DATE.plusYears(8))
      .assetType("SINGLE_CCY")
      .subAssetType("SUB_SINGLE")
      .additionalInfo("ADDITIONAL")
      .notional(10.0)
      .underlying("UNDERLYING")
      .build()
      .toTradeDetails(new TradeInfoDetails(
      counterParty: "CPTY",
      tradeCurrency: "EUR",
      counterPartyType: CounterpartyType.BILATERAL,
      tradeDate: TRADE_DATE
      ))
    return trade(type, details, "000000000000000000000001", "externalTradeId")
  }

  def static singleCreditCcyGenericTrade(GenericProductType type) {

    var details = ResolvableGenericSingleCcyTradeDetails.builder()
      .startDate(START_DATE)
      .endDate(END_DATE.plusYears(8))
      .sector("CONSUMER_SERVICES")
      .assetType("SINGLE_CCY")
      .subAssetType("SUB_SINGLE")
      .additionalInfo("ADDITIONAL")
      .notional(10.0)
      .underlying("UNDERLYING")
      .build()
      .toTradeDetails(new TradeInfoDetails(
      counterParty: "CPTY",
      tradeCurrency: "EUR",
      counterPartyType: CounterpartyType.BILATERAL,
      tradeDate: TRADE_DATE,
      ))
    return trade(type, details, "000000000000000000000001", "externalTradeId")
  }

  def static fxGenericTrade(GenericProductType type) {

    var details = ResolvableGenericFxTradeDetails.builder()
      .startDate(START_DATE)
      .endDate(END_DATE.plusYears(8))
      .receiveCurrency(Currency.EUR)
      .payCurrency(Currency.USD)
      .receiveCurrencyAmount(10.0)
      .payCurrencyAmount(-11.0)
      .assetType("SINGLE_CCY")
      .subAssetType("SUB_SINGLE")
      .additionalInfo("ADDITIONAL")
      .underlying("UNDERLYING")
      .build()
      .toTradeDetails(new TradeInfoDetails(
      counterParty: "CPTY",
      tradeCurrency: "EUR",
      counterPartyType: CounterpartyType.BILATERAL,
      tradeDate: TRADE_DATE
      ))

    return trade(type, details, "000000000000000000000001", "externalTradeId")
  }

  def static trade(GenericProductType productType,
    TradeDetails details,
    String portfolioId = "000000000000000000000001",
    String tradeId = "externalTradeId"
  ) {
    return new PortfolioItemBuilder(tradeId)
      .portfolioId(new ObjectId(portfolioId))
      .productType(productType)
      .tradeDetails(details)
      .build()
  }
}
