package com.solum.xplain.generic.type.controller.value

import static java.time.LocalDate.now

import com.solum.xplain.core.common.value.FutureVersionsAction
import com.solum.xplain.core.common.value.NewVersionFormV2
import com.solum.xplain.core.portfolio.form.ClientMetricsForm
import com.solum.xplain.core.portfolio.value.CalculationType
import com.solum.xplain.extensions.enums.CreditSector
import com.solum.xplain.extensions.enums.PositionType
import com.solum.xplain.generic.type.product.GenericProductType
import spock.lang.Specification

class NonFxCustomTradeFormTest extends Specification {
  def "should map to trade value"() {
    setup:
    def form = new NonFxCustomTradeForm(
      externalTradeId: "EXTERNAL_TRADE_ID",
      tradeCounterparty: "COUNTERPARTY",
      tradeCounterpartyType: "BILATERAL",
      description: "DESCRIPTION",
      productType: "CUSTOM_RATES_1",
      startDate: now(),
      endDate: now().plusDays(1),
      tradeDate: now(),
      assetType: "ASSET",
      subAssetType: "SUB_ASSET",
      additionalInfo: "ADDITIONAL_INFO",
      underlying: "UNDERLYING",
      tradeCurrency: "EUR",
      notionalValue: 10,
      payLegType : "FIXED",
      receiveLegType: "INFLATION",
      versionForm: new NewVersionFormV2("comment", now(), now(), FutureVersionsAction.DELETE),
      clientMetrics: new ClientMetricsForm(presentValue: 10)
      )

    when:
    def result = form.toTradeValue()

    then:
    result.isRight()

    and:
    with(result.getOrNull()) {
      description == "DESCRIPTION"
      productType == GenericProductType.CUSTOM_RATES_1

      tradeDetails.customTradeDetails.assetType == "ASSET"
      tradeDetails.customTradeDetails.subAssetType == "SUB_ASSET"
      tradeDetails.customTradeDetails.additionalInfo == "ADDITIONAL_INFO"
      tradeDetails.customTradeDetails.underlying == "UNDERLYING"
      tradeDetails.customTradeDetails.notional == 10
      tradeDetails.info.tradeCurrency == "EUR"
      tradeDetails.creditTradeDetails == null
      tradeDetails.payLeg.type == CalculationType.FIXED
      tradeDetails.receiveLeg.type == CalculationType.INFLATION
    }
  }

  def "should map credit to trade value"() {
    setup:
    def form = new NonFxCustomTradeForm(
      externalTradeId: "EXTERNAL_TRADE_ID",
      tradeCounterparty: "COUNTERPARTY",
      tradeCounterpartyType: "BILATERAL",
      description: "DESCRIPTION",
      productType: "CUSTOM_CREDIT_1",
      sector: "UNDEFINED",
      startDate: now(),
      endDate: now().plusDays(1),
      tradeDate: now(),
      assetType: "ASSET",
      subAssetType: "SUB_ASSET",
      additionalInfo: "ADDITIONAL_INFO",
      tradeCurrency: "EUR",
      notionalValue: 10,
      protection : "SELL",
      versionForm: new NewVersionFormV2("comment", now(), now(), FutureVersionsAction.DELETE),
      clientMetrics: new ClientMetricsForm(presentValue: 10)
      )

    when:
    def result = form.toTradeValue()

    then:
    result.isRight()

    and:
    with(result.getOrNull()) {
      description == "DESCRIPTION"
      productType == GenericProductType.CUSTOM_CREDIT_1

      tradeDetails.customTradeDetails.assetType == "ASSET"
      tradeDetails.customTradeDetails.subAssetType == "SUB_ASSET"
      tradeDetails.customTradeDetails.additionalInfo == "ADDITIONAL_INFO"
      tradeDetails.customTradeDetails.notional == 10
      tradeDetails.info.tradeCurrency == "EUR"
      tradeDetails.creditTradeDetails.sector == CreditSector.UNDEFINED
      tradeDetails.creditTradeDetails.protection == PositionType.SELL
    }
  }

  def "should map to ref sec trade value"() {
    setup:
    def form = new NonFxCustomTradeForm(
      externalTradeId: "EXTERNAL_TRADE_ID",
      productType: "CUSTOM_RATES_1",
      startDate: now(),
      endDate: now().plusDays(1),
      assetType: "ASSET",
      tradeCurrency: "EUR",
      )

    when:
    def result = form.toTradeValue()

    then:
    result.isRight()

    and:
    with(result.getOrNull()) {
      productType == GenericProductType.CUSTOM_RATES_1
      tradeDetails.customTradeDetails.notional == null
      tradeDetails.info.tradeCurrency == "EUR"
      tradeDetails.creditTradeDetails == null
    }
  }
}
