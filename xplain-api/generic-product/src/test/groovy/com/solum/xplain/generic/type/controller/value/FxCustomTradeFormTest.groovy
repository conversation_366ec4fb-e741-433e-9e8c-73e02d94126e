package com.solum.xplain.generic.type.controller.value

import static java.time.LocalDate.now

import com.solum.xplain.core.common.value.FutureVersionsAction
import com.solum.xplain.core.common.value.NewVersionFormV2
import com.solum.xplain.core.portfolio.form.ClientMetricsForm
import com.solum.xplain.core.portfolio.value.CurrencyAmountForm
import com.solum.xplain.generic.type.product.GenericProductType
import spock.lang.Specification

class FxCustomTradeFormTest extends Specification {

  def "should map to trade value"() {
    setup:
    def form = new FxCustomTradeForm(
      externalTradeId: "EXTERNAL_TRADE_ID",
      tradeCounterparty: "COUNTERPARTY",
      tradeCounterpartyType: "BILATERAL",
      description: "DESCRIPTION",
      productType: "CUSTOM_FX_1",
      startDate: now(),
      endDate: now().plusDays(1),
      tradeDate: now(),
      assetType: "ASSET",
      subAssetType: "SUB_ASSET",
      additionalInfo: "ADDITIONAL_INFO",
      underlying: "UNDERLYING",
      optionPosition: "BUY",
      domesticCurrencyAmount: new CurrencyAmountForm(currency: "EUR", amount: 10),
      foreignCurrencyAmount: new CurrencyAmountForm(currency: "USD", amount: -11),
      versionForm: new NewVersionFormV2("comment", now(), now(), FutureVersionsAction.DELETE),
      clientMetrics: new ClientMetricsForm(presentValue: 10)
      )

    when:
    def result = form.toTradeValue()

    then:
    result.isRight()

    and:
    with(result.getOrNull()) {
      description == "DESCRIPTION"
      productType == GenericProductType.CUSTOM_FX_1

      tradeDetails.startDate == now()
      tradeDetails.endDate == now().plusDays(1)
      tradeDetails.payLeg.currency == "USD"
      tradeDetails.payLeg.notional == -11
      tradeDetails.receiveLeg.currency == "EUR"
      tradeDetails.receiveLeg.notional == 10

      tradeDetails.customTradeDetails.assetType == "ASSET"
      tradeDetails.customTradeDetails.subAssetType == "SUB_ASSET"
      tradeDetails.customTradeDetails.additionalInfo == "ADDITIONAL_INFO"
      tradeDetails.customTradeDetails.underlying == "UNDERLYING"
      tradeDetails.customTradeDetails.optionPosition == "BUY"
      tradeDetails.customTradeDetails.notional == null

      tradeDetails.info.tradeCurrency == "EUR"
    }
  }

  def "should map to trade value with fx rate"() {
    setup:
    def form = new FxCustomTradeForm(
      externalTradeId: "EXTERNAL_TRADE_ID",
      tradeCounterparty: "COUNTERPARTY",
      tradeCounterpartyType: "BILATERAL",
      description: "DESCRIPTION",
      productType: "CUSTOM_FX_1",
      startDate: now(),
      fxRate: 0.5,
      endDate: now().plusDays(1),
      tradeDate: now(),
      assetType: "ASSET",
      subAssetType: "SUB_ASSET",
      additionalInfo: "ADDITIONAL_INFO",
      domesticCurrencyAmount: new CurrencyAmountForm(currency: "USD"),
      foreignCurrencyAmount: new CurrencyAmountForm(currency: "EUR"),
      versionForm: new NewVersionFormV2("comment", now(), now(), FutureVersionsAction.DELETE),
      clientMetrics: new ClientMetricsForm(presentValue: 10)
      )

    when:
    def result = form.toTradeValue()

    then:
    result.isRight()

    and:
    with(result.getOrNull()) {
      description == "DESCRIPTION"
      productType == GenericProductType.CUSTOM_FX_1

      tradeDetails.startDate == now()
      tradeDetails.endDate == now().plusDays(1)
      tradeDetails.payLeg.currency == "USD"
      tradeDetails.receiveLeg.currency == "EUR"
      tradeDetails.fxRate == 0.5

      tradeDetails.customTradeDetails.assetType == "ASSET"
      tradeDetails.customTradeDetails.subAssetType == "SUB_ASSET"
      tradeDetails.customTradeDetails.additionalInfo == "ADDITIONAL_INFO"
      tradeDetails.customTradeDetails.notional == null

      tradeDetails.info.tradeCurrency == "USD"
    }
  }
}
