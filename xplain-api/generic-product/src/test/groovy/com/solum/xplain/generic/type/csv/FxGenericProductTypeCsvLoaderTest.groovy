package com.solum.xplain.generic.type.csv

import static com.google.common.io.ByteSource.wrap

import com.google.common.io.ByteStreams
import com.opengamma.strata.basics.currency.Currency
import com.opengamma.strata.collect.io.CsvIterator
import com.opengamma.strata.collect.io.CsvRow
import com.opengamma.strata.collect.io.UnicodeBom
import com.opengamma.strata.product.common.PayReceive
import com.solum.xplain.core.error.ErrorItem
import com.solum.xplain.core.portfolio.trade.TradeInfoDetails
import com.solum.xplain.generic.type.product.GenericProductType
import io.atlassian.fugue.Either
import java.time.LocalDate
import spock.lang.Specification

class FxGenericProductTypeCsvLoaderTest extends Specification {
  def LOADER = new FxGenericProductTypeCsvLoader()

  def "should return correct product types"() {
    expect:
    LOADER.productTypes() == [
      GenericProductType.CUSTOM_FX_1,
      GenericProductType.CUSTOM_FX_2,
      GenericProductType.CUSTOM_FX_3,
      GenericProductType.CUSTOM_FX_4,
      GenericProductType.CUSTOM_FX_5
    ]
  }

  def "should parse BESPOKE Generic Fx trade"() {
    setup:
    def rows = loadResource("CustomFxTrade.csv")

    when:
    def parsedRows = rows.stream().map {
      LOADER.parse(it, false)
    }.toList()

    then:
    parsedRows.size() == 2
    parsedRows.stream().allMatch(Either::isRight)

    and:
    def result = parsedRows[0].getOrNull().toTradeDetails(new TradeInfoDetails())
    result.startDate == LocalDate.parse("2023-01-01")
    result.endDate == LocalDate.parse("2023-12-12")

    def payLeg = result.getPayLeg()
    payLeg.payReceive == PayReceive.PAY
    payLeg.currency == "EUR"
    payLeg.notional == -10.0

    def receiveLeg = result.getReceiveLeg()
    receiveLeg.payReceive == PayReceive.RECEIVE
    receiveLeg.currency == "USD"
    receiveLeg.notional == 11

    def genericDetails = result.customTradeDetails
    genericDetails.assetType == "ASSET"
    genericDetails.subAssetType == "SUB"
    genericDetails.additionalInfo == "ADDITIONAL"
    genericDetails.optionPosition == "SELL"
  }

  def "should parse REFSEC Generic Fx trade"() {
    setup:
    def rows = loadResource("CustomFxTrade.csv")

    when:
    def parsedRows = rows.stream().map {
      LOADER.parse(it, true)
    }.toList()

    then:
    parsedRows.size() == 2
    parsedRows.stream().allMatch(Either::isRight)

    and:
    def result = parsedRows[0].getOrNull().toTradeDetails(new TradeInfoDetails())
    result.startDate == LocalDate.parse("2023-01-01")
    result.endDate == LocalDate.parse("2023-12-12")
    result.fxRate == 0.2

    def payLeg = result.getPayLeg()
    payLeg.payReceive == PayReceive.PAY
    payLeg.currency == "EUR"
    payLeg.notional == null

    def receiveLeg = result.getReceiveLeg()
    receiveLeg.payReceive == PayReceive.RECEIVE
    receiveLeg.currency == "USD"
    receiveLeg.notional == null

    def genericDetails = result.customTradeDetails
    genericDetails.assetType == "ASSET"
    genericDetails.subAssetType == "SUB"
    genericDetails.additionalInfo == "ADDITIONAL"
    genericDetails.optionPosition == "SELL"
  }

  def "should parse BESPOKE Generic Fx trade currency"() {
    setup:
    def rows = loadResource("CustomFxTrade.csv")

    when:
    def parsedRows = rows.stream().map { LOADER.parseTradeCcy(it) }.toList()

    then:
    parsedRows.size() == 2
    parsedRows[0] == Currency.EUR
    parsedRows[1] == Currency.USD
  }

  def "should return Generic FX trade parse errors"() {
    setup:
    def rows = loadResource("CustomFxTradeInvalid.csv")

    when:
    def parsedRow = LOADER.parse(rows[row], false)

    then:
    parsedRow.isLeft()
    def error = (ErrorItem) parsedRow.left().get()
    error.description.startsWith(expectedError)

    where:
    row | expectedError
    0 | "Error at line number 2. Error: No value was found for 'Start Date'"
    1 | "Error at line number 3. Error: No value was found for 'End Date'"
    2 | "Error at line number 4. Error: Error parsing field End Date. Must be after Start Date"
    3 | "Error at line number 5. Error: Unable to parse pay/receive from '', must be one of 'Pay', 'Receive', 'Rec', 'P', or 'R' (case insensitive)"
    4 | "Error at line number 6. Error: No value was found for 'Leg1.Ccy'"
    5 | "Error at line number 7. Error: Unable to parse double from ''"
    6 | "Error at line number 8. Error: Unable to parse pay/receive from '', must be one of 'Pay', 'Receive', 'Rec', 'P', or 'R' (case insensitive)"
    7 | "Error at line number 9. Error: No value was found for 'Leg2.Ccy'"
    8 | "Error at line number 10. Error: Unable to parse double from ''"
    9 | "Error at line number 11. Error: Detected two legs having the same direction: [Pay]"
    10 | "Error at line number 12. Error: Value must be positive for field: Leg1.Notional"
    11 | "Error at line number 13. Error: Value must be positive for field: Leg2.Notional"
  }

  List<CsvRow> loadResource(String fileName) {
    def content = ByteStreams.toByteArray(getClass().getResourceAsStream("/csv/" + fileName))
    return CsvIterator.of(UnicodeBom.toCharSource(wrap(content)), true)
    .asStream()
    .toList()
  }
}
