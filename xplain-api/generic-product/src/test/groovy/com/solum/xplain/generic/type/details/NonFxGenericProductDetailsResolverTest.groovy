package com.solum.xplain.generic.type.details

import static com.solum.xplain.generic.type.helpers.GenericPortfolioItemBuilder.singleCreditCcyGenericTrade
import static com.solum.xplain.generic.type.product.GenericProductType.CUSTOM_CREDIT_2

import com.solum.xplain.core.portfolio.trade.TradeDetails
import com.solum.xplain.generic.type.product.GenericProductType
import spock.lang.Specification

class NonFxGenericProductDetailsResolverTest extends Specification {
  def RESOLVER = new NonFxGenericProductDetailsResolver()

  def "should return correct product types"() {
    expect:
    RESOLVER.productTypes().size() == 29
  }

  def "should fall back to default underlying"() {
    expect:
    RESOLVER.resolveUnderlying(GenericProductType.CUSTOM_RATES_1, new TradeDetails()) == "Custom Rates"
  }

  def "should resolve trade underlying"() {
    expect:
    RESOLVER.resolveUnderlying(CUSTOM_CREDIT_2, singleCreditCcyGenericTrade(CUSTOM_CREDIT_2).tradeDetails) == "UNDERLYING"
  }

  def "should resolve notional"() {
    expect:
    RESOLVER.resolveNotional(singleCreditCcyGenericTrade(CUSTOM_CREDIT_2).tradeDetails) == 10.0d
  }

  def "should resolve notional when refSec (notional is null)"() {
    def refSecDetails = singleCreditCcyGenericTrade(CUSTOM_CREDIT_2).tradeDetails
    refSecDetails.customTradeDetails.setNotional(null)
    expect:
    RESOLVER.resolveNotional(refSecDetails) == 0d
  }
}
