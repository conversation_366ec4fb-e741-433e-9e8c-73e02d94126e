package com.solum.xplain.generic.type.details

import static com.solum.xplain.generic.type.helpers.GenericPortfolioItemBuilder.fxGenericTrade
import static com.solum.xplain.generic.type.product.GenericProductType.CUSTOM_FX_1

import com.solum.xplain.core.portfolio.trade.TradeDetails
import com.solum.xplain.core.portfolio.trade.details.FxDetailsResolver
import spock.lang.Specification

class FxGenericProductDetailsResolverTest extends Specification {

  def FX_RESOLVER = Mock(FxDetailsResolver)
  def RESOLVER = new FxGenericProductDetailsResolver(FX_RESOLVER)

  def "should return correct product types"() {
    expect:
    RESOLVER.productTypes().size() == 5
  }

  def "should fall back to default underlying"() {
    expect:
    RESOLVER.resolveUnderlying(CUSTOM_FX_1, new TradeDetails()) == "Custom FX"
  }

  def "should resolve trade underlying"() {
    expect:
    RESOLVER.resolveUnderlying(CUSTOM_FX_1, fxGenericTrade(CUSTOM_FX_1).tradeDetails) == "UNDERLYING"
  }

  def "should resolve notional"() {
    setup:
    def details = fxGenericTrade(CUSTOM_FX_1).tradeDetails

    when:
    def result = RESOLVER.resolveNotional(details)

    then:
    1 * FX_RESOLVER.resolveNotional(details) >> 10.0d

    and:
    result == 10.0d
  }
}
