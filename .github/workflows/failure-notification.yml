name: Notify main branch failures
on:
  workflow_run:
    workflows: [Xplain API Build, Xplain Valuations Build, Xplain XVA PROXY build, FOSSA Scan, OWASP report]
    types: [completed]
    branches: [main]

jobs:
  on-failure:
    runs-on: ubuntu-latest
    if: github.event.workflow_run.conclusion == 'failure' || github.event.workflow_run.conclusion == 'timed_out'
    steps:
      - uses: ravsamhq/notify-slack-action@v2
        with:
          status: ${{ github.event.workflow_run.conclusion }}
          notification_title: " ${{github.event.workflow_run.name}} - ${{github.event.workflow_run.conclusion}} on ${{github.event.workflow_run.head_branch}} - <${{github.server_url}}/${{github.repository}}/actions/runs/${{github.event.workflow_run.id}}|View Failure>"
          message_format: ":fire: *${{github.event.workflow_run.name}}* ${{github.event.workflow_run.conclusion}} in <${{github.server_url}}/${{github.repository}}/${{github.event.workflow_run.head_branch}}|${{github.repository}}>"
          footer: "Linked Repo <${{github.server_url}}/${{github.repository}}|${{github.repository}}> | <${{github.server_url}}/${{github.repository}}/actions/runs/${{github.event.workflow_run.id}}|View Failure>"
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.XPLAIN_FAILS_SLACK_WEBHOOK }}
