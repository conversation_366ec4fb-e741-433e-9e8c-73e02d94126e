name: FOSSA Scan

on:
  workflow_dispatch:
  push:
    branches:
      - 'main'
      - 'release-*'
    tags:
      - 'v*'
  pull_request:
    branches:
      - main
permissions:
  contents: read
  checks: write
  id-token: write
jobs:
  fossa-scan:
    runs-on: ubuntu-latest
    steps:
      - name: Set up JDK 21
        uses: actions/setup-java@v5
        with:
          distribution: 'temurin'
          java-version: 21.0.7
      - name: "Checkout Code"
        uses: actions/checkout@v5
        with:
          lfs: false # Don't need LFS files for dependency analysis
      - name: Resolve branch
        id: resolve-branch
        run: |
          if [ "${{ github.event_name }}" == "pull_request" ]; then
            echo "branch=${{ github.head_ref }}"
            echo "branch=${{ github.head_ref }}" >> $GITHUB_OUTPUT || exit 1
          else
            echo "branch=${{ github.ref_name }}"
            echo "branch=${{ github.ref_name }}" >> $GITHUB_OUTPUT || exit 1
          fi
      - name: "Run FOSSA Scan"
        uses: fossas/fossa-action@main
        with:
          api-key: ${{ secrets.FOSSA_API_KEY }}
          branch: ${{ steps.resolve-branch.outputs.branch }}
        env:
          GITHUB_TOKEN: ${{ secrets.PAT_GITHUB_TOKEN }}
      - name: "Run FOSSA Tests"
        uses: fossas/fossa-action@main
        with:
          api-key: ${{ secrets.FOSSA_API_KEY }}
          run-tests: true
        env:
          GITHUB_TOKEN: ${{ secrets.PAT_GITHUB_TOKEN }}
