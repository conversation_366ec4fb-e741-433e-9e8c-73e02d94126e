name: 'Build Java Docker'
description: 'Build Java docker image with jib'
inputs:
  aws-access-key-id:
    description: "AWS Access Key Id"
    required: true
  aws-secret-access-key:
    description: "AWS Secret Access Key"
    required: true
  aws-region:
    description: "AWS Region"
    required: true
  github-token:
    description: "Github Token"
    required: true
  branch:
    description: "Built branch name"
    required: true
  image-tag:
    description: "Built image unique tag"
    required: true
  module-name:
    description: "Module to build"
    required: true
runs:
  using: "composite"
  steps:
    - name: Configure AWS credentials
      uses: aws-actions/configure-aws-credentials@v4.0.2
      with:
        aws-access-key-id: ${{ inputs.aws-access-key-id }}
        aws-secret-access-key: ${{ inputs.aws-secret-access-key }}
        mask-aws-account-id: true
        aws-region: ${{ inputs.aws-region }}
    - name: Login to Amazon ECR
      id: login-ecr
      uses: aws-actions/amazon-ecr-login@v2
      with:
        mask-password: true
    - name: Build ECR docker image
      shell: bash
      run: ./gradlew :${{ inputs.module-name }}:jib -Djib.to.tags=${{ inputs.image-tag }},${{ inputs.branch }}
      env:
        GITHUB_TOKEN: ${{ inputs.github-token }}
    - name: Logout of Amazon ECR
      shell: bash
      if: always()
      run: docker logout ${{ steps.login-ecr.outputs.registry }}
