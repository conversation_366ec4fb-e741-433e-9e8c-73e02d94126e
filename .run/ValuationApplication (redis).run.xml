<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="ValuationApplication (redis)" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot">
    <option name="ACTIVE_PROFILES" value="dev,local,redis" />
    <module name="solum-xplain-api.xplain-valuation.app.main" />
    <option name="SPRING_BOOT_MAIN_CLASS" value="com.solum.xplain.valuation.ValuationApplication" />
    <option name="VM_PARAMETERS" value="-Dspring.profiles.active=dev,local,redis -Dspring.autoconfigure.exclude=org.springframework.boot.autoconfigure.hazelcast.HazelcastAutoConfiguration -Dspring.data.redis.cluster.nodes=localhost:6379,localhost:6380,localhost:6381,localhost:6382,localhost:6383,localhost:6384 -Dspring.data.redis.cluster.max-redirects=3 -Djdk.tracePinnedThreads=full" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
</component>