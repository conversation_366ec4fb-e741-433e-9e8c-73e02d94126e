<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="ApiApplication (redis)" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot">
    <option name="ACTIVE_PROFILES" value="dev,local,redis" />
    <module name="solum-xplain-api.xplain-api.app.main" />
    <option name="SPRING_BOOT_MAIN_CLASS" value="com.solum.xplain.app.ApiApplication" />
    <option name="UPDATE_ACTION_UPDATE_POLICY" value="UpdateClassesAndResources" />
    <option name="VM_PARAMETERS" value="-Duser.timezone=UTC -Dspring.profiles.active=dev,local,redis -Dspring.autoconfigure.exclude=org.springframework.boot.autoconfigure.hazelcast.HazelcastAutoConfiguration -Dspring.data.redis.cluster.nodes=localhost:6379,localhost:6380,localhost:6381,localhost:6382,localhost:6383,localhost:6384 -Dspring.data.redis.cluster.max-redirects=3 -Djdk.tracePinnedThreads=full" />
    <extension name="coverage">
      <pattern>
        <option name="PATTERN" value="com.solum.xplain.app.*" />
        <option name="ENABLED" value="true" />
      </pattern>
    </extension>
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
</component>