#!/bin/sh

# Commit-msg hook to enforce company email addresses

# Colors for output
RED='\033[0;31m'
YELLOW='\033[1;33m'
GREEN='\033[0;32m'
DEFAULT='\033[0m' # terminals default color

check_company_email() {
    # Get the configured email address
    local email=$(git config user.email)

    if [ -z "$email" ]; then
        echo "${RED}ERROR: No email address configured in git.${DEFAULT}"
        echo "${YELLOW}Please configure your email with:"
        echo "  git config user.email '<EMAIL>'${DEFAULT}"
        return 1
    fi

    # Check if email ends with @solum-financial.com
    if ! echo "$email" | grep -qE "@solum-financial\.com$"; then
        echo "${RED}ERROR: Invalid email address: $email${DEFAULT}"
        echo "${YELLOW}Company policy requires commits to use @solum-financial.com email addresses.${DEFAULT}"
        echo "${YELLOW}Please update your email with:"
        echo "  git config user.email '<EMAIL>'${DEFAULT}"
        return 1
    fi

    echo "${GREEN}✓ Valid company email: $email${DEFAULT}"
    return 0
}

# Check company email
if ! check_company_email; then
    exit 1
fi

exit 0
