#!/bin/sh

# If the push refs start with (delete), then we're deleting a branch, so skip the pre-push hook
STDIN=$(cat -)
if echo "$STDIN" | grep -q "^(delete)"; then
  echo "(delete) found at start of push refs, skipping husky pre-push hook"
  echo $STDIN
  exit 0
fi

# Run Git LFS pre-push hook first
if command -v git-lfs >/dev/null 2>&1; then
  echo "$STDIN" | git lfs pre-push "$@"
  LFS_RESULT=$?
  if [ $LFS_RESULT -ne 0 ]; then
    echo "Git LFS pre-push hook failed"
    exit $LFS_RESULT
  fi
else
  echo "Warning: Git LFS not found. Large files (.gz, .zip) may not be handled correctly."
fi

# Run spotless check
./gradlew spotlessCheck
RESULT=$?

[ $RESULT -eq 0 ] && exit 0

./gradlew spotlessApply
exit 1

