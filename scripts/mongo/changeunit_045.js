db.calculationResultCharts.aggregate([
  {
    $lookup: {
      from: "calculationResult",
      localField: "_id",
      foreignField: "_id",
      as: "calculationResult"
    }
  },
  {
    $unwind: "$calculationResult"
  },
  {
    $project: {
      _id: 1,
      _class: "com.solum.xplain.calculation.CalculationResultCurves",
      valuationDate: "$calculationResult.valuationDate",
      curveConfigurationCurves: {
        $map: {
          input: "$curveConfigurationChartData",
          as: "var",
          in: {
            name: "$$var.indexName",
            yValueType: "$$var.valueType",
            chartPoints: "$$var.chartPoints"
          }
        }
      },
      fxCurveConfigurationCurves: {
        $map: {
          input: "$fxCurveConfigurationChartData",
          as: "var",
          in: {
            name: "$$var.indexName",
            yValueType: "$$var.valueType",
            chartPoints: "$$var.chartPoints"
          }
        }
      }
    }
  },
  {
    $out: {
      coll: "calculationResultsCurves"
    }
  }
]);

db.getCollection("calculationResultCharts").drop();

db.getCollection("mongockChangeLog").insertMany([
  {
    "author": "donatasz",
    "changeId": "45_before",
    "executionId": "changeunit_045",
    "changeLogClass": "com.solum.xplain.support.migration.changeunits.ChangeUnit045",
    "changeSetMethod": "beforeExecution",
    "errorTrace": null,
    "executionHostname": "manual",
    "executionMillis": NumberLong(1),
    "metadata": null,
    "state": "EXECUTED",
    "systemChange": false,
    "timestamp": ISODate(),
    "type": "BEFORE_EXECUTION"
  },
  {
    "author": "donatasz",
    "changeId": "45",
    "executionId": "changeunit_045",
    "changeLogClass": "com.solum.xplain.support.migration.changeunits.ChangeUnit045",
    "changeSetMethod": "execution",
    "errorTrace": null,
    "executionHostname": "manual",
    "executionMillis": NumberLong(1),
    "metadata": null,
    "state": "EXECUTED",
    "systemChange": false,
    "timestamp": ISODate(),
    "type": "EXECUTION"
  }
]);
