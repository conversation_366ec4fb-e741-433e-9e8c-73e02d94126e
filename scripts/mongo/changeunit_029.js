// Collection names for task executions
var taskExecutions = ["taskExecution", "ipvTaskExecution"];

taskExecutions.forEach(function (collection) {
    // execution method
    db.getCollection(collection).updateMany(
        {"status": "IN_PROGRESS"},
        {$set: {"status": "IN_RESOLUTION"}},
    );

    db.getCollection(collection).updateMany(
        {"previousStatuses": {$exists: true, $ne: []}},
        {$set: {"previousStatuses.$[element].status": "IN_RESOLUTION"}},
        {arrayFilters: [{"element.status": "IN_PROGRESS"}], multi: true}
    );
});

// Collection names for results
var results = ["instrumentResultPreliminary", "instrumentResultOverlay", "ipvTradeResultOverlay"];

results.forEach(function (collection) {
    // beforeExecution method
    db.getCollection(collection).updateMany(
        {"status": "PENDING"},
        {$set: {"status": "WAITING_RESOLUTION"}},
    );

    db.getCollection(collection).updateMany(
        {"previousStatuses": {$exists: true, $ne: []}},
        {$set: {"previousStatuses.$[element].status": "WAITING_RESOLUTION"}},
        {arrayFilters: [{"element.status": "PENDING"}], multi: true}
    );
});


db.getCollection("mongockChangeLog").insertMany([
    {
        "author": "solum",
        "changeId": "29_before",
        "executionId": "changeunit_029",
        "changeLogClass": "com.solum.xplain.support.migration.changeunits.ChangeUnit029",
        "changeSetMethod": "beforeExecution",
        "errorTrace": null,
        "executionHostname": "manual",
        "executionMillis": NumberLong(1),
        "metadata": null,
        "state": "EXECUTED",
        "systemChange": false,
        "timestamp": ISODate(),
        "type": "BEFORE_EXECUTION"
    },
    {
        "author": "solum",
        "changeId": "29",
        "executionId": "changeunit_029",
        "changeLogClass": "com.solum.xplain.support.migration.changeunits.ChangeUnit029",
        "changeSetMethod": "execution",
        "errorTrace": null,
        "executionHostname": "manual",
        "executionMillis": NumberLong(1),
        "metadata": null,
        "state": "EXECUTED",
        "systemChange": false,
        "timestamp": ISODate(),
        "type": "EXECUTION"
    }
]);
