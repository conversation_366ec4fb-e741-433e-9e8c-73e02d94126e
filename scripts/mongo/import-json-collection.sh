#!/bin/bash


# Use first argument as the source path
if [ -z "$1" ] || [ -z "$2" ]; then
  echo "Usage: $0 <source-file.json> <collection-name>"
  exit 1
fi

# check that we have the source file
if [ ! -f "$1" ]; then
  echo "Error: $1 not found "
  exit 1
fi

# get the directory name at the end of $1
FILENAME=$(basename "$1")
echo copying $FILENAME into docker container at /mongo-import

CONTAINER=solum-xplain-api-solum_mongo-1

docker exec $CONTAINER mkdir -p /mongo-import \
    && docker cp "$1" $CONTAINER:/mongo-import \
    && docker exec $CONTAINER mongoimport --drop --jsonArray --db=solum-xplain --collection=$2 /mongo-import/$FILENAME \
    && echo "✔ imported"

