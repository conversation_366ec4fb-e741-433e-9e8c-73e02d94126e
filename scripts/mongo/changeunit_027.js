var collections = ["instrumentResultPreliminary", "instrumentResultOverlay", "ipvTradeResultOverlay"];

collections.forEach(function (collection) {
    db.getCollection(collection).updateMany(
        {"breakTests": {$ne: null}},
        [
            {
                "$set": {
                    "breakTests": {
                        "$map": {
                            "input": "$breakTests",
                            "as": "element",
                            "in": {
                                "$mergeObjects": [
                                    "$$element",
                                    {
                                        "providerValue": {
                                            "$mergeObjects": [
                                                "$$element.providerValue",
                                                {
                                                    "triggeredThresholdLevel": {
                                                        "$cond": {
                                                            "if": {
                                                                "$eq": [
                                                                    {
                                                                        "$ifNull": [
                                                                            "$$element.threshold",
                                                                            ""
                                                                        ]
                                                                    },
                                                                    ""
                                                                ]
                                                            },
                                                            "then": null,
                                                            "else": 1
                                                        }
                                                    },
                                                    "triggeredThreshold": "$$element.threshold"
                                                }
                                            ]
                                        }
                                    }
                                ]
                            }
                        }
                    }
                }
            }
        ]
    )
})
db.getCollection("mongockChangeLog").insertMany([
    {
        "author": "solum",
        "changeId": "27_before",
        "executionId": "changeunit_027",
        "changeLogClass": "com.solum.xplain.support.migration.changeunits.ChangeUnit027",
        "changeSetMethod": "beforeExecution",
        "errorTrace": null,
        "executionHostname": "manual",
        "executionMillis": NumberLong(1),
        "metadata": null,
        "state": "EXECUTED",
        "systemChange": false,
        "timestamp": ISODate(),
        "type": "BEFORE_EXECUTION"
    },
    {
        "author": "solum",
        "changeId": "27",
        "executionId": "changeunit_027",
        "changeLogClass": "com.solum.xplain.support.migration.changeunits.ChangeUnit027",
        "changeSetMethod": "execution",
        "errorTrace": null,
        "executionHostname": "manual",
        "executionMillis": NumberLong(1),
        "metadata": null,
        "state": "EXECUTED",
        "systemChange": false,
        "timestamp": ISODate(),
        "type": "EXECUTION"
    }
]);
