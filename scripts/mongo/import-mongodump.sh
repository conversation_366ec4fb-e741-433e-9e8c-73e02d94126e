#!/bin/bash


# Use first argument as the source path
if [ -z "$1" ]; then
  echo "Usage: $0 <source-path>"
  exit 1
fi

# check if source path exists
if [ ! -d "$1" ]; then
  echo "Error: Source path does not exist"
  exit 1
fi

# check that we have company.bson
if [ ! -f "$1/company.bson" ]; then
  echo "Error: company.bson not found in source path"
#  exit 1
fi

# get the directory name at the end of $1
DIRNAME=$(basename "$1")
echo copying into docker container at /mongo-import/$DIRNAME

CONTAINER=solum-xplain-api-solum_mongo-1

docker exec $CONTAINER mkdir -p /mongo-import \
    && docker cp "$1" $CONTAINER:/mongo-import/$DIRNAME \
    && docker exec $CONTAINER mongosh solum-xplain --eval "db.dropDatabase()" \
    && docker exec $CONTAINER mongorestore --db=solum-xplain /mongo-import/$DIRNAME --quiet \
    && echo "✔ mongodump restored"

