#!/bin/sh

# Pre-commit hook to prevent committing license keys in application*.yml files
# and enforce company email addresses

# Colors for output
RED='\033[0;31m'
YELLOW='\033[1;33m'
GREEN='\033[0;32m'
DEFAULT='\033[0m' # terminals default color

check_license_keys() {
    local file="$1"
    local has_license_key=false
    local line_number=0

    while IFS= read -r line; do
        line_number=$((line_number + 1))

        # Check if line contains license-key with a non-empty value
        if echo "$line" | grep -q "license-key:" && ! echo "$line" | grep -qE "license-key:\s*$|license-key:\s*\"\"\s*$|license-key:\s*null\s*$"; then
            echo "${RED}ERROR: License key found in $file at line $line_number:${DEFAULT}"
            echo "${YELLOW}  $line${DEFAULT}"
            has_license_key=true
        fi
    done < "$file"

    if [ "$has_license_key" = true ]; then
        return 1
    else
        return 0
    fi
}

check_company_email() {
    # Get the configured email address
    local email=$(git config user.email)

    if [ -z "$email" ]; then
        echo "${RED}ERROR: No email address configured in git.${DEFAULT}"
        echo "${YELLOW}Please configure your email with:"
        echo "  git config user.email '<EMAIL>'${DEFAULT}"
        return 1
    fi

    # Check if email ends with @solum-financial.com
    if ! echo "$email" | grep -qE "@solum-financial\.com$"; then
        echo "${RED}ERROR: Invalid email address: $email${DEFAULT}"
        echo "${YELLOW}Company policy requires commits to use @solum-financial.com email addresses.${DEFAULT}"
        echo "${YELLOW}Please update your email with:"
       echo "  git config user.email '<EMAIL>'${DEFAULT}"
        return 1
    fi

    echo "${GREEN}✓ Valid company email: $email${DEFAULT}"
    return 0
}

# Check company email first
if ! check_company_email; then
    exit 1
fi

# Get list of staged files that match application*.yml pattern
staged_files=$(git diff --cached --name-only --diff-filter=ACM | grep -E "application.*\.yml$" || true)

# No application*.yml files staged
if [ -z "$staged_files" ]; then
    exit 0
fi

echo "Checking staged application*.yml files for license keys..."

found_license_keys=false

# Check each staged application*.yml file
for file in $staged_files; do
    if [ -f "$file" ]; then
        check_license_keys "$file"
        if [ $? -eq 1 ]; then
            found_license_keys=true
        fi
    fi
done

# If license keys were found, prevent the commit
if [ "$found_license_keys" = true ]; then
    echo ""
    echo "${RED}COMMIT REJECTED: License keys detected in application*.yml files.${DEFAULT}"
    echo "${YELLOW}Please remove license keys before committing!${DEFAULT}" # reset to default color
    echo ""
    exit 1
fi

echo "No license keys found in staged application*.yml files."
exit 0
