# output
/ui/dist/

# dependencies
/ui/node_modules

# xva
/XVA_compiled_for_linux/

# e2e
/ui/e2e/*.js
/ui/e2e/*.map

# debug
/ui/npm-debug.log

.gradle
build
/*/build
!gradle/wrapper/gradle-wrapper.jar

# This cached data is not in the build folder as we don't want clean to wipe it
/dependency-check/data/

### IntelliJ IDEA ###
.idea
*.iws
*.iml
*.ipr
/api/build/
out/**
**/out
**/generated
/docker-compose.override.yml
application-local.yml
ui-env-local.js

###MacOS
**/.DS_Store

**/pinning.jfr
