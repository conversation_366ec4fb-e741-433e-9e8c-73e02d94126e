package com.solum.xplain.valuation.messages;

import com.solum.xplain.shared.datagrid.impl.redis.ForySerializationConfigurer;
import com.solum.xplain.valuation.messages.calibration.rates.ValuationCurveRates;
import com.solum.xplain.valuation.messages.calibration.vols.ValuationCalibratedCaplets;
import org.jspecify.annotations.NullMarked;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Auto-configuration for valuation messages which ensures that if Fory serialization is available
 * then it is configured to register message data types which are known to contain transient fields.
 */
@AutoConfiguration
@NullMarked
public class ValuationMessagesAutoConfiguration {
  @Configuration
  @ConditionalOnClass(ForySerializationConfigurer.class)
  static class ValuationMessagesForySerializationConfiguration {
    @Bean
    ForySerializationConfigurer valuationMessagesForySerializationConfigurer() {
      return config -> {
        config.register(ValuationCalibratedCaplets.class);
        config.register(ValuationCurveRates.class);
      };
    }
  }
}
