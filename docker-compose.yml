services:
  solum_mongo:
    image: mongo:7
    ulimits:
      memlock:
        soft: -1
        hard: -1
    deploy:
      resources:
        limits:
          memory: 1g
    volumes:
      - solum_mongo_data:/data/db
    ports:
      - 27017:27017
  xplain_ui:
    image: 575153581909.dkr.ecr.eu-west-2.amazonaws.com/solum-xplain-ui:main
    network_mode: "host"
    volumes:
      - ./nginx-for-api-dev.conf:/etc/nginx/nginx.conf:ro
      - ./ui-env.js:/usr/share/nginx/html/env.js:ro
  xplain_kafka:
    image: confluentinc/cp-kafka:7.5.5
    ports:
      - 29092:29092
    environment:
      CLUSTER_ID: MkU3OEVBNT8a236c3c9c19 # could be ${KAFKA_CLUSTER_ID}
      KAFKA_CONTROLLER_QUORUM_VOTERS: '1@xplain_kafka:29093'
      KAFKA_CONTROLLER_LISTENER_NAMES: CONTROLLER
      KAFKA_PROCESS_ROLES: broker,controller
      <PERSON><PERSON><PERSON>_BROKER_ID: 1
      KAFKA_ADVERTISED_LISTENERS: SASL_PLAINTEXT://xplain_kafka:9092,SASL_PLAINTEXT_HOST://localhost:29092
      KAFKA_LISTENERS: 'SASL_PLAINTEXT://xplain_kafka:9092,CONTROLLER://xplain_kafka:29093,SASL_PLAINTEXT_HOST://0.0.0.0:29092'
      KAFKA_LISTENER_SECURITY_PROTOCOL_MAP: CONTROLLER:PLAINTEXT,SASL_PLAINTEXT:SASL_PLAINTEXT,SASL_PLAINTEXT_HOST:SASL_PLAINTEXT
      KAFKA_INTER_BROKER_LISTENER_NAME: SASL_PLAINTEXT
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
      KAFKA_OPTS: "-Djava.security.auth.login.config=/etc/kafka/kafka_server_jaas.conf"
      KAFKA_SASL_ENABLED_MECHANISMS: PLAIN
      KAFKA_SASL_MECHANISM_INTER_BROKER_PROTOCOL: PLAIN
    volumes:
      - ./kafka_server_jaas.conf:/etc/kafka/kafka_server_jaas.conf
  xplain_kafka_ui:
    container_name: xplain_kafka-ui
    image: provectuslabs/kafka-ui:latest
    ports:
      - 8070:8080
    depends_on:
      - xplain_kafka
    environment:
      KAFKA_CLUSTERS_0_NAME: xplain
      KAFKA_CLUSTERS_0_BOOTSTRAPSERVERS: xplain_kafka:9092
      KAFKA_CLUSTERS_0_PROPERTIES_SECURITY_PROTOCOL: SASL_PLAINTEXT
      KAFKA_CLUSTERS_0_PROPERTIES_SASL_MECHANISM: PLAIN
      KAFKA_CLUSTERS_0_PROPERTIES_SASL_JAAS_CONFIG: 'org.apache.kafka.common.security.plain.PlainLoginModule required username="admin" password="admin-secret";'
    profiles:
      - kafka-ui

  redis-node1:
    hostname: redis-node1
    image: redis:7.4.2
    command: redis-server --bind 0.0.0.0 --cluster-announce-ip host.docker.internal --cluster-announce-port 6379 --cluster-announce-bus-port 16379 --port 6379 --cluster-enabled yes --cluster-node-timeout 5000
    ports:
      - "6379:6379"
      - "16379:16379"
    networks:
      - redis-cluster-net
    extra_hosts:
      - "host.docker.internal:host-gateway"
    healthcheck:
      test: [ "CMD", "redis-cli", "-p", "6379", "ping" ]
      interval: 10s
      timeout: 5s
      retries: 3
    tmpfs:
      - /data

  redis-node2:
    hostname: redis-node2
    image: redis:7.4.2
    command: redis-server --bind 0.0.0.0 --cluster-announce-ip host.docker.internal --cluster-announce-port 6380 --cluster-announce-bus-port 16380 --port 6380 --cluster-enabled yes --cluster-node-timeout 5000
    ports:
      - "6380:6380"
      - "16380:16380"
    networks:
      - redis-cluster-net
    extra_hosts:
      - "host.docker.internal:host-gateway"
    healthcheck:
      test: [ "CMD", "redis-cli", "-p", "6380", "ping" ]
      interval: 10s
      timeout: 5s
      retries: 3
    tmpfs:
      - /data

  redis-node3:
    hostname: redis-node3
    image: redis:7.4.2
    command: redis-server --bind 0.0.0.0 --cluster-announce-ip host.docker.internal --cluster-announce-port 6381 --cluster-announce-bus-port 16381 --port 6381 --cluster-enabled yes --cluster-node-timeout 5000
    ports:
      - "6381:6381"
      - "16381:16381"
    networks:
      - redis-cluster-net
    extra_hosts:
      - "host.docker.internal:host-gateway"
    healthcheck:
      test: [ "CMD", "redis-cli", "-p", "6381", "ping" ]
      interval: 10s
      timeout: 5s
      retries: 3
    tmpfs:
      - /data

  redis-node4:
    hostname: redis-node4
    image: redis:7.4.2
    command: redis-server --bind 0.0.0.0 --cluster-announce-ip host.docker.internal --cluster-announce-port 6382 --cluster-announce-bus-port 16382 --port 6382 --cluster-enabled yes --cluster-node-timeout 5000
    ports:
      - "6382:6382"
      - "16382:16382"
    networks:
      - redis-cluster-net
    extra_hosts:
      - "host.docker.internal:host-gateway"
    healthcheck:
      test: [ "CMD", "redis-cli", "-p", "6382", "ping" ]
      interval: 10s
      timeout: 5s
      retries: 3
    tmpfs:
      - /data

  redis-node5:
    hostname: redis-node5
    image: redis:7.4.2
    command: redis-server --bind 0.0.0.0 --cluster-announce-ip host.docker.internal --cluster-announce-port 6383 --cluster-announce-bus-port 16383 --port 6383 --cluster-enabled yes --cluster-node-timeout 5000
    ports:
      - "6383:6383"
      - "16383:16383"
    networks:
      - redis-cluster-net
    extra_hosts:
      - "host.docker.internal:host-gateway"
    healthcheck:
      test: [ "CMD", "redis-cli", "-p", "6383", "ping" ]
      interval: 10s
      timeout: 5s
      retries: 3
    tmpfs:
      - /data

  redis-node6:
    hostname: redis-node6
    image: redis:7.4.2
    command: redis-server --bind 0.0.0.0 --cluster-announce-ip host.docker.internal --cluster-announce-port 6384 --cluster-announce-bus-port 16384 --port 6384 --cluster-enabled yes --cluster-node-timeout 5000
    ports:
      - "6384:6384"
      - "16384:16384"
    networks:
      - redis-cluster-net
    extra_hosts:
      - "host.docker.internal:host-gateway"
    healthcheck:
      test: [ "CMD", "redis-cli", "-p", "6384", "ping" ]
      interval: 10s
      timeout: 5s
      retries: 3
    tmpfs:
      - /data

  redis-cluster-init:
    image: redis:7.4.2
    command: redis-cli --cluster create host.docker.internal:6379 host.docker.internal:6380 host.docker.internal:6381 host.docker.internal:6382 host.docker.internal:6383 host.docker.internal:6384 --cluster-replicas 1 --cluster-yes
    extra_hosts:
      - "host.docker.internal:host-gateway"
    depends_on:
      redis-node1:
        condition: service_healthy
      redis-node2:
        condition: service_healthy
      redis-node3:
        condition: service_healthy
      redis-node4:
        condition: service_healthy
      redis-node5:
        condition: service_healthy
      redis-node6:
        condition: service_healthy

  redis-insight:
    image: redis/redisinsight:latest
    ports:
      - "5540:5540"
    networks:
      - redis-cluster-net
    extra_hosts:
      - "host.docker.internal:host-gateway"
    depends_on:
      - redis-cluster-init

  xplain_api_valuation:
    image: 575153581909.dkr.ecr.eu-west-2.amazonaws.com/solum-xplain-valuation:main
    container_name: xplain_api_valuation
    networks:
      - redis-cluster-net
    extra_hosts:
      - "host.docker.internal:host-gateway"
    depends_on:
      - redis-cluster-init
    environment:
      SPRING_PROFILES_ACTIVE: dev,redis
      SPRING_AUTOCONFIGURE_EXCLUDE: org.springframework.boot.autoconfigure.hazelcast.HazelcastAutoConfiguration
      SPRING_DATA_REDIS_CLUSTER_NODES: redis-node1:6379,redis-node2:6380,redis-node3:6381,redis-node4:6382,redis-node5:6383,redis-node6:6384
      SPRING_DATA_REDIS_CLUSTER_MAX-REDIRECTS: 3
      SPRING_DATA_REDIS_CLIENT_NAME: xplain-valuation
    profiles:
      - valuation


  xplain_setup:
    image: 575153581909.dkr.ecr.eu-west-2.amazonaws.com/solum-xplain-setup:main
    container_name: xplain_setup
    ports:
      - 8083:8080
  mock_oauth:
    image: ghcr.io/navikt/mock-oauth2-server:2.2.1
    hostname: oauth2
    environment:
      PORT: 8081
      JSON_CONFIG: '{
        "interactiveLogin": false,
        "httpServer": "NettyWrapper",
        "tokenCallbacks": [
          {
            "issuerId": "default",
            "tokenExpiry": 43200,
            "requestMappings": [
              {
                "requestParam": "audience",
                "match": "api",
                "claims": {
                  "azp": "DEBUGGER",
                  "sub": "DevUser",
                  "gty" : "client-credentials",
                  "xplain/roles-teams" : ["ROLE_ADMIN", "TEAM_DEFAULT"],
                  "xplain/username" : "DevUserName"
                }
              }
            ]
          }
        ]
      }'
    ports:
      - 8081:8081
volumes:
  solum_mongo_data:
    driver: local
networks:
  redis-cluster-net:
    driver: bridge
