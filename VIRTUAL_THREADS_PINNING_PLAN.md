# Virtual Thread Pinning Resolution Plan

## Executive Summary
Your VirtualThreadWorkflowEngine faces thread pinning issues primarily from Caffeine cache operations and ConcurrentHashMap usage. This plan focuses on the critical components actually in use and provides warnings for problematic features.

## Current Pinning Issues Identified

### Critical Issues (Active Components)
1. **Caffeine Cache Pinning**: `CaffeineRedisBackedSpringCache` uses ConcurrentHashMap internally, causing pinning during `computeIfAbsent()` operations with database fetches in `WorkflowQueryService`
2. **DataModificationCommandQueue**: Synchronized `flush()` method causes pinning during database I/O operations
3. **WorkflowDataCacheService**: ConcurrentHashMap for cache management with `computeIfAbsent()` pattern

**Note**: DataModificationCommandQueue's `bulkOpsCache` ConcurrentHashMap is not problematic since `computeIfAbsent()` only performs object construction via `newBulkOperations()` with no I/O or blocking operations.

### Components to Deprecate/Warn (Not Fix)
1. **ThreadSafeBulkOperationsProxy**: Mark as deprecated with virtual thread warnings (not currently used)
2. **VirtualThreadWorkflowEngine Idle Tracking**: Add warnings about synchronized blocks when idle tracking is enabled

## Research Findings

### Virtual Thread Pinning in Java 21
- Virtual threads pin to platform threads when blocking operations occur within synchronized blocks
- ConcurrentHashMap uses internal synchronized blocks, particularly problematic in `computeIfAbsent()` 
- Java 24 (available 2025) resolves these issues via JEP 491, but Java 21 requires workarounds

### Caffeine Cache Solutions
- Caffeine AsyncCache uses CompletableFuture-based operations to avoid pinning
- AsyncCache performs main work in non-pinning CompletableFuture context
- Alternative: Replace with different cache implementation (Guava, custom)

### ConcurrentHashMap Alternatives  
- HashMap + ReentrantReadWriteLock for better virtual thread compatibility
- ReentrantLock doesn't cause pinning unlike synchronized blocks
- ConcurrentSkipListMap as alternative concurrent map implementation

## Migration Strategy

### Phase 1: Cache Layer Modernization (High Priority) ✅ COMPLETED

#### 1.1 Implement AsyncCache for Caffeine and Guava ✅ COMPLETED
- [x] Modify `CaffeineRedisBackedSpringCache` to use Caffeine's AsyncCache instead of regular Cache
- [x] Update `AbstractCaffeineRedisBackedCache` to handle CompletableFuture-based operations  
- [x] Modify `WorkflowQueryService` cache calls to work with async patterns
- [x] Replace Guava cache in `SubprocessTracker.subprocessCounter()` with Caffeine AsyncCache to eliminate ~20 remaining pinning events

#### 1.2 Replace ConcurrentHashMap in Active Components ✅ COMPLETED
- [x] **WorkflowDataCacheService**: Replace `ConcurrentHashMap<Class<?>, Cache>` with HashMap + ReentrantReadWriteLock

### Phase 2: Synchronization Fixes (Medium Priority) ✅ COMPLETED

#### 2.1 DataModificationCommandQueue Modernization
- [x] Replace synchronized `flush()` method with ReentrantLock to eliminate pinning during database I/O operations
- [x] Maintain same blocking semantics: "Calling threads will be blocked if any other thread is already flushing"
- [x] Keep `bulkOpsCache` as ConcurrentHashMap (no changes needed - no blocking operations in computeIfAbsent)
- [x] Replace synchronized block in ReorderingBulkOperations with ConcurrentHashMap.compute() for lock-free update coalescing
- [x] **Result**: Reduced virtual thread pinning events from >800 to ~20 from Guava cache + ~500 from logging infrastructure (>95% improvement in application code)

#### 2.2 Logging Infrastructure Modernization
- [x] Replace synchronous logging with Log4j2 Async Logger using LMAX Disruptor
- [x] Configure async logging to eliminate ~500 logging-related pinning events
- [x] Prevent potential deadlocks from logging within synchronized blocks
- [x] Improve overall application performance through non-blocking logging

### Phase 3: Documentation and Warnings (Immediate) ✅ COMPLETED

#### 3.1 Add Virtual Thread Warnings
- [x] **ThreadSafeBulkOperationsProxy**: Add `@Deprecated` annotation and javadoc warning about synchronized methods causing virtual thread pinning
- [x] **VirtualThreadWorkflowEngine**: Update `setIdleTrackingEnabled()` javadoc to warn about synchronized block pinning when idle tracking is enabled  
- [x] Add logged warning when idle tracking is enabled: "WARNING: Idle tracking enabled with virtual threads may cause thread pinning due to synchronized blocks"

### Phase 4: Monitoring and Testing (Ongoing)

#### 4.1 Virtual Thread Pinning Detection ✅ COMPLETED
- [x] Enable JFR monitoring with `jdk.VirtualThreadPinned` events
- [x] Add system property `-Djdk.tracePinnedThreads=full` for development environments
- [x] Focus monitoring on cache operations and database flush patterns

**JFR Monitoring Setup:**
```bash
# For development with detailed pinning traces
export JAVA_OPTS="-XX:+UnlockExperimentalVMOptions -XX:+EnableJFR -XX:StartFlightRecording=duration=60s,filename=pinning.jfr -Djdk.tracePinnedThreads=full"
./gradlew bootRun

# For production monitoring (longer duration, less verbose)
export JAVA_OPTS="-XX:+UnlockExperimentalVMOptions -XX:+EnableJFR -XX:StartFlightRecording=duration=300s,filename=pinning-prod.jfr"
./gradlew bootRun
```

**Analyzing JFR Files:**
```bash
# View pinning events
jfr summary pinning.jfr
jfr print --events jdk.VirtualThreadPinned pinning.jfr

# For detailed analysis, use JDK Mission Control or VisualVM
```

**Development Environment Setup:**
Add to your IDE run configuration or shell profile:
```bash
export JAVA_OPTS="-Djdk.tracePinnedThreads=full"
```

**IntelliJ Run Configurations:**
All IntelliJ run configurations in `.run/` have been updated to include `-Djdk.tracePinnedThreads=full`:
- `ApiApplication (hazelcast).run.xml`
- `ApiApplication (redis).run.xml` 
- `ValuationApplication (hazelcast).run.xml`
- `ValuationApplication (redis).run.xml`

This will log pinning events to stderr with stack traces showing exactly where pinning occurs.

#### 4.2 Cache-Specific Performance Testing ✅ COMPLETED
- [x] Extend `WorkflowEnginePerformanceIntegrationTest` to measure cache contention
- [x] Add specific tests for high-concurrency cache access patterns
- [x] Implement comprehensive JFR-based pinning detection test
- [x] Test reliably detects >800 pinning events during 500-subprocess workflow execution
- [x] Establish baseline for before/after AsyncCache performance comparison

## Implementation Priority

### Immediate (Week 1) ✅ COMPLETED
- [x] Add deprecation warnings to ThreadSafeBulkOperationsProxy
- [x] Add virtual thread warnings to VirtualThreadWorkflowEngine idle tracking  
- [x] Enable pinning detection and monitoring

### High Priority (Week 2-3) ✅ COMPLETED
- [x] Implement AsyncCache in CaffeineRedisBackedSpringCache
- [x] Update WorkflowQueryService to handle async cache patterns
- [x] Replace ConcurrentHashMap in WorkflowDataCacheService
- [x] Replace Guava cache in SubprocessTracker with Caffeine AsyncCache

### Medium Priority (Week 4) ✅ COMPLETED  
- [x] Modernize DataModificationCommandQueue synchronization (replace synchronized flush() method)
- [x] Comprehensive testing with performance comparison

### Lower Priority (Optional)
- [x] Implement Log4j2 Async Logger with LMAX Disruptor to eliminate ~500 logging-related pinning events

## Expected Outcomes
- **Performance**: 3-10x improvement in cache-heavy workflow scenarios
- **Scalability**: Support for thousands of concurrent virtual threads without pinning  
- **Reliability**: Elimination of deadlocks from cache operations
- **Developer Experience**: Clear warnings about virtual thread incompatible features

## Risk Mitigation
- Feature flags for AsyncCache vs regular Cache implementations
- Gradual rollout starting with non-critical cache operations  
- Comprehensive integration testing with database load patterns
- Maintain backward compatibility for non-virtual thread engines

## Progress Log

### 2025-08-26
- Initial analysis completed
- Identified critical pinning issues in cache layer
- Research completed on Java 21 limitations and Java 24 solutions  
- Plan created and documented
- **Phase 3 completed**: Added deprecation warnings to ThreadSafeBulkOperationsProxy and virtual thread pinning warnings to VirtualThreadWorkflowEngine idle tracking
- **Phase 4 monitoring completed**: Added JFR and stderr-based pinning detection, updated CLAUDE.md with monitoring commands, created initial pinning detection test in WorkflowEnginePerformanceIntegrationTest, updated all IntelliJ run configurations with `-Djdk.tracePinnedThreads=full`

### 2025-08-27
- **Phase 4 enhanced**: Improved pinning detection test with JFR-based monitoring
- Fixed broken stdout capture mechanism in original test
- Replaced stdout capture with reliable JFR `VirtualThreadPinnedEventHandler` approach
- Test now detects >800 pinning events during normal 500-subprocess workflow execution
- Confirmed pinning issues in cache operations (Caffeine/ConcurrentHashMap) and DataModificationCommandQueue synchronization
- Established comprehensive baseline for before/after comparison when Phase 1 fixes are implemented
- Updated documentation in CLAUDE.md and README.md with current test status
- Test marked as `@PendingFeature` until AsyncCache and ConcurrentHashMap fixes are completed

### 2025-08-28
- **Phase 2.1 completed**: Complete DataModificationCommandQueue and ReorderingBulkOperations modernization
- Replaced synchronized `flush()` method with ReentrantLock in DataModificationCommandQueue
- Replaced synchronized block in ReorderingBulkOperations with ConcurrentHashMap.compute() for atomic update coalescing
- Maintained identical blocking semantics and thread-safety guarantees for all changes
- **Major success**: Reduced virtual thread pinning events from >800 to ~20 from Guava + ~500 from logging (>95% improvement in application code)
- Remaining ~20 pinning events come from Guava LocalCache in SubprocessTracker.subprocessCounter()
- Additional ~500 pinning events from logging infrastructure (not application business logic)
- All workflow integration tests, unit tests, and ReorderingBulkOperations tests passing - no regressions
- Pinning detection test threshold updated from 500 to 10 events
- **Complete elimination** of all synchronized blocks causing virtual thread pinning in workflow code
- Configured asychronous logging using LMAX Disruptor
- **Major success**: ~500 logging-related pinning events
- Only a handful of pinning events remain from Guava cache in SubprocessTracker.

### 2025-08-29
- **Phase 1.2 completed**: WorkflowDataCacheService ConcurrentHashMap modernization
- Replaced `ConcurrentHashMap<Class<?>, Cache>` with HashMap + ReentrantReadWriteLock in WorkflowDataCacheService
- Eliminated `computeIfAbsent()` pinning by implementing double-checked locking pattern with read/write locks
- Fast path uses read lock for cache lookups, write lock only for cache creation
- All unit tests passing, maintaining identical thread-safety guarantees and functionality
- Virtual thread pinning eliminated from WorkflowDataCacheService cache operations
- **Phase 1.2 achievement**: Further reduction in virtual thread pinning from cache layer modernization

### 2025-09-03
- **Phase 1.1 completed**: AbstractCaffeineRedisBackedCache AsyncCache modernization
- Updated `AbstractCaffeineRedisBackedCache` to use Caffeine's AsyncCache for all cache operations
- Eliminated synchronized blocks in cache layer that were causing virtual thread pinning
- Modified core methods (`layeredGet`, `layeredPut`, `layeredPutIfAbsent`) to use CompletableFuture-based async operations
- Maintained identical caching semantics and thread-safety guarantees
- **Complete elimination** of Caffeine-related virtual thread pinning in Redis-backed cache implementations

### 2025-09-04
- **AsyncCache ClassLoader Fix completed**: Implemented shared context-preserving ForkJoinPool for all AsyncCache instances
- Added `AsyncUtils.CONTEXT_PRESERVING_COMMON_POOL` and `AsyncUtils.newCaffeineBuilder()` utility
- Updated all 12+ AsyncCache instances across the codebase to use context-preserving executor
- **Result**: All AsyncCache operations now have proper Spring Boot classloader access for fat JAR deployments

---

*This document will be updated as we progress through the implementation phases.*
