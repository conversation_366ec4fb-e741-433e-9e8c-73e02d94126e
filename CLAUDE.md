# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

### Building the Application
- `./gradlew clean build` - Clean and build all modules
- `./gradlew clean bootRun` - Run the main API application (requires Docker services)
- `./gradlew clean bootRun -x test` - Run without executing tests

### Virtual Thread Monitoring (Java 21)
For debugging virtual thread pinning issues with VirtualThreadWorkflowEngine:
- `JAVA_OPTS="-Djdk.tracePinnedThreads=full" ./gradlew bootRun` - Enable pinning detection with stack traces
- `JAVA_OPTS="-XX:+UnlockExperimentalVMOptions -XX:+EnableJFR -XX:StartFlightRecording=duration=60s,filename=pinning.jfr -Djdk.tracePinnedThreads=full" ./gradlew bootRun` - Full JFR monitoring
- `jfr print --events jdk.VirtualThreadPinned pinning.jfr` - Analyze JFR pinning events

**Pinning Detection Test:**
- `./gradlew :xplain-api:workflow:integrationTest --tests "*should detect virtual thread pinning*"` - Run baseline pinning detection test
- Test uses JFR-based monitoring to detect pinning events during normal 500-subprocess workflow execution
- Currently marked as `@PendingFeature` until pinning fixes are implemented (see VIRTUAL_THREADS_PINNING_PLAN.md)
- Reliably detects >800 pinning events during workflow execution, confirming cache-related issues

### Testing
- `./gradlew test` - Run unit tests
- `./gradlew integrationTest` - Run integration tests  
- `./gradlew check` - Run all tests and code quality checks
- `./gradlew test --tests "*.SpecificTest"` - Run specific test
- `./gradlew jacocoTestReport` - Generate test coverage report

### Code Quality
- `./gradlew spotlessCheck` - Check code formatting
- `./gradlew spotlessApply` - Apply code formatting
- `./gradlew dependencyCheck` - OWASP dependency vulnerability check

### Docker Services
Start required third-party services before running the application:
- `docker-compose up` - Start MongoDB, Kafka, Hazelcast, UI
- `docker-compose --profile valuation up` - Include valuation service
We- Ensure `/etc/hosts` contains: `127.0.0.1 oauth2`

## Architecture Overview

This is a multi-module Spring Boot application for financial valuation and risk management (Solum Xplain).

### Main Application Modules
- **xplain-api/app** - Main Spring Boot application entry point (`ApiApplication.java`)
- **xplain-api/core** - Core domain models and shared functionality
- **xplain-api/calculation** - Valuation calculations and portfolio processing
- **xplain-api/calibration** - Curve calibration and market data processing
- **xplain-api/workflow** - Business workflow orchestration
- **xplain-api/xm** - Exception management and IPV (Independent Price Verification)
- **xplain-api/secmaster** - Securities master data integration
- **xplain-api/xva** - XVA (Credit/Funding/Capital valuation adjustments)

### Shared Libraries
- **shared/data-grid** - Hazelcast/Redis distributed caching and cluster coordination
- **shared/spring-mongo** - MongoDB integration extensions and aggregation utilities
- **shared/strata-extension** - Extensions for OpenGamma Strata quantitative finance library
- **shared/utils** - Common utilities, authentication, and error handling

### Additional Applications
- **xplain-valuation** - Standalone valuation service
- **xplain-xva** - XVA calculation service

### Technology Stack
- **Java 21** with Spring Boot 3.4.4
- **MongoDB** for data persistence
- **Hazelcast/Redis** for distributed caching
- **Apache Kafka** for messaging
- **OpenGamma Strata** for quantitative finance
- **Gradle** build system with custom multi-module setup
- **Docker Compose** for local development environment

### Key Configuration
- Application properties in `xplain-api/app/src/main/resources/application*.yml`
- Environment-specific profiles: local, dev, qa, staging, beta, demo
- Authentication: OAuth2 resource server
- Logging: Log4j2 with JSON structured logging

### Development Notes
- Requires `GITHUB_TOKEN` environment variable for accessing private Maven packages
- Uses Lombok for boilerplate code reduction
- MapStruct for object mapping
- Spock framework for testing (Groovy-based)
- Code formatting enforced with Spotless (Google Java Format)
- OWASP dependency checking enabled
