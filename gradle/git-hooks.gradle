
tasks.register('installGitLfs') {
  description = 'Install and configure Git LFS'

  doLast {
    // Check if git-lfs is installed
    try {
      providers.exec {
        commandLine('git', 'lfs', 'version')
      }.result.get()
      println "Git LFS is available"
    } catch (Exception ignored) {
      throw new GradleException("Git LFS is not installed. Please install git-lfs first:\n" +
        "  Ubuntu/Debian: sudo apt-get install git-lfs\n" +
        "  macOS: brew install git-lfs\n" +
        "  Windows: Download from https://git-lfs.github.io/")
    }

    // Install all LFS hooks with --force to overwrite any existing hooks
    providers.exec {
      commandLine('git', 'lfs', 'install', '--force')
    }.result.get()

    println "Git LFS hooks installed"
  }
}

tasks.register('installCustomHooks') {
  dependsOn 'installGitLfs'
  description = 'Install custom hooks (overwriting LFS hooks where we have custom behavior)'

  doLast {
    def hooksDir = new File(projectDir, '.git/hooks')

    // Install our custom hooks (these will overwrite the LFS-installed versions)
    def customHooks = ['pre-push', 'pre-commit', 'commit-msg']
    customHooks.each { hookName ->
      def sourceFile = new File(projectDir, "scripts/${hookName}")
      def targetFile = new File(hooksDir, hookName)

      if (sourceFile.exists()) {
        // Copy our custom hook (overwriting the LFS version)
        targetFile.text = sourceFile.text
        targetFile.setExecutable(true)
        println "${hookName} custom hook installed (overwriting LFS version)"
      }
    }
  }
}

// Keep these for backward compatibility, but make them depend on the unified task
tasks.register('installGitHook') {
  dependsOn 'installCustomHooks'
  description = 'Install pre-push hook (legacy task name)'
}

tasks.register('installPreCommitHook') {
  dependsOn 'installCustomHooks'
  description = 'Install pre-commit hook (legacy task name)'
}

tasks.named('prepareKotlinBuildScriptModel') {
  dependsOn 'installCustomHooks'
}