package com.solum.xplain.extensions;

import com.opengamma.strata.pricer.fxopt.BlackFxOptionVolatilities;
import com.opengamma.strata.pricer.fxopt.InterpolatedStrikeSmileDeltaTermStructure;
import com.solum.xplain.core.utils.proxy.ProxyUtils;
import com.solum.xplain.shared.datagrid.impl.redis.ForySerializationConfigurer;
import org.jspecify.annotations.NullMarked;
import org.springframework.beans.factory.BeanFactory;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.AutoConfigurationExcludeFilter;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.context.TypeExcludeFilter;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.ComponentScan.Filter;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.FilterType;

@AutoConfiguration
@ComponentScan(
    excludeFilters = {
      @Filter(type = FilterType.CUSTOM, classes = TypeExcludeFilter.class),
      @Filter(type = FilterType.CUSTOM, classes = AutoConfigurationExcludeFilter.class)
    })
public class StrataExtensionsAutoConfiguration {

  public StrataExtensionsAutoConfiguration(BeanFactory beanFactory) {
    ProxyUtils.setBeanResolver(beanFactory::getBean);
  }

  /**
   * Configuration which ensures that if Fory serialization is available then it is configured to
   * register data types which are known to contain transient fields.
   */
  @Configuration
  @ConditionalOnClass(ForySerializationConfigurer.class)
  @NullMarked
  static class StrataForySerializationConfiguration {
    @Bean
    ForySerializationConfigurer strataForySerializationConfigurer() {
      return config -> {
        config.register(InterpolatedStrikeSmileDeltaTermStructure.class);
        config.register(BlackFxOptionVolatilities.class);
      };
    }
  }
}
