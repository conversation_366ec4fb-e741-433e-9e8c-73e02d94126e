plugins {
  id "java-test-fixtures"
}

configurations {
  all*.exclude module: 'spring-boot-starter-logging'
}

dependencies {
  compileOnly "com.hazelcast:hazelcast-spring:${hazelcastVersion}"
  compileOnly "org.springframework.boot:spring-boot-starter-cache"
  compileOnly 'javax.cache:cache-api'
  implementation "org.springframework.retry:spring-retry"
  implementation "org.apache.fory:fory-core:${foryVersion}"
  implementation "org.springframework.data:spring-data-redis"
  implementation "io.lettuce:lettuce-core"
  implementation "com.github.ben-manes.caffeine:caffeine"
  implementation project(":shared:spring-async")

  testImplementation "org.spockframework:spock-core:${spockVersion}"
  testImplementation "com.hazelcast:hazelcast-spring:${hazelcastVersion}"

  integrationTestImplementation(testFixtures(project(":shared:data-grid")))
  integrationTestImplementation 'org.springframework.boot:spring-boot-starter-validation'
  integrationTestImplementation 'org.springframework.boot:spring-boot-starter-test'
  integrationTestImplementation "org.springframework.boot:spring-boot-starter-cache"
  integrationTestImplementation "org.springframework.data:spring-data-redis"
  integrationTestImplementation "com.hazelcast:hazelcast-spring:${hazelcastVersion}"
  integrationTestImplementation 'javax.cache:cache-api'
  integrationTestImplementation "io.lettuce:lettuce-core"
  integrationTestImplementation "com.redis:testcontainers-redis:2.2.4"

  testFixturesImplementation "org.apache.groovy:groovy:${groovyJsonVersion}"
  testFixturesImplementation 'org.springframework.boot:spring-boot-starter-test'
  testFixturesImplementation "com.redis:testcontainers-redis:2.2.4"
  testFixturesImplementation "org.spockframework:spock-core:${spockVersion}"

  annotationProcessor "org.projectlombok:lombok:${lombokVersion}"
}

test {
  useJUnitPlatform()
}
