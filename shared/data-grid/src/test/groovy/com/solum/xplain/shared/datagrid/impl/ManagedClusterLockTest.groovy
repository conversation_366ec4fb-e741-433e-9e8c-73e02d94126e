package com.solum.xplain.shared.datagrid.impl

import com.solum.xplain.shared.datagrid.ClusterLock
import com.solum.xplain.shared.datagrid.LockManager
import com.solum.xplain.shared.datagrid.ManagedClusterLock
import com.solum.xplain.shared.datagrid.RenewableClusterLock
import java.util.concurrent.TimeUnit
import spock.lang.Specification

class ManagedClusterLockTest extends Specification {

  def "tryLock() should delegate to LockManager.acquireWithHeartbeat()"() {
    given:
    RenewableClusterLock lock = Mock()
    LockManager<RenewableClusterLock> lockManager = Mock()
    def managedClusterLock = new ManagedClusterLock<>(lock, lockManager)

    when:
    managedClusterLock.tryLock()

    then:
    1 * lockManager.acquireWithHeartbeat(lock)
  }

  def "tryLock(time, unit) should delegate to LockManager.acquireWithHeartbeat with timeout"() {
    given:
    RenewableClusterLock lock = Mock()
    LockManager<RenewableClusterLock> lockManager = Mock()
    def managedClusterLock = new ManagedClusterLock<>(lock, lockManager)

    when:
    managedClusterLock.tryLock(1, TimeUnit.SECONDS)

    then:
    1 * lockManager.acquireWithHeartbeat(lock, 1, TimeUnit.SECONDS)
  }

  def "lock() should delegate to lock and start heartbeat"() {
    given:
    RenewableClusterLock lock = Mock()
    LockManager<RenewableClusterLock> lockManager = Mock()
    def managedClusterLock = new ManagedClusterLock<>(lock, lockManager)

    when:
    managedClusterLock.lock()

    then:
    1 * lock.lock()
    1 * lockManager.startHeartbeat(lock)
  }

  def "lock() should unlock and rethrow if startHeartbeat fails"() {
    given:
    RenewableClusterLock lock = Mock()
    LockManager<RenewableClusterLock> lockManager = Mock()
    def managedClusterLock = new ManagedClusterLock<>(lock, lockManager)

    when:
    managedClusterLock.lock()

    then:
    1 * lock.lock()
    1 * lockManager.startHeartbeat(lock) >> { throw new Exception("heartbeat failed") }
    1 * lock.unlock()
    thrown(RuntimeException)
  }

  def "lock() fails to unlock after startHeartbeat fails"() {
    given:
    RenewableClusterLock lock = Mock()
    LockManager<RenewableClusterLock> lockManager = Mock()
    def managedClusterLock = new ManagedClusterLock<>(lock, lockManager)

    when:
    managedClusterLock.lock()

    then:
    1 * lock.lock()
    1 * lockManager.startHeartbeat(lock) >> { throw new Exception("heartbeat failed") }
    1 * lock.unlock() >> { throw new Exception("unlock failed") }
    thrown(RuntimeException)
  }

  def "lockInterruptibly() should delegate to lockInterruptibly and start heartbeat"() {
    given:
    RenewableClusterLock lock = Mock()
    LockManager<RenewableClusterLock> lockManager = Mock()
    def managedClusterLock = new ManagedClusterLock<>(lock, lockManager)

    when:
    managedClusterLock.lockInterruptibly()

    then:
    1 * lock.lockInterruptibly()
    1 * lockManager.startHeartbeat(lock)
  }

  def "unlock() should delegate to LockManager.release()"() {
    given:
    RenewableClusterLock lock = Mock()
    LockManager<RenewableClusterLock> lockManager = Mock()
    def managedClusterLock = new ManagedClusterLock<>(lock, lockManager)

    when:
    managedClusterLock.unlock()

    then:
    1 * lockManager.release(lock)
  }
}
