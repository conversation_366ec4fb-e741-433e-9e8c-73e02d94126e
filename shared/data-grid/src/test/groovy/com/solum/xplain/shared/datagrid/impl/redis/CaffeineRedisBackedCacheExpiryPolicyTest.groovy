package com.solum.xplain.shared.datagrid.impl.redis

import com.solum.xplain.shared.datagrid.LocalCacheReplica
import java.time.Duration
import spock.lang.Specification

class CaffeineRedisBackedCacheExpiryPolicyTest extends Specification {
  def expiryPolicy = new CaffeineRedisBackedCacheExpiryPolicy(new LocalCacheReplica(
  "testCache",
  100,
  Duration.ofSeconds(10), // default TTL
  Duration.ofSeconds(5), // idle TTL
  true
  ), Collections.emptyMap())

  def "create, access, update, access, access should return correct expiries"() {
    long creationTime = 567_000_333_333_333L // Arbitrary ticker in ns
    long accessTime1 = creationTime + 1_000_000_000 // 1 second later
    long updateTime = accessTime1 + 2_000_000_000 // 2 seconds after access
    long accessTime2 = updateTime + 4_000_000_000 // 4 seconds after update
    long accessTime3 = accessTime2 + 10_000_000_000 // 10 seconds after access

    expect: "An entry is created, the duration is the idle ttl"
    expiryPolicy.expireAfterCreate("key1", "value1", creationTime) == 5_000_000_000L

    and: "When the entry is accessed, the duration is the default ttl, reduced by the time since creation"
    expiryPolicy.expireAfterRead("key1", "value1", accessTime1, 4_000_000_000L) == 9_000_000_000L

    and: "When the entry is updated, the duration is back to the idle ttl until it is read"
    expiryPolicy.expireAfterUpdate("key1", "value1", updateTime, 7_000_000_000L) == 5_000_000_000L

    and: "When the entry is accessed afer update, the duration is the default ttl, reduced by the time since update"
    expiryPolicy.expireAfterRead("key1", "value1", accessTime2, 1_000_000_000L) == 6_000_000_000L

    and: "When the entry is accessed again after it will have expired, the duration is 0"
    expiryPolicy.expireAfterRead("key1", "value1", accessTime3, 0L) == 0L
  }

  def "should use default ttl if less than idle ttl"() {
    def replica = new LocalCacheReplica(
      "testCache",
      100,
      Duration.ofSeconds(3), // default TTL
      Duration.ofSeconds(5), // idle TTL
      true
      )
    def expiryPolicy = new CaffeineRedisBackedCacheExpiryPolicy(replica, Collections.emptyMap())

    expect: "The expiry time should be the default TTL"
    expiryPolicy.expireAfterCreate("key1", "value1", 0) == 3_000_000_000L
  }

  def "should treat zero default ttl as no expiry on creation"() {
    def replica = new LocalCacheReplica(
      "testCache",
      100,
      Duration.ofSeconds(defaultTtl),
      Duration.ofSeconds(idleTtl),
      true
      )
    def expiryPolicy = new CaffeineRedisBackedCacheExpiryPolicy(replica, Collections.emptyMap())

    expect: "The expiry time should be zero, indicating no expiry"
    expiryPolicy.expireAfterCreate("key1", "value1", 0) == expected

    where:
    defaultTtl | idleTtl | expected
    0          | 5       | 5_000_000_000L
    5          | 0       | 5_000_000_000L
    0          | 0       | Long.MAX_VALUE
  }

  def "should treat zero default ttl as no expiry after access"() {
    def replica = new LocalCacheReplica(
      "testCache",
      100,
      Duration.ZERO,
      Duration.ofSeconds(3),
      true
      )
    def expiryPolicy = new CaffeineRedisBackedCacheExpiryPolicy(replica, Collections.emptyMap())

    expect: "The expiry time should be zero, indicating no expiry after access"
    expiryPolicy.expireAfterRead("key1", "value1", 0, 2_000_000_000L) == Long.MAX_VALUE
  }
}
