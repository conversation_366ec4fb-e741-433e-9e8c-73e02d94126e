package com.solum.xplain.shared.datagrid.impl.redis

import org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory
import org.springframework.data.redis.core.BoundHashOperations
import org.springframework.data.redis.core.RedisTemplate
import org.springframework.data.redis.core.SessionCallback
import spock.lang.Specification

class RedisKeyValueCacheTest extends Specification {

  def "should apply h_ prefix to cache name in constructor"() {
    given: "A mock RedisTemplate and BoundHashOperations"
    def redisTemplate = Mock(RedisTemplate)
    def boundHashOps = Mock(BoundHashOperations)
    def cacheName = "testCache"

    when: "Creating a RedisKeyValueCache"
    redisTemplate.boundHashOps("h_" + cacheName) >> boundHashOps
    def cache = new RedisKeyValueCache<String, String>(redisTemplate, cacheName)

    then: "The RedisTemplate should be called with the prefixed name"
    1 * redisTemplate.boundHashOps("h_testCache")
  }

  def "should support clustered and non-clustered Redis"(clustered) {
    given: "A mock RedisTemplate that has a LettuceConnectionFactory where isClusterAware() = ${clustered}"
    def redisTemplate = Mock(RedisTemplate)
    def boundHashOps = Mock(BoundHashOperations)
    redisTemplate.boundHashOps(_) >> boundHashOps
    boundHashOps.operations >> redisTemplate
    boundHashOps.getKey() >> "h_testCache"

    def lettuceConnectionFactory = Mock(LettuceConnectionFactory)
    redisTemplate.getConnectionFactory() >> lettuceConnectionFactory
    lettuceConnectionFactory.isClusterAware() >> clustered


    when: "Creating a RedisKeyValueCache"
    def cache = new RedisKeyValueCache<String, String>(redisTemplate, "testCache")

    then: "The cache should be created successfully"
    noExceptionThrown()

    when: "Calling merge()"
    cache.merge("key", "value", (k, value) -> "value")

    then: "expected calls should be appropriate for a clustered Redis"
    1 * redisTemplate.execute(_ as SessionCallback) >> {
      SessionCallback callback ->
      return callback.execute(redisTemplate)
    }
    (clustered ? 0 : 1) * redisTemplate.watch(_ as Object)
    1 * boundHashOps.get("key") >> "prevValue"
    (clustered ? 0 : 1) * redisTemplate.multi()
    (clustered ? 0 : 1) * redisTemplate.exec()
    _ * boundHashOps.put("key", "value")
    _ * boundHashOps.delete("key")

    where:
    clustered << [true, false]
  }
}
