package com.solum.xplain.shared.datagrid.impl.redis

import com.solum.xplain.shared.datagrid.LocalCacheReplica
import java.time.Duration
import java.util.concurrent.Callable
import org.springframework.cache.Cache.ValueRetrievalException
import org.springframework.cache.support.SimpleValueWrapper
import org.springframework.data.redis.cache.RedisCache
import spock.lang.Specification
import spock.util.concurrent.PollingConditions

class CaffeineRedisBackedSpringCacheTest extends Specification {

  def publisher = Mock(RedisCacheMessagePublisher)
  def defaultTtl = Duration.ofSeconds(4)
  def idleTtl = Duration.ofSeconds(8)
  def config = new LocalCacheReplica("testCache", 100, defaultTtl, idleTtl, true)
  def redisCache = Mock(RedisCache)
  def cache = new CaffeineRedisBackedSpringCache(config, redisCache, publisher)
  def key = "testKey"
  def value = "testValue"

  def "should expire entries after defaultTtl"() {
    when: "We put the entry without a customTtl"
    cache.put(key, value)

    then: "The entry should be written to the cache"
    cache.get(key).get() == value

    def pollingConditions = new PollingConditions(timeout: defaultTtl.toMillis(), delay: 0.1)

    and: "The entry should expire after the defaultTtl"
    pollingConditions.eventually {
      cache.get(key) == null
    }
  }

  def "should expire entries after idleTtl when accessed"() {
    when: "A key with no customTtl"
    cache.put(key, value)

    def pollingConditions = new PollingConditions(timeout: idleTtl.toMillis(), delay: 0.1)

    then: "The entry should expire after idleTtl when not accessed"
    pollingConditions.eventually {
      cache.get(key) == null
    }
  }

  def "get() should read from backend when not in local cache"() {
    given: "A key that is not in the local cache but exists in the backend"
    def noValueKey = "testKey1"
    redisCache.get(noValueKey) >> new SimpleValueWrapper(value)

    when: "We try to get the entry"
    def result = cache.get(noValueKey)

    then: "The entry should be fetched from the backend and stored locally"
    result.get() == value
  }

  def "get() should use valueLoader to assign new value when not in local cache or backend"() {
    given: "A key that is not in the local cache or backend"
    def newValue = "newValue"
    def valueLoader = { _ -> return newValue }
    1 * redisCache.get(key, _ as Callable) >> { it[1].call() }

    when: "We try to get the entry with a valueLoader"
    def result = cache.get(key, valueLoader)

    then: "The valueLoader should be used and the result stored in both caches"
    result == newValue
    cache.get(key).get() == newValue
    1 * publisher.publishInvalidateKeyMessage(config.name(), key, cache.sourceId)
  }

  def "get() should use valueLoader to assign new value when not in local cache or backend and evict entry in local cache but not shared when autoUpdate is false"() {
    given: "A cache with autoUpdate disabled"
    def nonAutoUpdateConfig = new LocalCacheReplica("nonAutoUpdateCache", 100, defaultTtl, idleTtl, false)
    def nonAutoUpdateCache = new CaffeineRedisBackedSpringCache(nonAutoUpdateConfig, redisCache, publisher)
    def sourceId = nonAutoUpdateCache.getSourceId()
    def newValue = "newValue"
    def valueLoader = { _ -> return newValue }
    1 * redisCache.get(key, _ as Callable) >> { it[1].call() }

    when: "We try to get the entry with a valueLoader"
    def result = nonAutoUpdateCache.get(key, valueLoader)

    then: "The valueLoader should be used and the result stored in shared cache only"
    result == newValue
    nonAutoUpdateCache.get(key) == null
    1 * publisher.publishInvalidateKeyMessage("nonAutoUpdateCache", key, sourceId)
  }


  def "get() should return null when valueLoader returns null and key is not in local cache or backend"() {
    given: "A key that is not in the local cache or backend"
    def valueLoader = { _ -> return null }

    when: "We try to get the entry with a valueLoader"
    redisCache.get(key, _ as Callable) >> { it[1].call() }
    def result = cache.get(key, valueLoader)

    then: "The result should be null"
    result == null
  }

  def "get() should throw exception when valueLoader throws exception and key is not in local cache or backend"() {
    given: "A key that is not in the local cache or backend"
    def valueLoader = { _ -> throw new ValueRetrievalException(key, null, new Exception("testException")) }
    redisCache.get(key, _ as Callable) >> { it[1].call() }

    when: "We try to get the entry with a valueLoader"
    cache.get(key, valueLoader)

    then: "The result should be null"
    thrown(ValueRetrievalException)
  }

  def "evict() should evict entries from both caches"() {
    given: "A key in both caches"
    def sourceId = cache.getSourceId()
    cache.put(key, value)

    when: "We evict the entry"
    assert cache.get(key).get() == value
    cache.evict(key)

    then: "The entry should be removed from both caches"
    1 * redisCache.evict(key)
    1 * publisher.publishInvalidateKeyMessage(config.name(), key, sourceId)
    cache.get(key) == null
  }

  def "clear() should clear both caches"() {
    given: "Multiple entries in the cache"
    def sourceId = cache.getSourceId()
    cache.put("key1", "value1")
    cache.put("key2", "value2")

    when: "We clear the cache"
    cache.clear()

    then: "the cache should be cleared"
    1 * redisCache.clear()
    1 * publisher.publishInvalidateAllMessage(config.name(), sourceId)
    cache.get("key1") == null
    cache.get("key2") == null
  }

  def "put() should evict entry in local cache but not shared when autoUpdate is false"() {
    given: "A cache with autoUpdate disabled"
    def nonAutoUpdateConfig = new LocalCacheReplica("nonAutoUpdateCache", 100, defaultTtl, idleTtl, false)
    def nonAutoUpdateCache = new CaffeineRedisBackedSpringCache(nonAutoUpdateConfig, redisCache, publisher)
    def sourceId = nonAutoUpdateCache.getSourceId()

    when: "We put an entry"
    nonAutoUpdateCache.put(key, value)

    then: "The entry should be written to the backend but evicted locally"
    1 * redisCache.put(key, value)
    1 * publisher.publishInvalidateKeyMessage("nonAutoUpdateCache", key, sourceId)
    nonAutoUpdateCache.get(key) == null
  }

  def "put() should reset expiration time when entry is accessed"() {
    given: "A key"
    cache.put(key, value) // Will have default TTL of 8 seconds, since this is lower than the idle TTL.

    when: "We access the entry repeatedly, keeping it alive"
    cache.get(key) // Will still have default TTL minus a small amount of time.
    Thread.sleep(defaultTtl.toMillis() - 500)

    then: "The entry should still be available"
    cache.get(key) != null // Total elapsed time is now approximately (defaultTtl - 500), which is < defaultTtl

    when: "We access the entry again after some time"
    Thread.sleep(1000) // Now we should have exceeded the default TTL

    then: "The entry should no longer be available"
    cache.get(key) == null
  }

  def "get() should return value from local cache with type if present"() {
    def type = String
    cache.put(key, value)

    when:
    def result = cache.get(key, type)

    then:
    result == value
    0 * redisCache.get(key, type)
  }

  def "get() should fetch from backend with type if not in local cache"() {
    given:
    def type = String
    1 * redisCache.get(key) >> new SimpleValueWrapper(value)

    when:
    def result = cache.get(key, type)

    then:
    result == value
  }

  def "get() should throw if value is wrong type"() {
    given:
    def type = Integer
    cache.put(key, value)
    redisCache.get(key) >> value

    when:
    cache.get(key, type)

    then:
    thrown(IllegalStateException)
  }

  def "putIfAbsent() should put value if absent in local and shared cache and return null"() {
    given:
    redisCache.get(key) >> null

    when:
    def result = cache.putIfAbsent(key, value)

    then:
    result == null
    1 * redisCache.get(key)
    1 * redisCache.put(key, value)
  }

  def "putIfAbsent() should return value if present in cache"() {
    given:
    cache.put(key, value)

    when:
    def result = cache.putIfAbsent(key, value)

    then:
    result.get() == value
  }

  def "evictIfPresent() should evict from both caches"() {
    given:
    cache.put(key, value)

    when:
    def result = cache.evictIfPresent(key)

    then:
    !result
  }

  def "clear() should clear both caches and publish invalidation"() {
    given:
    def sourceId = cache.getSourceId()
    cache.put(key, value)

    when:
    cache.clear()

    then:
    1 * redisCache.clear()
    1 * publisher.publishInvalidateAllMessage(config.name(), sourceId)
    cache.get(key) == null
  }

  def "invalidate() should invalidate local cache"() {
    given:
    cache.put(key, value)

    when:
    def result = cache.invalidate()

    then:
    !result
  }

  def 'localCacheOnly should clear local cache'() {
    given:
    cache.put(key, value)

    when:
    cache.clearLocalCacheOnly()

    then:
    cache.get(key) == null
  }
}
