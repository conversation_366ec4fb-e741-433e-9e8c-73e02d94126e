package com.solum.xplain.shared.datagrid.impl.redis


import com.solum.xplain.shared.datagrid.LocalCacheReplica
import java.time.Duration
import java.util.concurrent.ConcurrentHashMap
import org.springframework.test.util.ReflectionTestUtils
import spock.lang.Specification
import spock.util.concurrent.PollingConditions

class CaffeineRedisBackedKeyValueCacheTest extends Specification {
  def publisher = Mock(RedisCacheMessagePublisher)
  def defaultTtl = Duration.ofSeconds(4)
  def idleTtl = Duration.ofSeconds(8)
  def config = new LocalCacheReplica("testCache", 100, defaultTtl, idleTtl, true)
  def keyValueCache = Mock(RedisKeyValueCache)
  def cache = new CaffeineRedisBackedKeyValueCache(config, keyValueCache, publisher)
  def key = "testKey"
  def value = "testValue"

  //// These tests are for the equivalent behaviour to CaffeineRedisBackedSpringCache ////

  def "get() should read from backend when not in local cache"() {
    given: "A key that is not in the local cache but exists in the backend"
    def noValueKey = "testKey1"
    keyValueCache.get(noValueKey) >> value

    when: "We try to get the entry"
    def result = cache.get(noValueKey)

    then: "The entry should be fetched from the backend and stored locally"
    result == value
  }

  def "delete() should evict entries from both caches"() {
    given: "A key in both caches"
    def sourceId = cache.getSourceId()
    cache.set(key, value)

    when: "We evict the entry"
    assert cache.get(key) == value
    cache.delete(key)

    then: "The entry should be removed from both caches"
    1 * keyValueCache.delete(key)
    1 * publisher.publishInvalidateKeyMessage(config.name(), key, sourceId)
    cache.get(key) == null
  }

  def "set() should evict entry in local cache but not shared when autoUpdate is false"() {
    given: "A cache with autoUpdate disabled"
    def nonAutoUpdateConfig = new LocalCacheReplica("nonAutoUpdateCache", 100, defaultTtl, idleTtl, false)
    def nonAutoUpdateCache = new CaffeineRedisBackedKeyValueCache(nonAutoUpdateConfig, keyValueCache, publisher)
    def sourceId = nonAutoUpdateCache.getSourceId()

    when: "We put an entry"
    nonAutoUpdateCache.set(key, value)

    then: "The entry should be written to the backend but evicted locally"
    1 * keyValueCache.set(key, value)
    1 * publisher.publishInvalidateKeyMessage("nonAutoUpdateCache", key, sourceId)
    nonAutoUpdateCache.get(key) == null
  }

  def "set() should reset expiration time when entry is accessed"() {
    given: "A key"
    cache.set(key, value) // Will have default TTL of 8 seconds, since this is lower than the idle TTL.

    when: "We access the entry repeatedly, keeping it alive"
    cache.get(key) // Will still have default TTL minus a small amount of time.
    Thread.sleep(defaultTtl.toMillis() - 500)

    then: "The entry should still be available"
    cache.get(key) != null // Total elapsed time is now approximately (defaultTtl - 500), which is < defaultTtl

    when: "We access the entry again after some time"
    Thread.sleep(1000) // Now we should have exceeded the default TTL

    then: "The entry should no longer be available"
    cache.get(key) == null
  }

  def 'localCacheOnly should clear local cache'() {
    given:
    cache.set(key, value)

    when:
    cache.clearLocalCacheOnly()

    then:
    cache.get(key) == null
  }

  //// Tests below here are specific to the CaffeineRedisBackedKeyValueCache ////

  def "set() should evict entry in local cache but not shared when autoUpdate is true with Ttl"() {
    given: "A cache with autoUpdate disabled"
    def nonAutoUpdateConfig = new LocalCacheReplica("nonAutoUpdateCache", 100, defaultTtl, idleTtl, true)
    def nonAutoUpdateCache = new CaffeineRedisBackedKeyValueCache(nonAutoUpdateConfig, keyValueCache, publisher)
    def expiryPolicy = ReflectionTestUtils.getField(nonAutoUpdateCache, "expiryPolicy") as CaffeineRedisBackedCacheExpiryPolicy
    def customTtls = ReflectionTestUtils.getField(expiryPolicy, "customTtls") as ConcurrentHashMap<Object, Duration>
    def sourceId = nonAutoUpdateCache.getSourceId()

    when: "We put an entry"
    nonAutoUpdateCache.set(key, value, Duration.ofSeconds(10))

    then: "The entry should be written to the backend but evicted locally"
    1 * keyValueCache.set(key, value)
    1 * keyValueCache.setKeyExpiry(key, Duration.ofSeconds(10))
    customTtls.isEmpty() // Custom TTL is longer than the default local TTL.
    1 * publisher.publishInvalidateKeyMessage("nonAutoUpdateCache", key, sourceId)
    nonAutoUpdateCache.get(key) == value
  }

  def "set() should handle a mix of customTtl and defaultTtl entries correctly"() {
    given: "Multiple keys with differentTtl configurations"
    def customTtlKey = "customTtlKey1"
    def customTtlValue = "customTtlValue1"
    def customTtlKey2 = "customTtlKey2"
    def customTtlValue2 = "customTtlValue2"
    def defaultTtlKey = "defaultTtlKey"
    def defaultTtlValue = "defaultTtlValue"
    def customTtl1 = Duration.ofSeconds(2)
    def customTtl2 = Duration.ofSeconds(3)

    when: "We put entries with and without customTtls"
    cache.set(customTtlKey, customTtlValue, customTtl1)
    cache.set(customTtlKey2, customTtlValue2, customTtl2)
    cache.set(defaultTtlKey, defaultTtlValue)

    then: "Both entries should be retrievable"
    cache.get(customTtlKey) == customTtlValue
    cache.get(customTtlKey2) == customTtlValue2
    cache.get(defaultTtlKey) == defaultTtlValue

    def customTtlTimeout1 = new PollingConditions(timeout: customTtl1.toMillis() + 500, delay: 0.1)

    and: "The customTtl entry should expire after itsTtl"
    customTtlTimeout1.eventually {
      cache.get(customTtlKey) == null
      cache.get(customTtlKey2) == customTtlValue2
      cache.get(defaultTtlKey) == defaultTtlValue
    }

    def customTtlTimeout2 = new PollingConditions(timeout: customTtl2.toMillis() + 500, delay: 0.1)

    and: "The second customTtl entry should expire after itsTtl"
    customTtlTimeout2.eventually {
      cache.get(customTtlKey) == null
      cache.get(customTtlKey2) == null
      cache.get(defaultTtlKey) == defaultTtlValue
    }

    def defaultTtlTimeout = new PollingConditions(timeout: defaultTtl.toMillis() + 500, delay: 0.1)

    and: "The defaultTtl entry should expire after the defaultTtl"
    defaultTtlTimeout.eventually {
      cache.get(customTtlKey) == null
      cache.get(customTtlKey2) == null
      cache.get(defaultTtlKey) == null
    }
  }

  def "should expire entries with a customTtl"() {
    given: "A customTtl for a specific key"
    def expiryPolicy = ReflectionTestUtils.getField(cache, "expiryPolicy") as CaffeineRedisBackedCacheExpiryPolicy
    def customTtls = ReflectionTestUtils.getField(expiryPolicy, "customTtls") as ConcurrentHashMap<Object, Duration>
    def customTtl = Duration.ofSeconds(2)

    when: "We put the entry with a customTtl"
    cache.set(key, value, customTtl)

    then: "The entry should be written to the cache"
    customTtls.get(key) == customTtl
    cache.get(key) == value

    def pollingConditions = new PollingConditions(timeout: customTtl.toMillis(), delay: 0.1)

    and: "The entry should expire after the customTtl"
    pollingConditions.eventually {
      cache.get(key) == null
    }
  }
}
