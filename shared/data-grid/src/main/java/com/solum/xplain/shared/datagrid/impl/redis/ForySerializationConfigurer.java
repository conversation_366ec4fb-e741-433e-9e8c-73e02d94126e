package com.solum.xplain.shared.datagrid.impl.redis;

import lombok.RequiredArgsConstructor;
import lombok.experimental.Delegate;
import org.apache.fory.ThreadSafeFory;
import org.jspecify.annotations.NullMarked;

public interface ForySerializationConfigurer {
  void configure(SerializationConfig config);

  /**
   * This is a facade which exposes only the methods that are needed for configuring Fory
   * serialization.
   */
  @SuppressWarnings("ClassCanBeRecord")
  @NullMarked
  @RequiredArgsConstructor
  class SerializationConfig {
    @Delegate(types = ForySerializationConfig.class)
    private final ThreadSafeFory fory;

    /** This interface is used to limit the methods that are exposed from the Fory delegate. */
    private interface ForySerializationConfig {
      @SuppressWarnings("unused")
      void register(Class<?> type);
    }
  }
}
